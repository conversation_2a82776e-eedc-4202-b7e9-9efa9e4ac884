// qabstracteventdispatcher.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


// Qt v6.8 introduced QAbstractEventDispatcherV2 as a temporary class intended to
// aid porting to Qt7 (where all timers will be based on std::chrono).  We choose
// not to implement them as PyQt is likely to use Python ints anyway.

class QAbstractEventDispatcher : public QObject /Abstract/
{
%TypeHeaderCode
#include <qabstracteventdispatcher.h>
%End

public:
    struct TimerInfo
    {
%TypeHeaderCode
#include <qabstracteventdispatcher.h>
%End

        int timerId;
        int interval;
        Qt::TimerType timerType;
        TimerInfo(int id, int i, Qt::TimerType t);
    };

    explicit QAbstractEventDispatcher(QObject *parent /TransferThis/ = 0);
    virtual ~QAbstractEventDispatcher();
    static QAbstractEventDispatcher *instance(QThread *thread = 0);
    virtual bool processEvents(QEventLoop::ProcessEventsFlags flags) = 0 /ReleaseGIL/;
    int registerTimer(qint64 interval, Qt::TimerType timerType, QObject *object);
    virtual void registerTimer(int timerId, qint64 interval, Qt::TimerType timerType, QObject *object) = 0;
    virtual bool unregisterTimer(int timerId) = 0;
    virtual bool unregisterTimers(QObject *object) = 0;
    virtual QList<QAbstractEventDispatcher::TimerInfo> registeredTimers(QObject *object) const = 0;
    virtual void wakeUp() = 0;
    virtual void interrupt() = 0;
    virtual void startingUp();
    virtual void closingDown();
    virtual int remainingTime(int timerId) = 0;
    void installNativeEventFilter(QAbstractNativeEventFilter *filterObj);
    void removeNativeEventFilter(QAbstractNativeEventFilter *filterObj);
    bool filterNativeEvent(const QByteArray &eventType, void *message, qintptr *result /Out/);

signals:
    void aboutToBlock();
    void awake();
};
