// qvideoframeformat.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QVideoFrameFormat
{
%TypeHeaderCode
#include <qvideoframeformat.h>
%End

public:
    enum PixelFormat
    {
        Format_Invalid,
        Format_YUV420P,
        Format_YUV422P,
%If (Qt_6_4_0 -)
        Format_YUV420P10,
%End
        Format_YV12,
        Format_UYVY,
        Format_YUYV,
        Format_NV12,
        Format_NV21,
        Format_IMC1,
        Format_IMC2,
        Format_IMC3,
        Format_IMC4,
        Format_Y8,
        Format_Y16,
        Format_P010,
        Format_P016,
        Format_Jpeg,
        Format_SamplerExternalOES,
        Format_ARGB8888,
        Format_ARGB8888_Premultiplied,
        Format_XRGB8888,
        Format_BGRA8888,
        Format_BGRA8888_Premultiplied,
        Format_BGRX8888,
        Format_ABGR8888,
        Format_XBGR8888,
        Format_RGBA8888,
        Format_RGBX8888,
        Format_AYUV,
        Format_AYUV_Premultiplied,
        Format_SamplerRect,
    };

    enum Direction
    {
        TopToBottom,
        BottomToTop,
    };

    enum YCbCrColorSpace
    {
        YCbCr_Undefined,
        YCbCr_BT601,
        YCbCr_BT709,
        YCbCr_xvYCC601,
        YCbCr_xvYCC709,
        YCbCr_JPEG,
%If (Qt_6_3_0 -)
        YCbCr_BT2020,
%End
    };

    QVideoFrameFormat();
    QVideoFrameFormat(const QSize &size, QVideoFrameFormat::PixelFormat pixelFormat);
    QVideoFrameFormat(const QVideoFrameFormat &format);
    ~QVideoFrameFormat();
    bool operator==(const QVideoFrameFormat &format) const;
    bool operator!=(const QVideoFrameFormat &format) const;
    bool isValid() const;
    QVideoFrameFormat::PixelFormat pixelFormat() const;
    QSize frameSize() const;
    void setFrameSize(const QSize &size);
    void setFrameSize(int width, int height);
    int frameWidth() const;
    int frameHeight() const;
    int planeCount() const;
    QRect viewport() const;
    void setViewport(const QRect &viewport);
    QVideoFrameFormat::Direction scanLineDirection() const;
    void setScanLineDirection(QVideoFrameFormat::Direction direction);
    qreal frameRate() const;
    void setFrameRate(qreal rate);
    QVideoFrameFormat::YCbCrColorSpace yCbCrColorSpace() const;
    void setYCbCrColorSpace(QVideoFrameFormat::YCbCrColorSpace colorSpace);
    bool isMirrored() const;
    void setMirrored(bool mirrored);
    static QVideoFrameFormat::PixelFormat pixelFormatFromImageFormat(QImage::Format format);
    static QImage::Format imageFormatFromPixelFormat(QVideoFrameFormat::PixelFormat format);
%If (Qt_6_2_0 -)
    static QString pixelFormatToString(QVideoFrameFormat::PixelFormat pixelFormat);
%End
%If (Qt_6_4_0 -)

    enum ColorSpace
    {
        ColorSpace_Undefined,
        ColorSpace_BT601,
        ColorSpace_BT709,
        ColorSpace_AdobeRgb,
        ColorSpace_BT2020,
    };

%End
%If (Qt_6_4_0 -)

    enum ColorTransfer
    {
        ColorTransfer_Unknown,
        ColorTransfer_BT709,
        ColorTransfer_BT601,
        ColorTransfer_Linear,
        ColorTransfer_Gamma22,
        ColorTransfer_Gamma28,
        ColorTransfer_ST2084,
        ColorTransfer_STD_B67,
    };

%End
%If (Qt_6_4_0 -)

    enum ColorRange
    {
        ColorRange_Unknown,
        ColorRange_Video,
        ColorRange_Full,
    };

%End
%If (Qt_6_4_0 -)
    QVideoFrameFormat::ColorSpace colorSpace() const;
%End
%If (Qt_6_4_0 -)
    void setColorSpace(QVideoFrameFormat::ColorSpace colorSpace);
%End
%If (Qt_6_4_0 -)
    QVideoFrameFormat::ColorTransfer colorTransfer() const;
%End
%If (Qt_6_4_0 -)
    void setColorTransfer(QVideoFrameFormat::ColorTransfer colorTransfer);
%End
%If (Qt_6_4_0 -)
    QVideoFrameFormat::ColorRange colorRange() const;
%End
%If (Qt_6_4_0 -)
    void setColorRange(QVideoFrameFormat::ColorRange range);
%End
%If (Qt_6_8_0 -)
    qreal streamFrameRate() const;
%End
%If (Qt_6_8_0 -)
    void setStreamFrameRate(qreal rate);
%End
%If (Qt_6_8_0 -)
    QtVideo::Rotation rotation() const;
%End
%If (Qt_6_8_0 -)
    void setRotation(QtVideo::Rotation rotation);
%End
};

%End
