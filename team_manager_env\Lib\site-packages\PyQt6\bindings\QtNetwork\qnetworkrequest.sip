// qnetworkrequest.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LIC<PERSON><PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QNetworkRequest
{
%TypeHeaderCode
#include <qnetworkrequest.h>
%End

public:
    enum KnownHeaders
    {
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        ContentD<PERSON><PERSON><PERSON>eader,
        <PERSON><PERSON><PERSON><PERSON><PERSON>eader,
        ServerHeader,
        IfModifiedSinceHeader,
        ETagHeader,
        IfMatchHeader,
        IfNoneMatchHeader,
    };

    enum Attribute
    {
        HttpStatusCodeAttribute,
        HttpReasonPhraseAttribute,
        RedirectionTargetAttribute,
        ConnectionEncryptedAttribute,
        CacheLoadControlAttribute,
        CacheSaveControlAttribute,
        SourceIsFromCacheAttribute,
        DoNotBufferUploadDataAttribute,
        HttpPipeliningAllowedAttribute,
        HttpPipeliningWasUsedAttribute,
        CustomVerbAttribute,
        CookieLoadControlAttribute,
        AuthenticationReuseAttribute,
        CookieSaveControlAttribute,
        BackgroundRequestAttribute,
        EmitAllUploadProgressSignalsAttribute,
        Http2AllowedAttribute,
        Http2WasUsedAttribute,
        OriginalContentLengthAttribute,
        RedirectPolicyAttribute,
        Http2DirectAttribute,
        AutoDeleteReplyOnFinishAttribute,
%If (Qt_6_3_0 -)
        ConnectionCacheExpiryTimeoutSecondsAttribute,
%End
%If (Qt_6_3_0 -)
        Http2CleartextAllowedAttribute,
%End
%If (Qt_6_5_0 -)
        UseCredentialsAttribute,
%End
%If (Qt_6_8_0 -)
        FullLocalServerNameAttribute,
%End
        User,
        UserMax,
    };

    enum CacheLoadControl
    {
        AlwaysNetwork,
        PreferNetwork,
        PreferCache,
        AlwaysCache,
    };

    enum LoadControl
    {
        Automatic,
        Manual,
    };

    enum Priority
    {
        HighPriority,
        NormalPriority,
        LowPriority,
    };

    explicit QNetworkRequest(const QUrl &url);
    QNetworkRequest();
    QNetworkRequest(const QNetworkRequest &other);
    ~QNetworkRequest();
    QUrl url() const;
    void setUrl(const QUrl &url);
    QVariant header(QNetworkRequest::KnownHeaders header) const;
    void setHeader(QNetworkRequest::KnownHeaders header, const QVariant &value);
%If (Qt_6_7_0 -)
    bool hasRawHeader(QAnyStringView headerName) const;
%End
%If (- Qt_6_7_0)
    bool hasRawHeader(const QByteArray &headerName) const;
%End
    QList<QByteArray> rawHeaderList() const;
%If (Qt_6_7_0 -)
    QByteArray rawHeader(QAnyStringView headerName) const;
%End
%If (- Qt_6_7_0)
    QByteArray rawHeader(const QByteArray &headerName) const;
%End
    void setRawHeader(const QByteArray &headerName, const QByteArray &value);
    QVariant attribute(QNetworkRequest::Attribute code, const QVariant &defaultValue = QVariant()) const;
    void setAttribute(QNetworkRequest::Attribute code, const QVariant &value);
%If (PyQt_SSL)
    QSslConfiguration sslConfiguration() const;
%End
%If (PyQt_SSL)
    void setSslConfiguration(const QSslConfiguration &configuration);
%End
    bool operator==(const QNetworkRequest &other) const;
    bool operator!=(const QNetworkRequest &other) const;
    void setOriginatingObject(QObject *object /KeepReference/);
    QObject *originatingObject() const;
    QNetworkRequest::Priority priority() const;
    void setPriority(QNetworkRequest::Priority priority);
    void swap(QNetworkRequest &other /Constrained/);
    int maximumRedirectsAllowed() const;
    void setMaximumRedirectsAllowed(int maximumRedirectsAllowed);

    enum RedirectPolicy
    {
        ManualRedirectPolicy,
        NoLessSafeRedirectPolicy,
        SameOriginRedirectPolicy,
        UserVerifiedRedirectPolicy,
    };

    QString peerVerifyName() const;
    void setPeerVerifyName(const QString &peerName);
%If (Qt_6_5_0 -)
    QHttp1Configuration http1Configuration() const;
%End
    QHttp2Configuration http2Configuration() const;
%If (Qt_6_5_0 -)
    void setHttp1Configuration(const QHttp1Configuration &configuration);
%End
    void setHttp2Configuration(const QHttp2Configuration &configuration);

    enum TransferTimeoutConstant
    {
        DefaultTransferTimeoutConstant,
    };

    int transferTimeout() const;
    // In Qt v6.7 this was replaced by two overloads but we need the optional keyword argument.
    void setTransferTimeout(int timeout = QNetworkRequest::DefaultTransferTimeoutConstant);
%If (Qt_6_2_0 -)
    qint64 decompressedSafetyCheckThreshold() const;
%End
%If (Qt_6_2_0 -)
    void setDecompressedSafetyCheckThreshold(qint64 threshold);
%End
%If (Qt_6_8_0 -)
    QHttpHeaders headers() const;
%End
%If (Qt_6_8_0 -)
    void setHeaders(const QHttpHeaders &newHeaders);
%End
};
