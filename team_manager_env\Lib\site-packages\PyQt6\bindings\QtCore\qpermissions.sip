// qpermissions.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_5_0 -)
%If (PyQt_Permissions)

class QPermission /TypeHintIn="Union[QBluetoothPermission, QCalendarPermission, QCameraPermission, QContactsPermission, QLocationPermission, QMicrophonePermission]"/
{
%TypeHeaderCode
#include <qpermissions.h>
%End

%ConvertToTypeCode
// Note that we don't allow sub-classes of the typed permissions.
const sipTypeDef *td = sipTypeFromPyTypeObject(Py_TYPE(sipPy));

if (!sipIsErr)
    return (td == sipType_QBluetoothPermission ||
            td == sipType_QCalendarPermission ||
            td == sipType_QCameraPermission ||
            td == sipType_QContactsPermission ||
            td == sipType_QLocationPermission ||
            td == sipType_QMicrophonePermission);

void *cpp = sipConvertToType(sipPy, td, sipTransferObj, SIP_NOT_NONE, NULL, sipIsErr);

if (*sipIsErr)
    return 0;

if (td == sipType_QBluetoothPermission)
    *sipCppPtr = new QPermission(*reinterpret_cast<QBluetoothPermission *>(cpp));

else if (td == sipType_QCalendarPermission)
    *sipCppPtr = new QPermission(*reinterpret_cast<QCalendarPermission *>(cpp));

else if (td == sipType_QCameraPermission)
    *sipCppPtr = new QPermission(*reinterpret_cast<QCameraPermission *>(cpp));

else if (td == sipType_QContactsPermission)
    *sipCppPtr = new QPermission(*reinterpret_cast<QContactsPermission *>(cpp));

else if (td == sipType_QLocationPermission)
    *sipCppPtr = new QPermission(*reinterpret_cast<QLocationPermission *>(cpp));

else if (td == sipType_QMicrophonePermission)
    *sipCppPtr = new QPermission(*reinterpret_cast<QMicrophonePermission *>(cpp));

return sipGetState(sipTransferObj);
%End

public:
    QPermission();
    Qt::PermissionStatus status() const;
    QMetaType type() const;
    SIP_PYOBJECT value() const;
%MethodCode
        const sipTypeDef *td = SIP_NULLPTR;
        void *perm = SIP_NULLPTR;
        QMetaType mt = sipCpp->type();
        
        if (mt == QMetaType::fromType<QBluetoothPermission>())
        {
            std::optional<QBluetoothPermission> opt_perm = sipCpp->value<QBluetoothPermission>();
            
            if (opt_perm)
            {
                perm = new QBluetoothPermission(opt_perm.value());
                td = sipType_QBluetoothPermission;
            }
        }
        else if (mt == QMetaType::fromType<QCalendarPermission>())
        {
            std::optional<QCalendarPermission> opt_perm = sipCpp->value<QCalendarPermission>();
            
            if (opt_perm)
            {
                perm = new QCalendarPermission(opt_perm.value());
                td = sipType_QCalendarPermission;
            }
        }
        else if (mt == QMetaType::fromType<QCameraPermission>())
        {
            std::optional<QCameraPermission> opt_perm = sipCpp->value<QCameraPermission>();
            
            if (opt_perm)
            {
                perm = new QCameraPermission(opt_perm.value());
                td = sipType_QCameraPermission;
            }
        }
        else if (mt == QMetaType::fromType<QContactsPermission>())
        {
            std::optional<QContactsPermission> opt_perm = sipCpp->value<QContactsPermission>();
            
            if (opt_perm)
            {
                perm = new QContactsPermission(opt_perm.value());
                td = sipType_QContactsPermission;
            }
        }
        else if (mt == QMetaType::fromType<QLocationPermission>())
        {
            std::optional<QLocationPermission> opt_perm = sipCpp->value<QLocationPermission>();
            
            if (opt_perm)
            {
                perm = new QLocationPermission(opt_perm.value());
                td = sipType_QLocationPermission;
            }
        }
        else if (mt == QMetaType::fromType<QMicrophonePermission>())
        {
            std::optional<QMicrophonePermission> opt_perm = sipCpp->value<QMicrophonePermission>();
            
            if (opt_perm)
            {
                perm = new QMicrophonePermission(opt_perm.value());
                td = sipType_QMicrophonePermission;
            }
        }
        
        if (perm)
        {
            sipRes = sipConvertFromNewType(perm, td, SIP_NULLPTR);
        }
        else
        {
            sipRes = Py_None;
            Py_INCREF(sipRes);
        }
%End
};

%End
%End
%If (Qt_6_5_0 -)
%If (PyQt_Permissions)

class QLocationPermission
{
%TypeHeaderCode
#include <qpermissions.h>
%End

public:
    QLocationPermission();
    QLocationPermission(const QLocationPermission &other);
    ~QLocationPermission();

    enum Accuracy
    {
        Approximate,
        Precise,
    };

    void setAccuracy(QLocationPermission::Accuracy accuracy);
    QLocationPermission::Accuracy accuracy() const;

    enum Availability
    {
        WhenInUse,
        Always,
    };

    void setAvailability(QLocationPermission::Availability availability);
    QLocationPermission::Availability availability() const;
};

%End
%End
%If (Qt_6_5_0 -)
%If (PyQt_Permissions)

class QCalendarPermission
{
%TypeHeaderCode
#include <qpermissions.h>
%End

public:
    QCalendarPermission();
    QCalendarPermission(const QCalendarPermission &other);
    ~QCalendarPermission();

    enum AccessMode
    {
        ReadOnly,
        ReadWrite,
    };

    void setAccessMode(QCalendarPermission::AccessMode mode);
    QCalendarPermission::AccessMode accessMode() const;
};

%End
%End
%If (Qt_6_5_0 -)
%If (PyQt_Permissions)

class QContactsPermission
{
%TypeHeaderCode
#include <qpermissions.h>
%End

public:
    QContactsPermission();
    QContactsPermission(const QContactsPermission &other);
    ~QContactsPermission();

    enum AccessMode
    {
        ReadOnly,
        ReadWrite,
    };

    void setAccessMode(QContactsPermission::AccessMode mode);
    QContactsPermission::AccessMode accessMode() const;
};

%End
%End
%If (Qt_6_5_0 -)
%If (PyQt_Permissions)

class QCameraPermission
{
%TypeHeaderCode
#include <qpermissions.h>
%End

public:
    QCameraPermission();
    QCameraPermission(const QCameraPermission &other);
    ~QCameraPermission();
};

%End
%End
%If (Qt_6_5_0 -)
%If (PyQt_Permissions)

class QMicrophonePermission
{
%TypeHeaderCode
#include <qpermissions.h>
%End

public:
    QMicrophonePermission();
    QMicrophonePermission(const QMicrophonePermission &other);
    ~QMicrophonePermission();
};

%End
%End
%If (Qt_6_5_0 -)
%If (PyQt_Permissions)

class QBluetoothPermission
{
%TypeHeaderCode
#include <qpermissions.h>
%End

public:
    QBluetoothPermission();
    QBluetoothPermission(const QBluetoothPermission &other);
    ~QBluetoothPermission();
%If (Qt_6_6_0 -)

    enum CommunicationMode
    {
        Access,
        Advertise,
        Default,
    };

%End
%If (Qt_6_6_0 -)
    typedef QFlags<QBluetoothPermission::CommunicationMode> CommunicationModes;
%End
%If (Qt_6_6_0 -)
    void setCommunicationModes(QBluetoothPermission::CommunicationModes modes);
%End
%If (Qt_6_6_0 -)
    QBluetoothPermission::CommunicationModes communicationModes() const;
%End
};

%End
%End
