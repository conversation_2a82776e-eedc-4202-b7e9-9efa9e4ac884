import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickfusionstyle_p.h"
        name: "QQuickFusionStyle"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.Controls.Fusion/Fusion 2.3",
            "QtQuick.Controls.Fusion/Fusion 6.0"
        ]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [515, 1536]
        Property {
            name: "lightShade"
            type: "QColor"
            read: "lightShade"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "darkShade"
            type: "QColor"
            read: "darkShade"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "topShadow"
            type: "QColor"
            read: "topShadow"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "innerContrastLine"
            type: "QColor"
            read: "innerContrastLine"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Method {
            name: "highlight"
            type: "QColor"
            Parameter { name: "palette"; type: "QQuickPalette"; isPointer: true }
        }
        Method {
            name: "highlightedText"
            type: "QColor"
            Parameter { name: "palette"; type: "QQuickPalette"; isPointer: true }
        }
        Method {
            name: "outline"
            type: "QColor"
            Parameter { name: "palette"; type: "QQuickPalette"; isPointer: true }
        }
        Method {
            name: "highlightedOutline"
            type: "QColor"
            Parameter { name: "palette"; type: "QQuickPalette"; isPointer: true }
        }
        Method {
            name: "tabFrameColor"
            type: "QColor"
            Parameter { name: "palette"; type: "QQuickPalette"; isPointer: true }
        }
        Method {
            name: "buttonColor"
            type: "QColor"
            Parameter { name: "palette"; type: "QQuickPalette"; isPointer: true }
            Parameter { name: "highlighted"; type: "bool" }
            Parameter { name: "down"; type: "bool" }
            Parameter { name: "hovered"; type: "bool" }
        }
        Method {
            name: "buttonColor"
            type: "QColor"
            isCloned: true
            Parameter { name: "palette"; type: "QQuickPalette"; isPointer: true }
            Parameter { name: "highlighted"; type: "bool" }
            Parameter { name: "down"; type: "bool" }
        }
        Method {
            name: "buttonColor"
            type: "QColor"
            isCloned: true
            Parameter { name: "palette"; type: "QQuickPalette"; isPointer: true }
            Parameter { name: "highlighted"; type: "bool" }
        }
        Method {
            name: "buttonColor"
            type: "QColor"
            isCloned: true
            Parameter { name: "palette"; type: "QQuickPalette"; isPointer: true }
        }
        Method {
            name: "buttonOutline"
            type: "QColor"
            Parameter { name: "palette"; type: "QQuickPalette"; isPointer: true }
            Parameter { name: "highlighted"; type: "bool" }
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "buttonOutline"
            type: "QColor"
            isCloned: true
            Parameter { name: "palette"; type: "QQuickPalette"; isPointer: true }
            Parameter { name: "highlighted"; type: "bool" }
        }
        Method {
            name: "buttonOutline"
            type: "QColor"
            isCloned: true
            Parameter { name: "palette"; type: "QQuickPalette"; isPointer: true }
        }
        Method {
            name: "gradientStart"
            type: "QColor"
            Parameter { name: "baseColor"; type: "QColor" }
        }
        Method {
            name: "gradientStop"
            type: "QColor"
            Parameter { name: "baseColor"; type: "QColor" }
        }
        Method {
            name: "mergedColors"
            type: "QColor"
            Parameter { name: "colorA"; type: "QColor" }
            Parameter { name: "colorB"; type: "QColor" }
            Parameter { name: "factor"; type: "int" }
        }
        Method {
            name: "mergedColors"
            type: "QColor"
            isCloned: true
            Parameter { name: "colorA"; type: "QColor" }
            Parameter { name: "colorB"; type: "QColor" }
        }
        Method {
            name: "grooveColor"
            type: "QColor"
            Parameter { name: "palette"; type: "QQuickPalette"; isPointer: true }
        }
    }
}
