// qmdiarea.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMdiArea : public QAbstractScrollArea
{
%TypeHeaderCode
#include <qmdiarea.h>
%End

public:
    enum AreaOption /BaseType=Flag/
    {
        DontMaximizeSubWindowOnActivation,
    };

    typedef QFlags<QMdiArea::AreaOption> AreaOptions;

    enum ViewMode
    {
        SubWindowView,
        TabbedView,
    };

    enum WindowOrder
    {
        CreationOrder,
        StackingOrder,
        ActivationHistoryOrder,
    };

    QMdiArea(QWidget *parent /TransferThis/ = 0);
    virtual ~QMdiArea();
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    QMdiSubWindow *activeSubWindow() const;
    QMdiSubWindow *addSubWindow(QWidget *widget /Transfer/, Qt::WindowFlags flags = Qt::WindowFlags());
    QList<QMdiSubWindow *> subWindowList(QMdiArea::WindowOrder order = QMdiArea::CreationOrder) const;
    QMdiSubWindow *currentSubWindow() const;
    void removeSubWindow(QWidget *widget /GetWrapper/);
%MethodCode
        // We need to implement /TransferBack/ on the argument, but it might be the
        // QMdiSubWindow that wraps the widget we are really after.
        QMdiSubWindow *swin = qobject_cast<QMdiSubWindow *>(a0);
        
        if (swin)
        {
            QWidget *w = swin->widget();
        
            a0Wrapper = (w ? sipGetPyObject(w, sipType_QWidget) : 0);
        }
        else
            a0Wrapper = 0;
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->removeSubWindow(a0);
        Py_END_ALLOW_THREADS
        
        if (a0Wrapper)
            sipTransferBack(a0Wrapper);
%End

    QBrush background() const;
    void setBackground(const QBrush &background);
    void setOption(QMdiArea::AreaOption option, bool on = true);
    bool testOption(QMdiArea::AreaOption opton) const;

signals:
    void subWindowActivated(QMdiSubWindow *);

public slots:
    void setActiveSubWindow(QMdiSubWindow *window);
    void tileSubWindows();
    void cascadeSubWindows();
    void closeActiveSubWindow();
    void closeAllSubWindows();
    void activateNextSubWindow();
    void activatePreviousSubWindow();

protected:
    virtual void setupViewport(QWidget *viewport);
    virtual bool event(QEvent *event);
    virtual bool eventFilter(QObject *object, QEvent *event);
    virtual void paintEvent(QPaintEvent *paintEvent);
    virtual void childEvent(QChildEvent *childEvent);
    virtual void resizeEvent(QResizeEvent *resizeEvent);
    virtual void timerEvent(QTimerEvent *timerEvent);
    virtual void showEvent(QShowEvent *showEvent);
    virtual bool viewportEvent(QEvent *event);
    virtual void scrollContentsBy(int dx, int dy);

public:
    QMdiArea::WindowOrder activationOrder() const;
    void setActivationOrder(QMdiArea::WindowOrder order);
    void setViewMode(QMdiArea::ViewMode mode);
    QMdiArea::ViewMode viewMode() const;
    void setTabShape(QTabWidget::TabShape shape);
    QTabWidget::TabShape tabShape() const;
    void setTabPosition(QTabWidget::TabPosition position);
    QTabWidget::TabPosition tabPosition() const;
    bool documentMode() const;
    void setDocumentMode(bool enabled);
    void setTabsClosable(bool closable);
    bool tabsClosable() const;
    void setTabsMovable(bool movable);
    bool tabsMovable() const;
};
