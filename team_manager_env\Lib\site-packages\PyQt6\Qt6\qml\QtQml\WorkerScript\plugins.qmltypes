import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickworkerscript_p.h"
        name: "QQuickWorkerScript"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQml.WorkerScript/WorkerScript 2.0",
            "QtQml.WorkerScript/WorkerScript 2.15",
            "QtQml.WorkerScript/WorkerScript 6.0"
        ]
        exportMetaObjectRevisions: [512, 527, 1536]
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "ready"
            revision: 527
            type: "bool"
            read: "ready"
            notify: "readyChanged"
            index: 1
            isReadonly: true
        }
        Signal { name: "sourceChanged" }
        Signal { name: "readyChanged"; revision: 527 }
        Signal {
            name: "message"
            Parameter { name: "messageObject"; type: "QJSValue" }
        }
        Method { name: "sendMessage"; isJavaScriptFunction: true }
    }
}
