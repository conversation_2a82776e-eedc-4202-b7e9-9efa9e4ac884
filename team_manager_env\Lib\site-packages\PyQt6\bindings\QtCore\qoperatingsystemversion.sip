// qoperatingsystemversion.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_3_0 -)

class QOperatingSystemVersionBase
{
%TypeHeaderCode
#include <qoperatingsystemversion.h>
%End

public:
    QVersionNumber version() const;
    int majorVersion() const;
    int minorVersion() const;
    int microVersion() const;
    int segmentCount() const;
    QString name() const;

protected:
    QOperatingSystemVersionBase();
};

%End
%If (Qt_6_3_0 -)

class QOperatingSystemVersion : public QOperatingSystemVersionBase
{
%TypeHeaderCode
#include <qoperatingsystemversion.h>
%End

public:
    enum OSType
    {
        Unknown,
        Windows,
        MacOS,
        IOS,
        TvOS,
        WatchOS,
        Android,
    };

    static const QOperatingSystemVersion Windows7;
    static const QOperatingSystemVersion Windows8;
    static const QOperatingSystemVersion Windows8_1;
    static const QOperatingSystemVersion Windows10;
    static const QOperatingSystemVersion OSXMavericks;
    static const QOperatingSystemVersion OSXYosemite;
    static const QOperatingSystemVersion OSXElCapitan;
    static const QOperatingSystemVersion MacOSSierra;
    static const QOperatingSystemVersion MacOSHighSierra;
    static const QOperatingSystemVersion MacOSMojave;
    static const QOperatingSystemVersion MacOSCatalina;
    static const QOperatingSystemVersion MacOSBigSur;
    static const QOperatingSystemVersion MacOSMonterey;
%If (Qt_6_5_0 -)
    static const QOperatingSystemVersionBase MacOSVentura;
%End
%If (Qt_6_6_0 -)
    static const QOperatingSystemVersionBase MacOSSonoma;
%End
    static const QOperatingSystemVersion AndroidJellyBean;
    static const QOperatingSystemVersion AndroidJellyBean_MR1;
    static const QOperatingSystemVersion AndroidJellyBean_MR2;
    static const QOperatingSystemVersion AndroidKitKat;
    static const QOperatingSystemVersion AndroidLollipop;
    static const QOperatingSystemVersion AndroidLollipop_MR1;
    static const QOperatingSystemVersion AndroidMarshmallow;
    static const QOperatingSystemVersion AndroidNougat;
    static const QOperatingSystemVersion AndroidNougat_MR1;
    static const QOperatingSystemVersion AndroidOreo;
    static const QOperatingSystemVersion AndroidOreo_MR1;
    static const QOperatingSystemVersion AndroidPie;
    static const QOperatingSystemVersion Android10;
    static const QOperatingSystemVersion Android11;
%If (Qt_6_5_0 -)
    static const QOperatingSystemVersionBase Android12;
%End
%If (Qt_6_5_0 -)
    static const QOperatingSystemVersionBase Android12L;
%End
%If (Qt_6_5_0 -)
    static const QOperatingSystemVersionBase Android13;
%End
    static const QOperatingSystemVersionBase Windows10_1809;
    static const QOperatingSystemVersionBase Windows10_1903;
    static const QOperatingSystemVersionBase Windows10_1909;
    static const QOperatingSystemVersionBase Windows10_2004;
    static const QOperatingSystemVersionBase Windows10_20H2;
    static const QOperatingSystemVersionBase Windows10_21H1;
    static const QOperatingSystemVersionBase Windows10_21H2;
%If (Qt_6_5_0 -)
    static const QOperatingSystemVersionBase Windows10_22H2;
%End
    static const QOperatingSystemVersionBase Windows11;
%If (Qt_6_4_0 -)
    static const QOperatingSystemVersionBase Windows11_21H2;
%End
%If (Qt_6_4_0 -)
    static const QOperatingSystemVersionBase Windows11_22H2;
%End
    QOperatingSystemVersion(QOperatingSystemVersion::OSType osType, int vmajor, int vminor = -1, int vmicro = -1);
    static QOperatingSystemVersion current();
    static QOperatingSystemVersion::OSType currentType();
    QOperatingSystemVersion::OSType type() const;

private:
    QOperatingSystemVersion();
};

%End
%If (- Qt_6_3_0)

class QOperatingSystemVersion
{
%TypeHeaderCode
#include <qoperatingsystemversion.h>
%End

public:
    enum OSType
    {
        Unknown,
        Windows,
        MacOS,
        IOS,
        TvOS,
        WatchOS,
        Android,
    };

    static const QOperatingSystemVersion Windows7;
    static const QOperatingSystemVersion Windows8;
    static const QOperatingSystemVersion Windows8_1;
    static const QOperatingSystemVersion Windows10;
    static const QOperatingSystemVersion OSXMavericks;
    static const QOperatingSystemVersion OSXYosemite;
    static const QOperatingSystemVersion OSXElCapitan;
    static const QOperatingSystemVersion MacOSSierra;
    static const QOperatingSystemVersion MacOSHighSierra;
    static const QOperatingSystemVersion MacOSMojave;
    static const QOperatingSystemVersion MacOSCatalina;
    static const QOperatingSystemVersion MacOSBigSur;
    static const QOperatingSystemVersion AndroidJellyBean;
    static const QOperatingSystemVersion AndroidJellyBean_MR1;
    static const QOperatingSystemVersion AndroidJellyBean_MR2;
    static const QOperatingSystemVersion AndroidKitKat;
    static const QOperatingSystemVersion AndroidLollipop;
    static const QOperatingSystemVersion AndroidLollipop_MR1;
    static const QOperatingSystemVersion AndroidMarshmallow;
    static const QOperatingSystemVersion AndroidNougat;
    static const QOperatingSystemVersion AndroidNougat_MR1;
    static const QOperatingSystemVersion AndroidOreo;
%If (Qt_6_1_0 -)
    static const QOperatingSystemVersion AndroidOreo_MR1;
%End
%If (Qt_6_1_0 -)
    static const QOperatingSystemVersion AndroidPie;
%End
%If (Qt_6_1_0 -)
    static const QOperatingSystemVersion Android11;
%End
%If (Qt_6_1_0 -)
    static const QOperatingSystemVersion Android10;
%End
    QOperatingSystemVersion(QOperatingSystemVersion::OSType osType, int vmajor, int vminor = -1, int vmicro = -1);
    static QOperatingSystemVersion current();
    static QOperatingSystemVersion::OSType currentType();
%If (Qt_6_1_0 -)
    QVersionNumber version() const;
%End
    int majorVersion() const;
    int minorVersion() const;
    int microVersion() const;
    int segmentCount() const;
    QOperatingSystemVersion::OSType type() const;
    QString name() const;

private:
    QOperatingSystemVersion();
};

%End
%If (Qt_6_3_0 -)
bool operator>(QOperatingSystemVersionBase lhs, QOperatingSystemVersionBase rhs);
%End
%If (- Qt_6_3_0)
bool operator>(const QOperatingSystemVersion &lhs, const QOperatingSystemVersion &rhs);
%End
%If (Qt_6_3_0 -)
bool operator>=(QOperatingSystemVersionBase lhs, QOperatingSystemVersionBase rhs);
%End
%If (- Qt_6_3_0)
bool operator>=(const QOperatingSystemVersion &lhs, const QOperatingSystemVersion &rhs);
%End
%If (Qt_6_3_0 -)
bool operator<(QOperatingSystemVersionBase lhs, QOperatingSystemVersionBase rhs);
%End
%If (- Qt_6_3_0)
bool operator<(const QOperatingSystemVersion &lhs, const QOperatingSystemVersion &rhs);
%End
%If (Qt_6_3_0 -)
bool operator<=(QOperatingSystemVersionBase lhs, QOperatingSystemVersionBase rhs);
%End
%If (- Qt_6_3_0)
bool operator<=(const QOperatingSystemVersion &lhs, const QOperatingSystemVersion &rhs);
%End
