// qopenglpaintdevice.sip generated by MetaSIP
//
// This file is part of the QtOpenGL Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QOpenGLPaintDevice : public QPaintDevice
{
%TypeHeaderCode
#include <qopenglpaintdevice.h>
%End

public:
    QOpenGLPaintDevice();
    explicit QOpenGLPaintDevice(const QSize &size);
    QOpenGLPaintDevice(int width, int height);
    virtual ~QOpenGLPaintDevice();
    virtual QPaintEngine *paintEngine() const;
    QOpenGLContext *context() const;
    QSize size() const;
    void setSize(const QSize &size);
    qreal dotsPerMeterX() const;
    qreal dotsPerMeterY() const;
    void setDotsPerMeterX(qreal);
    void setDotsPerMeterY(qreal);
    void setPaintFlipped(bool flipped);
    bool paintFlipped() const;
    virtual void ensureActiveTarget();
    void setDevicePixelRatio(qreal devicePixelRatio);

protected:
    virtual int metric(QPaintDevice::PaintDeviceMetric metric) const;
};
