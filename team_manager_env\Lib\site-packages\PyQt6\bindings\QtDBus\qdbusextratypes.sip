// qdbusextratypes.sip generated by MetaSIP
//
// This file is part of the QtDBus Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDBusObjectPath
{
%TypeHeaderCode
#include <qdbusextratypes.h>
%End

public:
    QDBusObjectPath();
    explicit QDBusObjectPath(const QString &objectPath);
    QString path() const;
    void setPath(const QString &objectPath);
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp, 0);
%End

    void swap(QDBusObjectPath &other /Constrained/);
};

bool operator==(const QDBusObjectPath &lhs, const QDBusObjectPath &rhs);
bool operator!=(const QDBusObjectPath &lhs, const QDBusObjectPath &rhs);
bool operator<(const QDBusObjectPath &lhs, const QDBusObjectPath &rhs);

class QDBusSignature
{
%TypeHeaderCode
#include <qdbusextratypes.h>
%End

public:
    QDBusSignature();
    explicit QDBusSignature(const QString &dBusSignature);
    QString signature() const;
    void setSignature(const QString &dBusSignature);
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp, 0);
%End

    void swap(QDBusSignature &other /Constrained/);
};

bool operator==(const QDBusSignature &lhs, const QDBusSignature &rhs);
bool operator!=(const QDBusSignature &lhs, const QDBusSignature &rhs);
bool operator<(const QDBusSignature &lhs, const QDBusSignature &rhs);

class QDBusVariant
{
%TypeHeaderCode
#include <qdbusextratypes.h>
%End

public:
    QDBusVariant();
    explicit QDBusVariant(const QVariant &dBusVariant);
    QVariant variant() const;
    void setVariant(const QVariant &dBusVariant);
    void swap(QDBusVariant &other /Constrained/);
};

bool operator==(const QDBusVariant &v1, const QDBusVariant &v2);
