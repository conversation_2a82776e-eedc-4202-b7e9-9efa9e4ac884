// qlocalserver.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLocalServer : public QObject
{
%TypeHeaderCode
#include <qlocalserver.h>
%End

public:
    explicit QLocalServer(QObject *parent /TransferThis/ = 0);
    virtual ~QLocalServer();
    void close();
    QString errorString() const;
    virtual bool hasPendingConnections() const;
    bool isListening() const;
    bool listen(const QString &name);
    bool listen(qintptr socketDescriptor);
    int maxPendingConnections() const;
    virtual QLocalSocket *nextPendingConnection();
    QString serverName() const;
    QString fullServerName() const;
    QAbstractSocket::SocketError serverError() const;
    void setMaxPendingConnections(int numConnections);
    bool waitForNewConnection(int msecs = 0, bool *timedOut = 0) /ReleaseGIL/;
    static bool removeServer(const QString &name);

signals:
    void newConnection();

protected:
    virtual void incomingConnection(quintptr socketDescriptor);
%If (Qt_6_8_0 -)
    void addPendingConnection(QLocalSocket *socket);
%End

public:
    enum SocketOption /BaseType=Flag/
    {
        UserAccessOption,
        GroupAccessOption,
        OtherAccessOption,
        WorldAccessOption,
%If (Qt_6_2_0 -)
        AbstractNamespaceOption,
%End
    };

    typedef QFlags<QLocalServer::SocketOption> SocketOptions;
    void setSocketOptions(QLocalServer::SocketOptions options);
    QLocalServer::SocketOptions socketOptions() const;
    qintptr socketDescriptor() const;
%If (Qt_6_3_0 -)
    void setListenBacklogSize(int size);
%End
%If (Qt_6_3_0 -)
    int listenBacklogSize() const;
%End
};
