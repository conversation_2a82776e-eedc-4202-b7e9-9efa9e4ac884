# The PEP 484 type hints stub file for the QtOpenGL module.
#
# Generated by SIP 6.12.0
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt6.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICEN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import collections, re, typing, enum

try:
    from warnings import deprecated
except ImportError:
    pass

import PyQt6.sip

from PyQt6 import QtCore
from PyQt6 import QtGui

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[collections.abc.Callable[..., Any], QtCore.pyqtBoundSignal]

# Convenient aliases for complicated OpenGL types.
PYQT_OPENGL_ARRAY = typing.Union[typing.Sequence[int], typing.Sequence[float],
        PyQt6.sip.Buffer, None]
PYQT_OPENGL_BOUND_ARRAY = typing.Union[typing.Sequence[int],
        typing.Sequence[float], PyQt6.sip.Buffer, int, None]
PYQT_SHADER_ATTRIBUTE_ARRAY = typing.Union[typing.Sequence[QtGui.QVector2D],
        typing.Sequence[QtGui.QVector3D], typing.Sequence[QtGui.QVector4D],
        typing.Sequence[typing.Sequence[float]]]
PYQT_SHADER_UNIFORM_VALUE_ARRAY = typing.Union[
        typing.Sequence[QtGui.QVector2D], typing.Sequence[QtGui.QVector3D],
        typing.Sequence[QtGui.QVector4D], typing.Sequence[QtGui.QMatrix2x2],
        typing.Sequence[QtGui.QMatrix2x3], typing.Sequence[QtGui.QMatrix2x4],
        typing.Sequence[QtGui.QMatrix3x2], typing.Sequence[QtGui.QMatrix3x3],
        typing.Sequence[QtGui.QMatrix3x4], typing.Sequence[QtGui.QMatrix4x2],
        typing.Sequence[QtGui.QMatrix4x3], typing.Sequence[QtGui.QMatrix4x4],
        typing.Sequence[typing.Sequence[float]]]


class QOpenGLBuffer(PyQt6.sip.simplewrapper):

    class RangeAccessFlag(enum.Flag):
        RangeRead = ... # type: QOpenGLBuffer.RangeAccessFlag
        RangeWrite = ... # type: QOpenGLBuffer.RangeAccessFlag
        RangeInvalidate = ... # type: QOpenGLBuffer.RangeAccessFlag
        RangeInvalidateBuffer = ... # type: QOpenGLBuffer.RangeAccessFlag
        RangeFlushExplicit = ... # type: QOpenGLBuffer.RangeAccessFlag
        RangeUnsynchronized = ... # type: QOpenGLBuffer.RangeAccessFlag

    class Access(enum.Enum):
        ReadOnly = ... # type: QOpenGLBuffer.Access
        WriteOnly = ... # type: QOpenGLBuffer.Access
        ReadWrite = ... # type: QOpenGLBuffer.Access

    class UsagePattern(enum.Enum):
        StreamDraw = ... # type: QOpenGLBuffer.UsagePattern
        StreamRead = ... # type: QOpenGLBuffer.UsagePattern
        StreamCopy = ... # type: QOpenGLBuffer.UsagePattern
        StaticDraw = ... # type: QOpenGLBuffer.UsagePattern
        StaticRead = ... # type: QOpenGLBuffer.UsagePattern
        StaticCopy = ... # type: QOpenGLBuffer.UsagePattern
        DynamicDraw = ... # type: QOpenGLBuffer.UsagePattern
        DynamicRead = ... # type: QOpenGLBuffer.UsagePattern
        DynamicCopy = ... # type: QOpenGLBuffer.UsagePattern

    class Type(enum.Enum):
        VertexBuffer = ... # type: QOpenGLBuffer.Type
        IndexBuffer = ... # type: QOpenGLBuffer.Type
        PixelPackBuffer = ... # type: QOpenGLBuffer.Type
        PixelUnpackBuffer = ... # type: QOpenGLBuffer.Type

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, type: 'QOpenGLBuffer.Type') -> None: ...
    @typing.overload
    def __init__(self, other: 'QOpenGLBuffer') -> None: ...

    def swap(self, other: 'QOpenGLBuffer') -> None: ...
    def mapRange(self, offset: int, count: int, access: 'QOpenGLBuffer.RangeAccessFlag') -> typing.Optional[PyQt6.sip.voidptr]: ...
    def unmap(self) -> bool: ...
    def map(self, access: 'QOpenGLBuffer.Access') -> typing.Optional[PyQt6.sip.voidptr]: ...
    @typing.overload
    def allocate(self, data: typing.Optional[PyQt6.sip.voidptr], count: int) -> None: ...
    @typing.overload
    def allocate(self, count: int) -> None: ...
    def write(self, offset: int, data: typing.Optional[PyQt6.sip.voidptr], count: int) -> None: ...
    def read(self, offset: int, data: typing.Optional[PyQt6.sip.voidptr], count: int) -> bool: ...
    def __len__(self) -> int: ...
    def size(self) -> int: ...
    def bufferId(self) -> int: ...
    @typing.overload
    def release(self) -> None: ...
    @typing.overload
    @staticmethod
    def release(type: 'QOpenGLBuffer.Type') -> None: ...
    def bind(self) -> bool: ...
    def destroy(self) -> None: ...
    def isCreated(self) -> bool: ...
    def create(self) -> bool: ...
    def setUsagePattern(self, value: 'QOpenGLBuffer.UsagePattern') -> None: ...
    def usagePattern(self) -> 'QOpenGLBuffer.UsagePattern': ...
    def type(self) -> 'QOpenGLBuffer.Type': ...


class QOpenGLDebugMessage(PyQt6.sip.simplewrapper):

    class Severity(enum.Flag):
        InvalidSeverity = ... # type: QOpenGLDebugMessage.Severity
        HighSeverity = ... # type: QOpenGLDebugMessage.Severity
        MediumSeverity = ... # type: QOpenGLDebugMessage.Severity
        LowSeverity = ... # type: QOpenGLDebugMessage.Severity
        NotificationSeverity = ... # type: QOpenGLDebugMessage.Severity
        AnySeverity = ... # type: QOpenGLDebugMessage.Severity

    class Type(enum.Flag):
        InvalidType = ... # type: QOpenGLDebugMessage.Type
        ErrorType = ... # type: QOpenGLDebugMessage.Type
        DeprecatedBehaviorType = ... # type: QOpenGLDebugMessage.Type
        UndefinedBehaviorType = ... # type: QOpenGLDebugMessage.Type
        PortabilityType = ... # type: QOpenGLDebugMessage.Type
        PerformanceType = ... # type: QOpenGLDebugMessage.Type
        OtherType = ... # type: QOpenGLDebugMessage.Type
        MarkerType = ... # type: QOpenGLDebugMessage.Type
        GroupPushType = ... # type: QOpenGLDebugMessage.Type
        GroupPopType = ... # type: QOpenGLDebugMessage.Type
        AnyType = ... # type: QOpenGLDebugMessage.Type

    class Source(enum.Flag):
        InvalidSource = ... # type: QOpenGLDebugMessage.Source
        APISource = ... # type: QOpenGLDebugMessage.Source
        WindowSystemSource = ... # type: QOpenGLDebugMessage.Source
        ShaderCompilerSource = ... # type: QOpenGLDebugMessage.Source
        ThirdPartySource = ... # type: QOpenGLDebugMessage.Source
        ApplicationSource = ... # type: QOpenGLDebugMessage.Source
        OtherSource = ... # type: QOpenGLDebugMessage.Source
        AnySource = ... # type: QOpenGLDebugMessage.Source

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, debugMessage: 'QOpenGLDebugMessage') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @staticmethod
    def createThirdPartyMessage(text: typing.Optional[str], id: int = ..., severity: 'QOpenGLDebugMessage.Severity' = ..., type: 'QOpenGLDebugMessage.Type' = ...) -> 'QOpenGLDebugMessage': ...
    @staticmethod
    def createApplicationMessage(text: typing.Optional[str], id: int = ..., severity: 'QOpenGLDebugMessage.Severity' = ..., type: 'QOpenGLDebugMessage.Type' = ...) -> 'QOpenGLDebugMessage': ...
    def message(self) -> str: ...
    def id(self) -> int: ...
    def severity(self) -> 'QOpenGLDebugMessage.Severity': ...
    def type(self) -> 'QOpenGLDebugMessage.Type': ...
    def source(self) -> 'QOpenGLDebugMessage.Source': ...
    def swap(self, debugMessage: 'QOpenGLDebugMessage') -> None: ...


class QOpenGLDebugLogger(QtCore.QObject):

    class LoggingMode(enum.Enum):
        AsynchronousLogging = ... # type: QOpenGLDebugLogger.LoggingMode
        SynchronousLogging = ... # type: QOpenGLDebugLogger.LoggingMode

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    messageLogged: typing.ClassVar[QtCore.pyqtSignal]
    def stopLogging(self) -> None: ...
    def startLogging(self, loggingMode: 'QOpenGLDebugLogger.LoggingMode' = ...) -> None: ...
    def logMessage(self, debugMessage: QOpenGLDebugMessage) -> None: ...
    def loggedMessages(self) -> list[QOpenGLDebugMessage]: ...
    @typing.overload
    def disableMessages(self, sources: QOpenGLDebugMessage.Source = ..., types: QOpenGLDebugMessage.Type = ..., severities: QOpenGLDebugMessage.Severity = ...) -> None: ...
    @typing.overload
    def disableMessages(self, ids: collections.abc.Iterable[int], sources: QOpenGLDebugMessage.Source = ..., types: QOpenGLDebugMessage.Type = ...) -> None: ...
    @typing.overload
    def enableMessages(self, sources: QOpenGLDebugMessage.Source = ..., types: QOpenGLDebugMessage.Type = ..., severities: QOpenGLDebugMessage.Severity = ...) -> None: ...
    @typing.overload
    def enableMessages(self, ids: collections.abc.Iterable[int], sources: QOpenGLDebugMessage.Source = ..., types: QOpenGLDebugMessage.Type = ...) -> None: ...
    def popGroup(self) -> None: ...
    def pushGroup(self, name: typing.Optional[str], id: int = ..., source: QOpenGLDebugMessage.Source = ...) -> None: ...
    def maximumMessageLength(self) -> int: ...
    def loggingMode(self) -> 'QOpenGLDebugLogger.LoggingMode': ...
    def isLogging(self) -> bool: ...
    def initialize(self) -> bool: ...


class QOpenGLFramebufferObject(PyQt6.sip.simplewrapper):

    class FramebufferRestorePolicy(enum.Enum):
        DontRestoreFramebufferBinding = ... # type: QOpenGLFramebufferObject.FramebufferRestorePolicy
        RestoreFramebufferBindingToDefault = ... # type: QOpenGLFramebufferObject.FramebufferRestorePolicy
        RestoreFrameBufferBinding = ... # type: QOpenGLFramebufferObject.FramebufferRestorePolicy

    class Attachment(enum.Enum):
        NoAttachment = ... # type: QOpenGLFramebufferObject.Attachment
        CombinedDepthStencil = ... # type: QOpenGLFramebufferObject.Attachment
        Depth = ... # type: QOpenGLFramebufferObject.Attachment

    @typing.overload
    def __init__(self, size: QtCore.QSize, target: int = ...) -> None: ...
    @typing.overload
    def __init__(self, width: int, height: int, target: int = ...) -> None: ...
    @typing.overload
    def __init__(self, size: QtCore.QSize, attachment: 'QOpenGLFramebufferObject.Attachment', target: int = ..., internal_format: int = ...) -> None: ...
    @typing.overload
    def __init__(self, width: int, height: int, attachment: 'QOpenGLFramebufferObject.Attachment', target: int = ..., internal_format: int = ...) -> None: ...
    @typing.overload
    def __init__(self, size: QtCore.QSize, format: 'QOpenGLFramebufferObjectFormat') -> None: ...
    @typing.overload
    def __init__(self, width: int, height: int, format: 'QOpenGLFramebufferObjectFormat') -> None: ...

    def sizes(self) -> list[QtCore.QSize]: ...
    @typing.overload
    def addColorAttachment(self, size: QtCore.QSize, internal_format: int = ...) -> None: ...
    @typing.overload
    def addColorAttachment(self, width: int, height: int, internal_format: int = ...) -> None: ...
    @typing.overload
    def takeTexture(self) -> int: ...
    @typing.overload
    def takeTexture(self, colorAttachmentIndex: int) -> int: ...
    @typing.overload
    @staticmethod
    def blitFramebuffer(target: typing.Optional['QOpenGLFramebufferObject'], targetRect: QtCore.QRect, source: typing.Optional['QOpenGLFramebufferObject'], sourceRect: QtCore.QRect, buffers: int = ..., filter: int = ...) -> None: ...
    @typing.overload
    @staticmethod
    def blitFramebuffer(target: typing.Optional['QOpenGLFramebufferObject'], source: typing.Optional['QOpenGLFramebufferObject'], buffers: int = ..., filter: int = ...) -> None: ...
    @typing.overload
    @staticmethod
    def blitFramebuffer(target: typing.Optional['QOpenGLFramebufferObject'], targetRect: QtCore.QRect, source: typing.Optional['QOpenGLFramebufferObject'], sourceRect: QtCore.QRect, buffers: int, filter: int, readColorAttachmentIndex: int, drawColorAttachmentIndex: int) -> None: ...
    @typing.overload
    @staticmethod
    def blitFramebuffer(target: typing.Optional['QOpenGLFramebufferObject'], targetRect: QtCore.QRect, source: typing.Optional['QOpenGLFramebufferObject'], sourceRect: QtCore.QRect, buffers: int, filter: int, readColorAttachmentIndex: int, drawColorAttachmentIndex: int, restorePolicy: 'QOpenGLFramebufferObject.FramebufferRestorePolicy') -> None: ...
    @staticmethod
    def hasOpenGLFramebufferBlit() -> bool: ...
    @staticmethod
    def hasOpenGLFramebufferObjects() -> bool: ...
    @staticmethod
    def bindDefault() -> bool: ...
    def handle(self) -> int: ...
    def setAttachment(self, attachment: 'QOpenGLFramebufferObject.Attachment') -> None: ...
    def attachment(self) -> 'QOpenGLFramebufferObject.Attachment': ...
    @typing.overload
    def toImage(self, flipped: bool = ...) -> QtGui.QImage: ...
    @typing.overload
    def toImage(self, flipped: bool, colorAttachmentIndex: int) -> QtGui.QImage: ...
    def size(self) -> QtCore.QSize: ...
    def textures(self) -> list[int]: ...
    def texture(self) -> int: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def release(self) -> bool: ...
    def bind(self) -> bool: ...
    def isBound(self) -> bool: ...
    def isValid(self) -> bool: ...
    def format(self) -> 'QOpenGLFramebufferObjectFormat': ...


class QOpenGLFramebufferObjectFormat(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QOpenGLFramebufferObjectFormat') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def internalTextureFormat(self) -> int: ...
    def setInternalTextureFormat(self, internalTextureFormat: int) -> None: ...
    def textureTarget(self) -> int: ...
    def setTextureTarget(self, target: int) -> None: ...
    def attachment(self) -> QOpenGLFramebufferObject.Attachment: ...
    def setAttachment(self, attachment: QOpenGLFramebufferObject.Attachment) -> None: ...
    def mipmap(self) -> bool: ...
    def setMipmap(self, enabled: bool) -> None: ...
    def samples(self) -> int: ...
    def setSamples(self, samples: int) -> None: ...


class QOpenGLPaintDevice(QtGui.QPaintDevice):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, size: QtCore.QSize) -> None: ...
    @typing.overload
    def __init__(self, width: int, height: int) -> None: ...

    def metric(self, metric: QtGui.QPaintDevice.PaintDeviceMetric) -> int: ...
    def setDevicePixelRatio(self, devicePixelRatio: float) -> None: ...
    def ensureActiveTarget(self) -> None: ...
    def paintFlipped(self) -> bool: ...
    def setPaintFlipped(self, flipped: bool) -> None: ...
    def setDotsPerMeterY(self, a0: float) -> None: ...
    def setDotsPerMeterX(self, a0: float) -> None: ...
    def dotsPerMeterY(self) -> float: ...
    def dotsPerMeterX(self) -> float: ...
    def setSize(self, size: QtCore.QSize) -> None: ...
    def size(self) -> QtCore.QSize: ...
    def context(self) -> typing.Optional[QtGui.QOpenGLContext]: ...
    def paintEngine(self) -> typing.Optional[QtGui.QPaintEngine]: ...


class QOpenGLPixelTransferOptions(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QOpenGLPixelTransferOptions') -> None: ...

    def isSwapBytesEnabled(self) -> bool: ...
    def setSwapBytesEnabled(self, swapBytes: bool) -> None: ...
    def isLeastSignificantBitFirst(self) -> bool: ...
    def setLeastSignificantByteFirst(self, lsbFirst: bool) -> None: ...
    def rowLength(self) -> int: ...
    def setRowLength(self, rowLength: int) -> None: ...
    def imageHeight(self) -> int: ...
    def setImageHeight(self, imageHeight: int) -> None: ...
    def skipPixels(self) -> int: ...
    def setSkipPixels(self, skipPixels: int) -> None: ...
    def skipRows(self) -> int: ...
    def setSkipRows(self, skipRows: int) -> None: ...
    def skipImages(self) -> int: ...
    def setSkipImages(self, skipImages: int) -> None: ...
    def alignment(self) -> int: ...
    def setAlignment(self, alignment: int) -> None: ...
    def swap(self, other: 'QOpenGLPixelTransferOptions') -> None: ...


class QOpenGLShader(QtCore.QObject):

    class ShaderTypeBit(enum.Flag):
        Vertex = ... # type: QOpenGLShader.ShaderTypeBit
        Fragment = ... # type: QOpenGLShader.ShaderTypeBit
        Geometry = ... # type: QOpenGLShader.ShaderTypeBit
        TessellationControl = ... # type: QOpenGLShader.ShaderTypeBit
        TessellationEvaluation = ... # type: QOpenGLShader.ShaderTypeBit
        Compute = ... # type: QOpenGLShader.ShaderTypeBit

    def __init__(self, type: 'QOpenGLShader.ShaderTypeBit', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    @staticmethod
    def hasOpenGLShaders(type: 'QOpenGLShader.ShaderTypeBit', context: typing.Optional[QtGui.QOpenGLContext] = ...) -> bool: ...
    def shaderId(self) -> int: ...
    def log(self) -> str: ...
    def isCompiled(self) -> bool: ...
    def sourceCode(self) -> QtCore.QByteArray: ...
    def compileSourceFile(self, fileName: typing.Optional[str]) -> bool: ...
    @typing.overload
    def compileSourceCode(self, source: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> bool: ...
    @typing.overload
    def compileSourceCode(self, source: typing.Optional[str]) -> bool: ...
    def shaderType(self) -> 'QOpenGLShader.ShaderTypeBit': ...


class QOpenGLShaderProgram(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def addCacheableShaderFromSourceFile(self, type: QOpenGLShader.ShaderTypeBit, fileName: typing.Optional[str]) -> bool: ...
    @typing.overload
    def addCacheableShaderFromSourceCode(self, type: QOpenGLShader.ShaderTypeBit, source: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> bool: ...
    @typing.overload
    def addCacheableShaderFromSourceCode(self, type: QOpenGLShader.ShaderTypeBit, source: typing.Optional[str]) -> bool: ...
    def create(self) -> bool: ...
    def defaultInnerTessellationLevels(self) -> list[float]: ...
    def setDefaultInnerTessellationLevels(self, levels: collections.abc.Iterable[float]) -> None: ...
    def defaultOuterTessellationLevels(self) -> list[float]: ...
    def setDefaultOuterTessellationLevels(self, levels: collections.abc.Iterable[float]) -> None: ...
    def patchVertexCount(self) -> int: ...
    def setPatchVertexCount(self, count: int) -> None: ...
    def maxGeometryOutputVertices(self) -> int: ...
    @staticmethod
    def hasOpenGLShaderPrograms(context: typing.Optional[QtGui.QOpenGLContext] = ...) -> bool: ...
    @typing.overload
    def setUniformValueArray(self, location: int, values: PYQT_SHADER_UNIFORM_VALUE_ARRAY) -> None: ...
    @typing.overload
    def setUniformValueArray(self, name: typing.Optional[str], values: PYQT_SHADER_UNIFORM_VALUE_ARRAY) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: int) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: float) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, x: float, y: float) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, x: float, y: float, z: float) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, x: float, y: float, z: float, w: float) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QtGui.QVector2D) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QtGui.QVector3D) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QtGui.QVector4D) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, point: QtCore.QPoint) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, point: QtCore.QPointF) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, size: QtCore.QSize) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, size: QtCore.QSizeF) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QtGui.QMatrix2x2) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QtGui.QMatrix2x3) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QtGui.QMatrix2x4) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QtGui.QMatrix3x2) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QtGui.QMatrix3x3) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QtGui.QMatrix3x4) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QtGui.QMatrix4x2) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QtGui.QMatrix4x3) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QtGui.QMatrix4x4) -> None: ...
    @typing.overload
    def setUniformValue(self, location: int, value: QtGui.QTransform) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: int) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: float) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], x: float, y: float) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], x: float, y: float, z: float) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], x: float, y: float, z: float, w: float) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: QtGui.QVector2D) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: QtGui.QVector3D) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: QtGui.QVector4D) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], point: QtCore.QPoint) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], point: QtCore.QPointF) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], size: QtCore.QSize) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], size: QtCore.QSizeF) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: QtGui.QMatrix2x2) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: QtGui.QMatrix2x3) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: QtGui.QMatrix2x4) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: QtGui.QMatrix3x2) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: QtGui.QMatrix3x3) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: QtGui.QMatrix3x4) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: QtGui.QMatrix4x2) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: QtGui.QMatrix4x3) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: QtGui.QMatrix4x4) -> None: ...
    @typing.overload
    def setUniformValue(self, name: typing.Optional[str], value: QtGui.QTransform) -> None: ...
    @typing.overload
    def uniformLocation(self, name: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> int: ...
    @typing.overload
    def uniformLocation(self, name: typing.Optional[str]) -> int: ...
    @typing.overload
    def disableAttributeArray(self, location: int) -> None: ...
    @typing.overload
    def disableAttributeArray(self, name: typing.Optional[str]) -> None: ...
    @typing.overload
    def enableAttributeArray(self, location: int) -> None: ...
    @typing.overload
    def enableAttributeArray(self, name: typing.Optional[str]) -> None: ...
    @typing.overload
    def setAttributeBuffer(self, location: int, type: int, offset: int, tupleSize: int, stride: int = ...) -> None: ...
    @typing.overload
    def setAttributeBuffer(self, name: typing.Optional[str], type: int, offset: int, tupleSize: int, stride: int = ...) -> None: ...
    @typing.overload
    def setAttributeArray(self, location: int, values: PYQT_SHADER_ATTRIBUTE_ARRAY) -> None: ...
    @typing.overload
    def setAttributeArray(self, name: typing.Optional[str], values: PYQT_SHADER_ATTRIBUTE_ARRAY) -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, value: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, x: float, y: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, x: float, y: float, z: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, x: float, y: float, z: float, w: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, value: QtGui.QVector2D) -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, value: QtGui.QVector3D) -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, value: QtGui.QVector4D) -> None: ...
    @typing.overload
    def setAttributeValue(self, location: int, value: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def setAttributeValue(self, name: typing.Optional[str], value: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, name: typing.Optional[str], x: float, y: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, name: typing.Optional[str], x: float, y: float, z: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, name: typing.Optional[str], x: float, y: float, z: float, w: float) -> None: ...
    @typing.overload
    def setAttributeValue(self, name: typing.Optional[str], value: QtGui.QVector2D) -> None: ...
    @typing.overload
    def setAttributeValue(self, name: typing.Optional[str], value: QtGui.QVector3D) -> None: ...
    @typing.overload
    def setAttributeValue(self, name: typing.Optional[str], value: QtGui.QVector4D) -> None: ...
    @typing.overload
    def setAttributeValue(self, name: typing.Optional[str], value: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def attributeLocation(self, name: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> int: ...
    @typing.overload
    def attributeLocation(self, name: typing.Optional[str]) -> int: ...
    @typing.overload
    def bindAttributeLocation(self, name: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], location: int) -> None: ...
    @typing.overload
    def bindAttributeLocation(self, name: typing.Optional[str], location: int) -> None: ...
    def programId(self) -> int: ...
    def release(self) -> None: ...
    def bind(self) -> bool: ...
    def log(self) -> str: ...
    def isLinked(self) -> bool: ...
    def link(self) -> bool: ...
    def removeAllShaders(self) -> None: ...
    def addShaderFromSourceFile(self, type: QOpenGLShader.ShaderTypeBit, fileName: typing.Optional[str]) -> bool: ...
    @typing.overload
    def addShaderFromSourceCode(self, type: QOpenGLShader.ShaderTypeBit, source: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> bool: ...
    @typing.overload
    def addShaderFromSourceCode(self, type: QOpenGLShader.ShaderTypeBit, source: typing.Optional[str]) -> bool: ...
    def shaders(self) -> list[QOpenGLShader]: ...
    def removeShader(self, shader: typing.Optional[QOpenGLShader]) -> None: ...
    def addShader(self, shader: typing.Optional[QOpenGLShader]) -> bool: ...


class QOpenGLTexture(PyQt6.sip.simplewrapper):

    class ComparisonMode(enum.Enum):
        CompareRefToTexture = ... # type: QOpenGLTexture.ComparisonMode
        CompareNone = ... # type: QOpenGLTexture.ComparisonMode

    class ComparisonFunction(enum.Enum):
        CompareLessEqual = ... # type: QOpenGLTexture.ComparisonFunction
        CompareGreaterEqual = ... # type: QOpenGLTexture.ComparisonFunction
        CompareLess = ... # type: QOpenGLTexture.ComparisonFunction
        CompareGreater = ... # type: QOpenGLTexture.ComparisonFunction
        CompareEqual = ... # type: QOpenGLTexture.ComparisonFunction
        CommpareNotEqual = ... # type: QOpenGLTexture.ComparisonFunction
        CompareAlways = ... # type: QOpenGLTexture.ComparisonFunction
        CompareNever = ... # type: QOpenGLTexture.ComparisonFunction
        CompareNotEqual = ... # type: QOpenGLTexture.ComparisonFunction

    class CoordinateDirection(enum.Enum):
        DirectionS = ... # type: QOpenGLTexture.CoordinateDirection
        DirectionT = ... # type: QOpenGLTexture.CoordinateDirection
        DirectionR = ... # type: QOpenGLTexture.CoordinateDirection

    class WrapMode(enum.Enum):
        Repeat = ... # type: QOpenGLTexture.WrapMode
        MirroredRepeat = ... # type: QOpenGLTexture.WrapMode
        ClampToEdge = ... # type: QOpenGLTexture.WrapMode
        ClampToBorder = ... # type: QOpenGLTexture.WrapMode

    class Filter(enum.Enum):
        Nearest = ... # type: QOpenGLTexture.Filter
        Linear = ... # type: QOpenGLTexture.Filter
        NearestMipMapNearest = ... # type: QOpenGLTexture.Filter
        NearestMipMapLinear = ... # type: QOpenGLTexture.Filter
        LinearMipMapNearest = ... # type: QOpenGLTexture.Filter
        LinearMipMapLinear = ... # type: QOpenGLTexture.Filter

    class DepthStencilMode(enum.Enum):
        DepthMode = ... # type: QOpenGLTexture.DepthStencilMode
        StencilMode = ... # type: QOpenGLTexture.DepthStencilMode

    class SwizzleValue(enum.Enum):
        RedValue = ... # type: QOpenGLTexture.SwizzleValue
        GreenValue = ... # type: QOpenGLTexture.SwizzleValue
        BlueValue = ... # type: QOpenGLTexture.SwizzleValue
        AlphaValue = ... # type: QOpenGLTexture.SwizzleValue
        ZeroValue = ... # type: QOpenGLTexture.SwizzleValue
        OneValue = ... # type: QOpenGLTexture.SwizzleValue

    class SwizzleComponent(enum.Enum):
        SwizzleRed = ... # type: QOpenGLTexture.SwizzleComponent
        SwizzleGreen = ... # type: QOpenGLTexture.SwizzleComponent
        SwizzleBlue = ... # type: QOpenGLTexture.SwizzleComponent
        SwizzleAlpha = ... # type: QOpenGLTexture.SwizzleComponent

    class Feature(enum.Flag):
        ImmutableStorage = ... # type: QOpenGLTexture.Feature
        ImmutableMultisampleStorage = ... # type: QOpenGLTexture.Feature
        TextureRectangle = ... # type: QOpenGLTexture.Feature
        TextureArrays = ... # type: QOpenGLTexture.Feature
        Texture3D = ... # type: QOpenGLTexture.Feature
        TextureMultisample = ... # type: QOpenGLTexture.Feature
        TextureBuffer = ... # type: QOpenGLTexture.Feature
        TextureCubeMapArrays = ... # type: QOpenGLTexture.Feature
        Swizzle = ... # type: QOpenGLTexture.Feature
        StencilTexturing = ... # type: QOpenGLTexture.Feature
        AnisotropicFiltering = ... # type: QOpenGLTexture.Feature
        NPOTTextures = ... # type: QOpenGLTexture.Feature
        NPOTTextureRepeat = ... # type: QOpenGLTexture.Feature
        Texture1D = ... # type: QOpenGLTexture.Feature
        TextureComparisonOperators = ... # type: QOpenGLTexture.Feature
        TextureMipMapLevel = ... # type: QOpenGLTexture.Feature

    class PixelType(enum.Enum):
        NoPixelType = ... # type: QOpenGLTexture.PixelType
        Int8 = ... # type: QOpenGLTexture.PixelType
        UInt8 = ... # type: QOpenGLTexture.PixelType
        Int16 = ... # type: QOpenGLTexture.PixelType
        UInt16 = ... # type: QOpenGLTexture.PixelType
        Int32 = ... # type: QOpenGLTexture.PixelType
        UInt32 = ... # type: QOpenGLTexture.PixelType
        Float16 = ... # type: QOpenGLTexture.PixelType
        Float16OES = ... # type: QOpenGLTexture.PixelType
        Float32 = ... # type: QOpenGLTexture.PixelType
        UInt32_RGB9_E5 = ... # type: QOpenGLTexture.PixelType
        UInt32_RG11B10F = ... # type: QOpenGLTexture.PixelType
        UInt8_RG3B2 = ... # type: QOpenGLTexture.PixelType
        UInt8_RG3B2_Rev = ... # type: QOpenGLTexture.PixelType
        UInt16_RGB5A1 = ... # type: QOpenGLTexture.PixelType
        UInt16_RGB5A1_Rev = ... # type: QOpenGLTexture.PixelType
        UInt16_R5G6B5 = ... # type: QOpenGLTexture.PixelType
        UInt16_R5G6B5_Rev = ... # type: QOpenGLTexture.PixelType
        UInt16_RGBA4 = ... # type: QOpenGLTexture.PixelType
        UInt16_RGBA4_Rev = ... # type: QOpenGLTexture.PixelType
        UInt32_RGB10A2 = ... # type: QOpenGLTexture.PixelType
        UInt32_RGB10A2_Rev = ... # type: QOpenGLTexture.PixelType
        UInt32_RGBA8 = ... # type: QOpenGLTexture.PixelType
        UInt32_RGBA8_Rev = ... # type: QOpenGLTexture.PixelType
        UInt32_D24S8 = ... # type: QOpenGLTexture.PixelType
        Float32_D32_UInt32_S8_X24 = ... # type: QOpenGLTexture.PixelType

    class PixelFormat(enum.Enum):
        NoSourceFormat = ... # type: QOpenGLTexture.PixelFormat
        Red = ... # type: QOpenGLTexture.PixelFormat
        RG = ... # type: QOpenGLTexture.PixelFormat
        RGB = ... # type: QOpenGLTexture.PixelFormat
        BGR = ... # type: QOpenGLTexture.PixelFormat
        RGBA = ... # type: QOpenGLTexture.PixelFormat
        BGRA = ... # type: QOpenGLTexture.PixelFormat
        Red_Integer = ... # type: QOpenGLTexture.PixelFormat
        RG_Integer = ... # type: QOpenGLTexture.PixelFormat
        RGB_Integer = ... # type: QOpenGLTexture.PixelFormat
        BGR_Integer = ... # type: QOpenGLTexture.PixelFormat
        RGBA_Integer = ... # type: QOpenGLTexture.PixelFormat
        BGRA_Integer = ... # type: QOpenGLTexture.PixelFormat
        Depth = ... # type: QOpenGLTexture.PixelFormat
        DepthStencil = ... # type: QOpenGLTexture.PixelFormat
        Alpha = ... # type: QOpenGLTexture.PixelFormat
        Luminance = ... # type: QOpenGLTexture.PixelFormat
        LuminanceAlpha = ... # type: QOpenGLTexture.PixelFormat
        Stencil = ... # type: QOpenGLTexture.PixelFormat

    class CubeMapFace(enum.Enum):
        CubeMapPositiveX = ... # type: QOpenGLTexture.CubeMapFace
        CubeMapNegativeX = ... # type: QOpenGLTexture.CubeMapFace
        CubeMapPositiveY = ... # type: QOpenGLTexture.CubeMapFace
        CubeMapNegativeY = ... # type: QOpenGLTexture.CubeMapFace
        CubeMapPositiveZ = ... # type: QOpenGLTexture.CubeMapFace
        CubeMapNegativeZ = ... # type: QOpenGLTexture.CubeMapFace

    class TextureFormat(enum.Enum):
        NoFormat = ... # type: QOpenGLTexture.TextureFormat
        R8_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RG8_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RGB8_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RGBA8_UNorm = ... # type: QOpenGLTexture.TextureFormat
        R16_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RG16_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RGB16_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RGBA16_UNorm = ... # type: QOpenGLTexture.TextureFormat
        R8_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RG8_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RGB8_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RGBA8_SNorm = ... # type: QOpenGLTexture.TextureFormat
        R16_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RG16_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RGB16_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RGBA16_SNorm = ... # type: QOpenGLTexture.TextureFormat
        R8U = ... # type: QOpenGLTexture.TextureFormat
        RG8U = ... # type: QOpenGLTexture.TextureFormat
        RGB8U = ... # type: QOpenGLTexture.TextureFormat
        RGBA8U = ... # type: QOpenGLTexture.TextureFormat
        R16U = ... # type: QOpenGLTexture.TextureFormat
        RG16U = ... # type: QOpenGLTexture.TextureFormat
        RGB16U = ... # type: QOpenGLTexture.TextureFormat
        RGBA16U = ... # type: QOpenGLTexture.TextureFormat
        R32U = ... # type: QOpenGLTexture.TextureFormat
        RG32U = ... # type: QOpenGLTexture.TextureFormat
        RGB32U = ... # type: QOpenGLTexture.TextureFormat
        RGBA32U = ... # type: QOpenGLTexture.TextureFormat
        R8I = ... # type: QOpenGLTexture.TextureFormat
        RG8I = ... # type: QOpenGLTexture.TextureFormat
        RGB8I = ... # type: QOpenGLTexture.TextureFormat
        RGBA8I = ... # type: QOpenGLTexture.TextureFormat
        R16I = ... # type: QOpenGLTexture.TextureFormat
        RG16I = ... # type: QOpenGLTexture.TextureFormat
        RGB16I = ... # type: QOpenGLTexture.TextureFormat
        RGBA16I = ... # type: QOpenGLTexture.TextureFormat
        R32I = ... # type: QOpenGLTexture.TextureFormat
        RG32I = ... # type: QOpenGLTexture.TextureFormat
        RGB32I = ... # type: QOpenGLTexture.TextureFormat
        RGBA32I = ... # type: QOpenGLTexture.TextureFormat
        R16F = ... # type: QOpenGLTexture.TextureFormat
        RG16F = ... # type: QOpenGLTexture.TextureFormat
        RGB16F = ... # type: QOpenGLTexture.TextureFormat
        RGBA16F = ... # type: QOpenGLTexture.TextureFormat
        R32F = ... # type: QOpenGLTexture.TextureFormat
        RG32F = ... # type: QOpenGLTexture.TextureFormat
        RGB32F = ... # type: QOpenGLTexture.TextureFormat
        RGBA32F = ... # type: QOpenGLTexture.TextureFormat
        RGB9E5 = ... # type: QOpenGLTexture.TextureFormat
        RG11B10F = ... # type: QOpenGLTexture.TextureFormat
        RG3B2 = ... # type: QOpenGLTexture.TextureFormat
        R5G6B5 = ... # type: QOpenGLTexture.TextureFormat
        RGB5A1 = ... # type: QOpenGLTexture.TextureFormat
        RGBA4 = ... # type: QOpenGLTexture.TextureFormat
        RGB10A2 = ... # type: QOpenGLTexture.TextureFormat
        D16 = ... # type: QOpenGLTexture.TextureFormat
        D24 = ... # type: QOpenGLTexture.TextureFormat
        D24S8 = ... # type: QOpenGLTexture.TextureFormat
        D32 = ... # type: QOpenGLTexture.TextureFormat
        D32F = ... # type: QOpenGLTexture.TextureFormat
        D32FS8X24 = ... # type: QOpenGLTexture.TextureFormat
        RGB_DXT1 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_DXT1 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_DXT3 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_DXT5 = ... # type: QOpenGLTexture.TextureFormat
        R_ATI1N_UNorm = ... # type: QOpenGLTexture.TextureFormat
        R_ATI1N_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RG_ATI2N_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RG_ATI2N_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RGB_BP_UNSIGNED_FLOAT = ... # type: QOpenGLTexture.TextureFormat
        RGB_BP_SIGNED_FLOAT = ... # type: QOpenGLTexture.TextureFormat
        RGB_BP_UNorm = ... # type: QOpenGLTexture.TextureFormat
        SRGB8 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8 = ... # type: QOpenGLTexture.TextureFormat
        SRGB_DXT1 = ... # type: QOpenGLTexture.TextureFormat
        SRGB_Alpha_DXT1 = ... # type: QOpenGLTexture.TextureFormat
        SRGB_Alpha_DXT3 = ... # type: QOpenGLTexture.TextureFormat
        SRGB_Alpha_DXT5 = ... # type: QOpenGLTexture.TextureFormat
        SRGB_BP_UNorm = ... # type: QOpenGLTexture.TextureFormat
        DepthFormat = ... # type: QOpenGLTexture.TextureFormat
        AlphaFormat = ... # type: QOpenGLTexture.TextureFormat
        RGBFormat = ... # type: QOpenGLTexture.TextureFormat
        RGBAFormat = ... # type: QOpenGLTexture.TextureFormat
        LuminanceFormat = ... # type: QOpenGLTexture.TextureFormat
        LuminanceAlphaFormat = ... # type: QOpenGLTexture.TextureFormat
        S8 = ... # type: QOpenGLTexture.TextureFormat
        R11_EAC_UNorm = ... # type: QOpenGLTexture.TextureFormat
        R11_EAC_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RG11_EAC_UNorm = ... # type: QOpenGLTexture.TextureFormat
        RG11_EAC_SNorm = ... # type: QOpenGLTexture.TextureFormat
        RGB8_ETC2 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_ETC2 = ... # type: QOpenGLTexture.TextureFormat
        RGB8_PunchThrough_Alpha1_ETC2 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_PunchThrough_Alpha1_ETC2 = ... # type: QOpenGLTexture.TextureFormat
        RGBA8_ETC2_EAC = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ETC2_EAC = ... # type: QOpenGLTexture.TextureFormat
        RGB8_ETC1 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_4x4 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_5x4 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_5x5 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_6x5 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_6x6 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_8x5 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_8x6 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_8x8 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_10x5 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_10x6 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_10x8 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_10x10 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_12x10 = ... # type: QOpenGLTexture.TextureFormat
        RGBA_ASTC_12x12 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_4x4 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_5x4 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_5x5 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_6x5 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_6x6 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_8x5 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_8x6 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_8x8 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_10x5 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_10x6 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_10x8 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_10x10 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_12x10 = ... # type: QOpenGLTexture.TextureFormat
        SRGB8_Alpha8_ASTC_12x12 = ... # type: QOpenGLTexture.TextureFormat

    class TextureUnitReset(enum.Enum):
        ResetTextureUnit = ... # type: QOpenGLTexture.TextureUnitReset
        DontResetTextureUnit = ... # type: QOpenGLTexture.TextureUnitReset

    class MipMapGeneration(enum.Enum):
        GenerateMipMaps = ... # type: QOpenGLTexture.MipMapGeneration
        DontGenerateMipMaps = ... # type: QOpenGLTexture.MipMapGeneration

    class BindingTarget(enum.Enum):
        BindingTarget1D = ... # type: QOpenGLTexture.BindingTarget
        BindingTarget1DArray = ... # type: QOpenGLTexture.BindingTarget
        BindingTarget2D = ... # type: QOpenGLTexture.BindingTarget
        BindingTarget2DArray = ... # type: QOpenGLTexture.BindingTarget
        BindingTarget3D = ... # type: QOpenGLTexture.BindingTarget
        BindingTargetCubeMap = ... # type: QOpenGLTexture.BindingTarget
        BindingTargetCubeMapArray = ... # type: QOpenGLTexture.BindingTarget
        BindingTarget2DMultisample = ... # type: QOpenGLTexture.BindingTarget
        BindingTarget2DMultisampleArray = ... # type: QOpenGLTexture.BindingTarget
        BindingTargetRectangle = ... # type: QOpenGLTexture.BindingTarget
        BindingTargetBuffer = ... # type: QOpenGLTexture.BindingTarget

    class Target(enum.Enum):
        Target1D = ... # type: QOpenGLTexture.Target
        Target1DArray = ... # type: QOpenGLTexture.Target
        Target2D = ... # type: QOpenGLTexture.Target
        Target2DArray = ... # type: QOpenGLTexture.Target
        Target3D = ... # type: QOpenGLTexture.Target
        TargetCubeMap = ... # type: QOpenGLTexture.Target
        TargetCubeMapArray = ... # type: QOpenGLTexture.Target
        Target2DMultisample = ... # type: QOpenGLTexture.Target
        Target2DMultisampleArray = ... # type: QOpenGLTexture.Target
        TargetRectangle = ... # type: QOpenGLTexture.Target
        TargetBuffer = ... # type: QOpenGLTexture.Target

    @typing.overload
    def __init__(self, target: 'QOpenGLTexture.Target') -> None: ...
    @typing.overload
    def __init__(self, image: QtGui.QImage, genMipMaps: 'QOpenGLTexture.MipMapGeneration' = ...) -> None: ...

    def comparisonMode(self) -> 'QOpenGLTexture.ComparisonMode': ...
    def setComparisonMode(self, mode: 'QOpenGLTexture.ComparisonMode') -> None: ...
    def comparisonFunction(self) -> 'QOpenGLTexture.ComparisonFunction': ...
    def setComparisonFunction(self, function: 'QOpenGLTexture.ComparisonFunction') -> None: ...
    def isFixedSamplePositions(self) -> bool: ...
    def setFixedSamplePositions(self, fixed: bool) -> None: ...
    def samples(self) -> int: ...
    def setSamples(self, samples: int) -> None: ...
    def target(self) -> 'QOpenGLTexture.Target': ...
    def levelofDetailBias(self) -> float: ...
    def setLevelofDetailBias(self, bias: float) -> None: ...
    def levelOfDetailRange(self) -> tuple[float, float]: ...
    def setLevelOfDetailRange(self, min: float, max: float) -> None: ...
    def maximumLevelOfDetail(self) -> float: ...
    def setMaximumLevelOfDetail(self, value: float) -> None: ...
    def minimumLevelOfDetail(self) -> float: ...
    def setMinimumLevelOfDetail(self, value: float) -> None: ...
    def borderColor(self) -> QtGui.QColor: ...
    def setBorderColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def wrapMode(self, direction: 'QOpenGLTexture.CoordinateDirection') -> 'QOpenGLTexture.WrapMode': ...
    @typing.overload
    def setWrapMode(self, mode: 'QOpenGLTexture.WrapMode') -> None: ...
    @typing.overload
    def setWrapMode(self, direction: 'QOpenGLTexture.CoordinateDirection', mode: 'QOpenGLTexture.WrapMode') -> None: ...
    def maximumAnisotropy(self) -> float: ...
    def setMaximumAnisotropy(self, anisotropy: float) -> None: ...
    def minMagFilters(self) -> tuple['QOpenGLTexture.Filter', 'QOpenGLTexture.Filter']: ...
    def setMinMagFilters(self, minificationFilter: 'QOpenGLTexture.Filter', magnificationFilter: 'QOpenGLTexture.Filter') -> None: ...
    def magnificationFilter(self) -> 'QOpenGLTexture.Filter': ...
    def setMagnificationFilter(self, filter: 'QOpenGLTexture.Filter') -> None: ...
    def minificationFilter(self) -> 'QOpenGLTexture.Filter': ...
    def setMinificationFilter(self, filter: 'QOpenGLTexture.Filter') -> None: ...
    def depthStencilMode(self) -> 'QOpenGLTexture.DepthStencilMode': ...
    def setDepthStencilMode(self, mode: 'QOpenGLTexture.DepthStencilMode') -> None: ...
    def swizzleMask(self, component: 'QOpenGLTexture.SwizzleComponent') -> 'QOpenGLTexture.SwizzleValue': ...
    @typing.overload
    def setSwizzleMask(self, component: 'QOpenGLTexture.SwizzleComponent', value: 'QOpenGLTexture.SwizzleValue') -> None: ...
    @typing.overload
    def setSwizzleMask(self, r: 'QOpenGLTexture.SwizzleValue', g: 'QOpenGLTexture.SwizzleValue', b: 'QOpenGLTexture.SwizzleValue', a: 'QOpenGLTexture.SwizzleValue') -> None: ...
    @typing.overload
    def generateMipMaps(self) -> None: ...
    @typing.overload
    def generateMipMaps(self, baseLevel: int, resetBaseLevel: bool = ...) -> None: ...
    def isAutoMipMapGenerationEnabled(self) -> bool: ...
    def setAutoMipMapGenerationEnabled(self, enabled: bool) -> None: ...
    def mipLevelRange(self) -> tuple[int, int]: ...
    def setMipLevelRange(self, baseLevel: int, maxLevel: int) -> None: ...
    def mipMaxLevel(self) -> int: ...
    def setMipMaxLevel(self, maxLevel: int) -> None: ...
    def mipBaseLevel(self) -> int: ...
    def setMipBaseLevel(self, baseLevel: int) -> None: ...
    @staticmethod
    def hasFeature(feature: 'QOpenGLTexture.Feature') -> bool: ...
    @typing.overload
    def setCompressedData(self, mipLevel: int, layer: int, cubeFace: 'QOpenGLTexture.CubeMapFace', dataSize: int, data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setCompressedData(self, mipLevel: int, layer: int, dataSize: int, data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setCompressedData(self, mipLevel: int, dataSize: int, data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setCompressedData(self, dataSize: int, data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setCompressedData(self, mipLevel: int, layer: int, layerCount: int, cubeFace: 'QOpenGLTexture.CubeMapFace', dataSize: int, data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, mipLevel: int, layer: int, cubeFace: 'QOpenGLTexture.CubeMapFace', sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, mipLevel: int, layer: int, sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, mipLevel: int, sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, image: QtGui.QImage, genMipMaps: 'QOpenGLTexture.MipMapGeneration' = ...) -> None: ...
    @typing.overload
    def setData(self, mipLevel: int, layer: int, layerCount: int, cubeFace: 'QOpenGLTexture.CubeMapFace', sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, xOffset: int, yOffset: int, zOffset: int, width: int, height: int, depth: int, sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, xOffset: int, yOffset: int, zOffset: int, width: int, height: int, depth: int, mipLevel: int, sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, xOffset: int, yOffset: int, zOffset: int, width: int, height: int, depth: int, mipLevel: int, layer: int, sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, xOffset: int, yOffset: int, zOffset: int, width: int, height: int, depth: int, mipLevel: int, layer: int, cubeFace: 'QOpenGLTexture.CubeMapFace', sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    @typing.overload
    def setData(self, xOffset: int, yOffset: int, zOffset: int, width: int, height: int, depth: int, mipLevel: int, layer: int, cubeFace: 'QOpenGLTexture.CubeMapFace', layerCount: int, sourceFormat: 'QOpenGLTexture.PixelFormat', sourceType: 'QOpenGLTexture.PixelType', data: typing.Optional[PyQt6.sip.voidptr], options: typing.Optional[QOpenGLPixelTransferOptions] = ...) -> None: ...
    def isTextureView(self) -> bool: ...
    def createTextureView(self, target: 'QOpenGLTexture.Target', viewFormat: 'QOpenGLTexture.TextureFormat', minimumMipmapLevel: int, maximumMipmapLevel: int, minimumLayer: int, maximumLayer: int) -> typing.Optional['QOpenGLTexture']: ...
    def isStorageAllocated(self) -> bool: ...
    @typing.overload
    def allocateStorage(self) -> None: ...
    @typing.overload
    def allocateStorage(self, pixelFormat: 'QOpenGLTexture.PixelFormat', pixelType: 'QOpenGLTexture.PixelType') -> None: ...
    def faces(self) -> int: ...
    def layers(self) -> int: ...
    def setLayers(self, layers: int) -> None: ...
    def maximumMipLevels(self) -> int: ...
    def mipLevels(self) -> int: ...
    def setMipLevels(self, levels: int) -> None: ...
    def depth(self) -> int: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def setSize(self, width: int, height: int = ..., depth: int = ...) -> None: ...
    def format(self) -> 'QOpenGLTexture.TextureFormat': ...
    def setFormat(self, format: 'QOpenGLTexture.TextureFormat') -> None: ...
    @typing.overload
    @staticmethod
    def boundTextureId(target: 'QOpenGLTexture.BindingTarget') -> int: ...
    @typing.overload
    @staticmethod
    def boundTextureId(unit: int, target: 'QOpenGLTexture.BindingTarget') -> int: ...
    @typing.overload
    def isBound(self) -> bool: ...
    @typing.overload
    def isBound(self, unit: int) -> bool: ...
    @typing.overload
    def release(self) -> None: ...
    @typing.overload
    def release(self, unit: int, reset: 'QOpenGLTexture.TextureUnitReset' = ...) -> None: ...
    @typing.overload
    def bind(self) -> None: ...
    @typing.overload
    def bind(self, unit: int, reset: 'QOpenGLTexture.TextureUnitReset' = ...) -> None: ...
    def textureId(self) -> int: ...
    def isCreated(self) -> bool: ...
    def destroy(self) -> None: ...
    def create(self) -> bool: ...


class QOpenGLTextureBlitter(PyQt6.sip.simplewrapper):

    class Origin(enum.Enum):
        OriginBottomLeft = ... # type: QOpenGLTextureBlitter.Origin
        OriginTopLeft = ... # type: QOpenGLTextureBlitter.Origin

    def __init__(self) -> None: ...

    def supportsRectangleTarget(self) -> bool: ...
    @staticmethod
    def sourceTransform(subTexture: QtCore.QRectF, textureSize: QtCore.QSize, origin: 'QOpenGLTextureBlitter.Origin') -> QtGui.QMatrix3x3: ...
    @staticmethod
    def targetTransform(target: QtCore.QRectF, viewport: QtCore.QRect) -> QtGui.QMatrix4x4: ...
    @typing.overload
    def blit(self, texture: int, targetTransform: QtGui.QMatrix4x4, sourceOrigin: 'QOpenGLTextureBlitter.Origin') -> None: ...
    @typing.overload
    def blit(self, texture: int, targetTransform: QtGui.QMatrix4x4, sourceTransform: QtGui.QMatrix3x3) -> None: ...
    def setOpacity(self, opacity: float) -> None: ...
    def setRedBlueSwizzle(self, swizzle: bool) -> None: ...
    def release(self) -> None: ...
    def bind(self, target: int = ...) -> None: ...
    def supportsExternalOESTarget(self) -> bool: ...
    def destroy(self) -> None: ...
    def isCreated(self) -> bool: ...
    def create(self) -> bool: ...


class QOpenGLTimerQuery(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def waitForResult(self) -> int: ...
    def isResultAvailable(self) -> bool: ...
    def recordTimestamp(self) -> None: ...
    def waitForTimestamp(self) -> int: ...
    def end(self) -> None: ...
    def begin(self) -> None: ...
    def objectId(self) -> int: ...
    def isCreated(self) -> bool: ...
    def destroy(self) -> None: ...
    def create(self) -> bool: ...


class QOpenGLTimeMonitor(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def reset(self) -> None: ...
    def waitForIntervals(self) -> list[int]: ...
    def waitForSamples(self) -> list[int]: ...
    def isResultAvailable(self) -> bool: ...
    def recordSample(self) -> int: ...
    def objectIds(self) -> list[int]: ...
    def isCreated(self) -> bool: ...
    def destroy(self) -> None: ...
    def create(self) -> bool: ...
    def sampleCount(self) -> int: ...
    def setSampleCount(self, sampleCount: int) -> None: ...


class QAbstractOpenGLFunctions(PyQt6.sip.wrapper): ...


class QOpenGLVersionFunctionsFactory(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QOpenGLVersionFunctionsFactory') -> None: ...

    @staticmethod
    def get(versionProfile: 'QOpenGLVersionProfile' = ..., context: typing.Optional[QtGui.QOpenGLContext] = ...) -> QAbstractOpenGLFunctions: ...


class QOpenGLVertexArrayObject(QtCore.QObject):

    class Binder(PyQt6.sip.simplewrapper):

        def __init__(self, v: typing.Optional['QOpenGLVertexArrayObject']) -> None: ...

        def __exit__(self, type: typing.Any, value: typing.Any, traceback: typing.Any) -> None: ...
        def __enter__(self) -> typing.Any: ...
        def rebind(self) -> None: ...
        def release(self) -> None: ...

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def release(self) -> None: ...
    def bind(self) -> None: ...
    def objectId(self) -> int: ...
    def isCreated(self) -> bool: ...
    def destroy(self) -> None: ...
    def create(self) -> bool: ...


class QOpenGLWindow(QtGui.QPaintDeviceWindow):

    class UpdateBehavior(enum.Enum):
        NoPartialUpdate = ... # type: QOpenGLWindow.UpdateBehavior
        PartialUpdateBlit = ... # type: QOpenGLWindow.UpdateBehavior
        PartialUpdateBlend = ... # type: QOpenGLWindow.UpdateBehavior

    @typing.overload
    def __init__(self, updateBehavior: 'QOpenGLWindow.UpdateBehavior' = ..., parent: typing.Optional[QtGui.QWindow] = ...) -> None: ...
    @typing.overload
    def __init__(self, shareContext: typing.Optional[QtGui.QOpenGLContext], updateBehavior: 'QOpenGLWindow.UpdateBehavior' = ..., parent: typing.Optional[QtGui.QWindow] = ...) -> None: ...

    def metric(self, metric: QtGui.QPaintDevice.PaintDeviceMetric) -> int: ...
    def resizeEvent(self, event: typing.Optional[QtGui.QResizeEvent]) -> None: ...
    def paintEvent(self, event: typing.Optional[QtGui.QPaintEvent]) -> None: ...
    def paintOverGL(self) -> None: ...
    def paintUnderGL(self) -> None: ...
    def paintGL(self) -> None: ...
    def resizeGL(self, w: int, h: int) -> None: ...
    def initializeGL(self) -> None: ...
    frameSwapped: typing.ClassVar[QtCore.pyqtSignal]
    def shareContext(self) -> typing.Optional[QtGui.QOpenGLContext]: ...
    def grabFramebuffer(self) -> QtGui.QImage: ...
    def defaultFramebufferObject(self) -> int: ...
    def context(self) -> typing.Optional[QtGui.QOpenGLContext]: ...
    def doneCurrent(self) -> None: ...
    def makeCurrent(self) -> None: ...
    def isValid(self) -> bool: ...
    def updateBehavior(self) -> 'QOpenGLWindow.UpdateBehavior': ...


class QOpenGLFunctions_2_0(QAbstractOpenGLFunctions):

    def __init__(self) -> None: ...

    def glVertexAttrib1d(self, index: int, x: float) -> None: ...
    def glVertexAttrib1dv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib1f(self, index: int, x: float) -> None: ...
    def glVertexAttrib1fv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib1s(self, index: int, x: int) -> None: ...
    def glVertexAttrib1sv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib2d(self, index: int, x: float, y: float) -> None: ...
    def glVertexAttrib2dv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib2f(self, index: int, x: float, y: float) -> None: ...
    def glVertexAttrib2fv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib2s(self, index: int, x: int, y: int) -> None: ...
    def glVertexAttrib2sv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib3d(self, index: int, x: float, y: float, z: float) -> None: ...
    def glVertexAttrib3dv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib3f(self, index: int, x: float, y: float, z: float) -> None: ...
    def glVertexAttrib3fv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib3s(self, index: int, x: int, y: int, z: int) -> None: ...
    def glVertexAttrib3sv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4Nbv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4Niv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4Nsv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4Nub(self, index: int, x: int, y: int, z: int, w: int) -> None: ...
    def glVertexAttrib4Nubv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4Nuiv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4Nusv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4bv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4d(self, index: int, x: float, y: float, z: float, w: float) -> None: ...
    def glVertexAttrib4dv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4f(self, index: int, x: float, y: float, z: float, w: float) -> None: ...
    def glVertexAttrib4fv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4iv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4s(self, index: int, x: int, y: int, z: int, w: int) -> None: ...
    def glVertexAttrib4sv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4ubv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4uiv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4usv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glFogCoordf(self, coord: float) -> None: ...
    def glFogCoordfv(self, coord: PYQT_OPENGL_ARRAY) -> None: ...
    def glFogCoordd(self, coord: float) -> None: ...
    def glFogCoorddv(self, coord: PYQT_OPENGL_ARRAY) -> None: ...
    def glFogCoordPointer(self, type: int, stride: int, pointer: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3b(self, red: int, green: int, blue: int) -> None: ...
    def glSecondaryColor3bv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3d(self, red: float, green: float, blue: float) -> None: ...
    def glSecondaryColor3dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3f(self, red: float, green: float, blue: float) -> None: ...
    def glSecondaryColor3fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3i(self, red: int, green: int, blue: int) -> None: ...
    def glSecondaryColor3iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3s(self, red: int, green: int, blue: int) -> None: ...
    def glSecondaryColor3sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3ub(self, red: int, green: int, blue: int) -> None: ...
    def glSecondaryColor3ubv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3ui(self, red: int, green: int, blue: int) -> None: ...
    def glSecondaryColor3uiv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3us(self, red: int, green: int, blue: int) -> None: ...
    def glSecondaryColor3usv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColorPointer(self, size: int, type: int, stride: int, pointer: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos2d(self, x: float, y: float) -> None: ...
    def glWindowPos2dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos2f(self, x: float, y: float) -> None: ...
    def glWindowPos2fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos2i(self, x: int, y: int) -> None: ...
    def glWindowPos2iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos2s(self, x: int, y: int) -> None: ...
    def glWindowPos2sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos3d(self, x: float, y: float, z: float) -> None: ...
    def glWindowPos3dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos3f(self, x: float, y: float, z: float) -> None: ...
    def glWindowPos3fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos3i(self, x: int, y: int, z: int) -> None: ...
    def glWindowPos3iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos3s(self, x: int, y: int, z: int) -> None: ...
    def glWindowPos3sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glClientActiveTexture(self, texture: int) -> None: ...
    def glMultiTexCoord1d(self, target: int, s: float) -> None: ...
    def glMultiTexCoord1dv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord1f(self, target: int, s: float) -> None: ...
    def glMultiTexCoord1fv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord1i(self, target: int, s: int) -> None: ...
    def glMultiTexCoord1iv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord1s(self, target: int, s: int) -> None: ...
    def glMultiTexCoord1sv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord2d(self, target: int, s: float, t: float) -> None: ...
    def glMultiTexCoord2dv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord2f(self, target: int, s: float, t: float) -> None: ...
    def glMultiTexCoord2fv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord2i(self, target: int, s: int, t: int) -> None: ...
    def glMultiTexCoord2iv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord2s(self, target: int, s: int, t: int) -> None: ...
    def glMultiTexCoord2sv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord3d(self, target: int, s: float, t: float, r: float) -> None: ...
    def glMultiTexCoord3dv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord3f(self, target: int, s: float, t: float, r: float) -> None: ...
    def glMultiTexCoord3fv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord3i(self, target: int, s: int, t: int, r: int) -> None: ...
    def glMultiTexCoord3iv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord3s(self, target: int, s: int, t: int, r: int) -> None: ...
    def glMultiTexCoord3sv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord4d(self, target: int, s: float, t: float, r: float, q: float) -> None: ...
    def glMultiTexCoord4dv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord4f(self, target: int, s: float, t: float, r: float, q: float) -> None: ...
    def glMultiTexCoord4fv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord4i(self, target: int, s: int, t: int, r: int, q: int) -> None: ...
    def glMultiTexCoord4iv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord4s(self, target: int, s: int, t: int, r: int, q: int) -> None: ...
    def glMultiTexCoord4sv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glLoadTransposeMatrixf(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glLoadTransposeMatrixd(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultTransposeMatrixf(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultTransposeMatrixd(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glColorTable(self, target: int, internalformat: int, width: int, format: int, type: int, table: PYQT_OPENGL_ARRAY) -> None: ...
    def glColorTableParameterfv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glColorTableParameteriv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glCopyColorTable(self, target: int, internalformat: int, x: int, y: int, width: int) -> None: ...
    def glGetColorTableParameterfv(self, target: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetColorTableParameteriv(self, target: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int, int]]]: ...
    def glColorSubTable(self, target: int, start: int, count: int, format: int, type: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCopyColorSubTable(self, target: int, start: int, x: int, y: int, width: int) -> None: ...
    def glConvolutionFilter1D(self, target: int, internalformat: int, width: int, format: int, type: int, image: PYQT_OPENGL_ARRAY) -> None: ...
    def glConvolutionFilter2D(self, target: int, internalformat: int, width: int, height: int, format: int, type: int, image: PYQT_OPENGL_ARRAY) -> None: ...
    def glConvolutionParameterf(self, target: int, pname: int, params: float) -> None: ...
    def glConvolutionParameterfv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glConvolutionParameteri(self, target: int, pname: int, params: int) -> None: ...
    def glConvolutionParameteriv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glCopyConvolutionFilter1D(self, target: int, internalformat: int, x: int, y: int, width: int) -> None: ...
    def glCopyConvolutionFilter2D(self, target: int, internalformat: int, x: int, y: int, width: int, height: int) -> None: ...
    def glGetConvolutionParameterfv(self, target: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetConvolutionParameteriv(self, target: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int, int]]]: ...
    def glHistogram(self, target: int, width: int, internalformat: int, sink: int) -> None: ...
    def glMinmax(self, target: int, internalformat: int, sink: int) -> None: ...
    def glResetHistogram(self, target: int) -> None: ...
    def glResetMinmax(self, target: int) -> None: ...
    def glArrayElement(self, i: int) -> None: ...
    def glColorPointer(self, size: int, type: int, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glDisableClientState(self, array: int) -> None: ...
    def glEdgeFlagPointer(self, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glEnableClientState(self, array: int) -> None: ...
    def glIndexPointer(self, type: int, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glNormalPointer(self, type: int, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glTexCoordPointer(self, size: int, type: int, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glVertexPointer(self, size: int, type: int, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glPopClientAttrib(self) -> None: ...
    def glPushClientAttrib(self, mask: int) -> None: ...
    def glNewList(self, list: int, mode: int) -> None: ...
    def glEndList(self) -> None: ...
    def glCallList(self, list: int) -> None: ...
    def glDeleteLists(self, list: int, range: int) -> None: ...
    def glGenLists(self, range: int) -> int: ...
    def glListBase(self, base: int) -> None: ...
    def glBegin(self, mode: int) -> None: ...
    def glBitmap(self, width: int, height: int, xorig: float, yorig: float, xmove: float, ymove: float, bitmap: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3b(self, red: int, green: int, blue: int) -> None: ...
    def glColor3bv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3d(self, red: float, green: float, blue: float) -> None: ...
    def glColor3dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3f(self, red: float, green: float, blue: float) -> None: ...
    def glColor3fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3i(self, red: int, green: int, blue: int) -> None: ...
    def glColor3iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3s(self, red: int, green: int, blue: int) -> None: ...
    def glColor3sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3ub(self, red: int, green: int, blue: int) -> None: ...
    def glColor3ubv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3ui(self, red: int, green: int, blue: int) -> None: ...
    def glColor3uiv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3us(self, red: int, green: int, blue: int) -> None: ...
    def glColor3usv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4b(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glColor4bv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4d(self, red: float, green: float, blue: float, alpha: float) -> None: ...
    def glColor4dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4f(self, red: float, green: float, blue: float, alpha: float) -> None: ...
    def glColor4fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4i(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glColor4iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4s(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glColor4sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4ub(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glColor4ubv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4ui(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glColor4uiv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4us(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glColor4usv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glEdgeFlag(self, flag: int) -> None: ...
    def glEdgeFlagv(self, flag: PYQT_OPENGL_ARRAY) -> None: ...
    def glEnd(self) -> None: ...
    def glIndexd(self, c: float) -> None: ...
    def glIndexdv(self, c: PYQT_OPENGL_ARRAY) -> None: ...
    def glIndexf(self, c: float) -> None: ...
    def glIndexfv(self, c: PYQT_OPENGL_ARRAY) -> None: ...
    def glIndexi(self, c: int) -> None: ...
    def glIndexiv(self, c: PYQT_OPENGL_ARRAY) -> None: ...
    def glIndexs(self, c: int) -> None: ...
    def glIndexsv(self, c: PYQT_OPENGL_ARRAY) -> None: ...
    def glNormal3b(self, nx: int, ny: int, nz: int) -> None: ...
    def glNormal3bv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glNormal3d(self, nx: float, ny: float, nz: float) -> None: ...
    def glNormal3dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glNormal3f(self, nx: float, ny: float, nz: float) -> None: ...
    def glNormal3fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glNormal3i(self, nx: int, ny: int, nz: int) -> None: ...
    def glNormal3iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glNormal3s(self, nx: int, ny: int, nz: int) -> None: ...
    def glNormal3sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos2d(self, x: float, y: float) -> None: ...
    def glRasterPos2dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos2f(self, x: float, y: float) -> None: ...
    def glRasterPos2fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos2i(self, x: int, y: int) -> None: ...
    def glRasterPos2iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos2s(self, x: int, y: int) -> None: ...
    def glRasterPos2sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos3d(self, x: float, y: float, z: float) -> None: ...
    def glRasterPos3dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos3f(self, x: float, y: float, z: float) -> None: ...
    def glRasterPos3fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos3i(self, x: int, y: int, z: int) -> None: ...
    def glRasterPos3iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos3s(self, x: int, y: int, z: int) -> None: ...
    def glRasterPos3sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos4d(self, x: float, y: float, z: float, w: float) -> None: ...
    def glRasterPos4dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos4f(self, x: float, y: float, z: float, w: float) -> None: ...
    def glRasterPos4fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos4i(self, x: int, y: int, z: int, w: int) -> None: ...
    def glRasterPos4iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos4s(self, x: int, y: int, z: int, w: int) -> None: ...
    def glRasterPos4sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRectd(self, x1: float, y1: float, x2: float, y2: float) -> None: ...
    def glRectf(self, x1: float, y1: float, x2: float, y2: float) -> None: ...
    def glRecti(self, x1: int, y1: int, x2: int, y2: int) -> None: ...
    def glRects(self, x1: int, y1: int, x2: int, y2: int) -> None: ...
    def glTexCoord1d(self, s: float) -> None: ...
    def glTexCoord1dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord1f(self, s: float) -> None: ...
    def glTexCoord1fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord1i(self, s: int) -> None: ...
    def glTexCoord1iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord1s(self, s: int) -> None: ...
    def glTexCoord1sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord2d(self, s: float, t: float) -> None: ...
    def glTexCoord2dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord2f(self, s: float, t: float) -> None: ...
    def glTexCoord2fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord2i(self, s: int, t: int) -> None: ...
    def glTexCoord2iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord2s(self, s: int, t: int) -> None: ...
    def glTexCoord2sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord3d(self, s: float, t: float, r: float) -> None: ...
    def glTexCoord3dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord3f(self, s: float, t: float, r: float) -> None: ...
    def glTexCoord3fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord3i(self, s: int, t: int, r: int) -> None: ...
    def glTexCoord3iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord3s(self, s: int, t: int, r: int) -> None: ...
    def glTexCoord3sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord4d(self, s: float, t: float, r: float, q: float) -> None: ...
    def glTexCoord4dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord4f(self, s: float, t: float, r: float, q: float) -> None: ...
    def glTexCoord4fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord4i(self, s: int, t: int, r: int, q: int) -> None: ...
    def glTexCoord4iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord4s(self, s: int, t: int, r: int, q: int) -> None: ...
    def glTexCoord4sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex2d(self, x: float, y: float) -> None: ...
    def glVertex2dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex2f(self, x: float, y: float) -> None: ...
    def glVertex2fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex2i(self, x: int, y: int) -> None: ...
    def glVertex2iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex2s(self, x: int, y: int) -> None: ...
    def glVertex2sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex3d(self, x: float, y: float, z: float) -> None: ...
    def glVertex3dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex3f(self, x: float, y: float, z: float) -> None: ...
    def glVertex3fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex3i(self, x: int, y: int, z: int) -> None: ...
    def glVertex3iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex3s(self, x: int, y: int, z: int) -> None: ...
    def glVertex3sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex4d(self, x: float, y: float, z: float, w: float) -> None: ...
    def glVertex4dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex4f(self, x: float, y: float, z: float, w: float) -> None: ...
    def glVertex4fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex4i(self, x: int, y: int, z: int, w: int) -> None: ...
    def glVertex4iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex4s(self, x: int, y: int, z: int, w: int) -> None: ...
    def glVertex4sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glClipPlane(self, plane: int, equation: PYQT_OPENGL_ARRAY) -> None: ...
    def glColorMaterial(self, face: int, mode: int) -> None: ...
    def glFogf(self, pname: int, param: float) -> None: ...
    def glFogfv(self, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glFogi(self, pname: int, param: int) -> None: ...
    def glFogiv(self, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glLightf(self, light: int, pname: int, param: float) -> None: ...
    def glLightfv(self, light: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glLighti(self, light: int, pname: int, param: int) -> None: ...
    def glLightiv(self, light: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glLightModelf(self, pname: int, param: float) -> None: ...
    def glLightModelfv(self, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glLightModeli(self, pname: int, param: int) -> None: ...
    def glLightModeliv(self, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glLineStipple(self, factor: int, pattern: int) -> None: ...
    def glMaterialf(self, face: int, pname: int, param: float) -> None: ...
    def glMaterialfv(self, face: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glMateriali(self, face: int, pname: int, param: int) -> None: ...
    def glMaterialiv(self, face: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glPolygonStipple(self, mask: PYQT_OPENGL_ARRAY) -> None: ...
    def glShadeModel(self, mode: int) -> None: ...
    def glTexEnvf(self, target: int, pname: int, param: float) -> None: ...
    def glTexEnvfv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexEnvi(self, target: int, pname: int, param: int) -> None: ...
    def glTexEnviv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexGend(self, coord: int, pname: int, param: float) -> None: ...
    def glTexGendv(self, coord: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexGenf(self, coord: int, pname: int, param: float) -> None: ...
    def glTexGenfv(self, coord: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexGeni(self, coord: int, pname: int, param: int) -> None: ...
    def glTexGeniv(self, coord: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glRenderMode(self, mode: int) -> int: ...
    def glInitNames(self) -> None: ...
    def glLoadName(self, name: int) -> None: ...
    def glPassThrough(self, token: float) -> None: ...
    def glPopName(self) -> None: ...
    def glPushName(self, name: int) -> None: ...
    def glClearAccum(self, red: float, green: float, blue: float, alpha: float) -> None: ...
    def glClearIndex(self, c: float) -> None: ...
    def glIndexMask(self, mask: int) -> None: ...
    def glAccum(self, op: int, value: float) -> None: ...
    def glPopAttrib(self) -> None: ...
    def glPushAttrib(self, mask: int) -> None: ...
    def glMap1d(self, target: int, u1: float, u2: float, stride: int, order: int, points: PYQT_OPENGL_ARRAY) -> None: ...
    def glMap1f(self, target: int, u1: float, u2: float, stride: int, order: int, points: PYQT_OPENGL_ARRAY) -> None: ...
    def glMap2d(self, target: int, u1: float, u2: float, ustride: int, uorder: int, v1: float, v2: float, vstride: int, vorder: int, points: PYQT_OPENGL_ARRAY) -> None: ...
    def glMap2f(self, target: int, u1: float, u2: float, ustride: int, uorder: int, v1: float, v2: float, vstride: int, vorder: int, points: PYQT_OPENGL_ARRAY) -> None: ...
    def glMapGrid1d(self, un: int, u1: float, u2: float) -> None: ...
    def glMapGrid1f(self, un: int, u1: float, u2: float) -> None: ...
    def glMapGrid2d(self, un: int, u1: float, u2: float, vn: int, v1: float, v2: float) -> None: ...
    def glMapGrid2f(self, un: int, u1: float, u2: float, vn: int, v1: float, v2: float) -> None: ...
    def glEvalCoord1d(self, u: float) -> None: ...
    def glEvalCoord1dv(self, u: PYQT_OPENGL_ARRAY) -> None: ...
    def glEvalCoord1f(self, u: float) -> None: ...
    def glEvalCoord1fv(self, u: PYQT_OPENGL_ARRAY) -> None: ...
    def glEvalCoord2d(self, u: float, v: float) -> None: ...
    def glEvalCoord2dv(self, u: PYQT_OPENGL_ARRAY) -> None: ...
    def glEvalCoord2f(self, u: float, v: float) -> None: ...
    def glEvalCoord2fv(self, u: PYQT_OPENGL_ARRAY) -> None: ...
    def glEvalMesh1(self, mode: int, i1: int, i2: int) -> None: ...
    def glEvalPoint1(self, i: int) -> None: ...
    def glEvalMesh2(self, mode: int, i1: int, i2: int, j1: int, j2: int) -> None: ...
    def glEvalPoint2(self, i: int, j: int) -> None: ...
    def glAlphaFunc(self, func: int, ref: float) -> None: ...
    def glPixelZoom(self, xfactor: float, yfactor: float) -> None: ...
    def glPixelTransferf(self, pname: int, param: float) -> None: ...
    def glPixelTransferi(self, pname: int, param: int) -> None: ...
    def glPixelMapfv(self, map: int, mapsize: int, values: PYQT_OPENGL_ARRAY) -> None: ...
    def glPixelMapuiv(self, map: int, mapsize: int, values: PYQT_OPENGL_ARRAY) -> None: ...
    def glPixelMapusv(self, map: int, mapsize: int, values: PYQT_OPENGL_ARRAY) -> None: ...
    def glCopyPixels(self, x: int, y: int, width: int, height: int, type: int) -> None: ...
    def glDrawPixels(self, width: int, height: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glGetClipPlane(self, plane: int) -> typing.Optional[tuple[float, float, float, float]]: ...
    def glGetLightfv(self, light: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float], tuple[float, float, float, float]]]: ...
    def glGetLightiv(self, light: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int], tuple[int, int, int, int]]]: ...
    def glGetMaterialfv(self, face: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float], tuple[float, float, float, float]]]: ...
    def glGetMaterialiv(self, face: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int], tuple[int, int, int, int]]]: ...
    def glGetTexEnvfv(self, target: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetTexEnviv(self, target: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int, int]]]: ...
    def glGetTexGendv(self, coord: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetTexGenfv(self, coord: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetTexGeniv(self, coord: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int, int]]]: ...
    def glIsList(self, list: int) -> int: ...
    def glFrustum(self, left: float, right: float, bottom: float, top: float, zNear: float, zFar: float) -> None: ...
    def glLoadIdentity(self) -> None: ...
    def glLoadMatrixf(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glLoadMatrixd(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glMatrixMode(self, mode: int) -> None: ...
    def glMultMatrixf(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultMatrixd(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glOrtho(self, left: float, right: float, bottom: float, top: float, zNear: float, zFar: float) -> None: ...
    def glPopMatrix(self) -> None: ...
    def glPushMatrix(self) -> None: ...
    def glRotated(self, angle: float, x: float, y: float, z: float) -> None: ...
    def glRotatef(self, angle: float, x: float, y: float, z: float) -> None: ...
    def glScaled(self, x: float, y: float, z: float) -> None: ...
    def glScalef(self, x: float, y: float, z: float) -> None: ...
    def glTranslated(self, x: float, y: float, z: float) -> None: ...
    def glTranslatef(self, x: float, y: float, z: float) -> None: ...
    def glBlendEquationSeparate(self, modeRGB: int, modeAlpha: int) -> None: ...
    def glDrawBuffers(self, n: int, bufs: PYQT_OPENGL_ARRAY) -> None: ...
    def glStencilOpSeparate(self, face: int, sfail: int, dpfail: int, dppass: int) -> None: ...
    def glStencilFuncSeparate(self, face: int, func: int, ref: int, mask: int) -> None: ...
    def glStencilMaskSeparate(self, face: int, mask: int) -> None: ...
    def glAttachShader(self, program: int, shader: int) -> None: ...
    def glBindAttribLocation(self, program: int, index: int, name: typing.Optional[str]) -> None: ...
    def glCompileShader(self, shader: int) -> None: ...
    def glCreateProgram(self) -> int: ...
    def glCreateShader(self, type: int) -> int: ...
    def glDeleteProgram(self, program: int) -> None: ...
    def glDeleteShader(self, shader: int) -> None: ...
    def glDetachShader(self, program: int, shader: int) -> None: ...
    def glDisableVertexAttribArray(self, index: int) -> None: ...
    def glEnableVertexAttribArray(self, index: int) -> None: ...
    def glGetActiveAttrib(self, program: int, index: int) -> tuple[str, int, int]: ...
    def glGetActiveUniform(self, program: int, index: int) -> tuple[str, int, int]: ...
    def glGetAttachedShaders(self, program: int) -> tuple[int, ...]: ...
    def glGetAttribLocation(self, program: int, name: typing.Optional[str]) -> int: ...
    def glGetProgramiv(self, program: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int]]]: ...
    def glGetProgramInfoLog(self, program: int) -> bytes: ...
    def glGetShaderiv(self, shader: int, pname: int) -> typing.Optional[int]: ...
    def glGetShaderInfoLog(self, shader: int) -> bytes: ...
    def glGetShaderSource(self, shader: int) -> bytes: ...
    def glGetUniformLocation(self, program: int, name: typing.Optional[str]) -> int: ...
    def glGetVertexAttribdv(self, index: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetVertexAttribfv(self, index: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetVertexAttribiv(self, index: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int, int]]]: ...
    def glIsProgram(self, program: int) -> int: ...
    def glIsShader(self, shader: int) -> int: ...
    def glLinkProgram(self, program: int) -> None: ...
    def glUseProgram(self, program: int) -> None: ...
    def glUniform1f(self, location: int, v0: float) -> None: ...
    def glUniform2f(self, location: int, v0: float, v1: float) -> None: ...
    def glUniform3f(self, location: int, v0: float, v1: float, v2: float) -> None: ...
    def glUniform4f(self, location: int, v0: float, v1: float, v2: float, v3: float) -> None: ...
    def glUniform1i(self, location: int, v0: int) -> None: ...
    def glUniform2i(self, location: int, v0: int, v1: int) -> None: ...
    def glUniform3i(self, location: int, v0: int, v1: int, v2: int) -> None: ...
    def glUniform4i(self, location: int, v0: int, v1: int, v2: int, v3: int) -> None: ...
    def glUniform1fv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform2fv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform3fv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform4fv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform1iv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform2iv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform3iv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform4iv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniformMatrix2fv(self, location: int, count: int, transpose: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniformMatrix3fv(self, location: int, count: int, transpose: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniformMatrix4fv(self, location: int, count: int, transpose: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glValidateProgram(self, program: int) -> None: ...
    def glVertexAttribPointer(self, index: int, size: int, type: int, normalized: int, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glGenQueries(self, n: int) -> typing.Optional[typing.Union[int, tuple[int, ...]]]: ...
    def glDeleteQueries(self, n: int, ids: PYQT_OPENGL_ARRAY) -> None: ...
    def glIsQuery(self, id: int) -> int: ...
    def glBeginQuery(self, target: int, id: int) -> None: ...
    def glEndQuery(self, target: int) -> None: ...
    def glGetQueryiv(self, target: int, pname: int) -> typing.Optional[int]: ...
    def glBindBuffer(self, target: int, buffer: int) -> None: ...
    def glDeleteBuffers(self, n: int, buffers: PYQT_OPENGL_ARRAY) -> None: ...
    def glGenBuffers(self, n: int) -> typing.Optional[typing.Union[int, tuple[int, ...]]]: ...
    def glIsBuffer(self, buffer: int) -> int: ...
    def glBufferData(self, target: int, size: int, data: PYQT_OPENGL_ARRAY, usage: int) -> None: ...
    def glBufferSubData(self, target: int, offset: int, size: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glUnmapBuffer(self, target: int) -> int: ...
    def glGetBufferParameteriv(self, target: int, pname: int) -> typing.Optional[int]: ...
    def glBlendFuncSeparate(self, sfactorRGB: int, dfactorRGB: int, sfactorAlpha: int, dfactorAlpha: int) -> None: ...
    def glPointParameterf(self, pname: int, param: float) -> None: ...
    def glPointParameterfv(self, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glPointParameteri(self, pname: int, param: int) -> None: ...
    def glPointParameteriv(self, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glActiveTexture(self, texture: int) -> None: ...
    def glSampleCoverage(self, value: float, invert: int) -> None: ...
    def glCompressedTexImage3D(self, target: int, level: int, internalformat: int, width: int, height: int, depth: int, border: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexImage2D(self, target: int, level: int, internalformat: int, width: int, height: int, border: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexImage1D(self, target: int, level: int, internalformat: int, width: int, border: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexSubImage3D(self, target: int, level: int, xoffset: int, yoffset: int, zoffset: int, width: int, height: int, depth: int, format: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexSubImage2D(self, target: int, level: int, xoffset: int, yoffset: int, width: int, height: int, format: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexSubImage1D(self, target: int, level: int, xoffset: int, width: int, format: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glBlendColor(self, red: float, green: float, blue: float, alpha: float) -> None: ...
    def glBlendEquation(self, mode: int) -> None: ...
    def glDrawRangeElements(self, mode: int, start: int, end: int, count: int, type: int, indices: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexImage3D(self, target: int, level: int, internalformat: int, width: int, height: int, depth: int, border: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexSubImage3D(self, target: int, level: int, xoffset: int, yoffset: int, zoffset: int, width: int, height: int, depth: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glCopyTexSubImage3D(self, target: int, level: int, xoffset: int, yoffset: int, zoffset: int, x: int, y: int, width: int, height: int) -> None: ...
    def glDrawArrays(self, mode: int, first: int, count: int) -> None: ...
    def glDrawElements(self, mode: int, count: int, type: int, indices: PYQT_OPENGL_ARRAY) -> None: ...
    def glPolygonOffset(self, factor: float, units: float) -> None: ...
    def glCopyTexImage1D(self, target: int, level: int, internalformat: int, x: int, y: int, width: int, border: int) -> None: ...
    def glCopyTexImage2D(self, target: int, level: int, internalformat: int, x: int, y: int, width: int, height: int, border: int) -> None: ...
    def glCopyTexSubImage1D(self, target: int, level: int, xoffset: int, x: int, y: int, width: int) -> None: ...
    def glCopyTexSubImage2D(self, target: int, level: int, xoffset: int, yoffset: int, x: int, y: int, width: int, height: int) -> None: ...
    def glTexSubImage1D(self, target: int, level: int, xoffset: int, width: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexSubImage2D(self, target: int, level: int, xoffset: int, yoffset: int, width: int, height: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glBindTexture(self, target: int, texture: int) -> None: ...
    def glDeleteTextures(self, n: int, textures: PYQT_OPENGL_ARRAY) -> None: ...
    def glGenTextures(self, n: int) -> typing.Optional[typing.Union[int, tuple[int, ...]]]: ...
    def glIsTexture(self, texture: int) -> int: ...
    def glIndexub(self, c: int) -> None: ...
    def glIndexubv(self, c: PYQT_OPENGL_ARRAY) -> None: ...
    def glCullFace(self, mode: int) -> None: ...
    def glFrontFace(self, mode: int) -> None: ...
    def glHint(self, target: int, mode: int) -> None: ...
    def glLineWidth(self, width: float) -> None: ...
    def glPointSize(self, size: float) -> None: ...
    def glPolygonMode(self, face: int, mode: int) -> None: ...
    def glScissor(self, x: int, y: int, width: int, height: int) -> None: ...
    def glTexParameterf(self, target: int, pname: int, param: float) -> None: ...
    def glTexParameterfv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexParameteri(self, target: int, pname: int, param: int) -> None: ...
    def glTexParameteriv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexImage1D(self, target: int, level: int, internalformat: int, width: int, border: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexImage2D(self, target: int, level: int, internalformat: int, width: int, height: int, border: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glDrawBuffer(self, mode: int) -> None: ...
    def glClear(self, mask: int) -> None: ...
    def glClearColor(self, red: float, green: float, blue: float, alpha: float) -> None: ...
    def glClearStencil(self, s: int) -> None: ...
    def glClearDepth(self, depth: float) -> None: ...
    def glStencilMask(self, mask: int) -> None: ...
    def glColorMask(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glDepthMask(self, flag: int) -> None: ...
    def glDisable(self, cap: int) -> None: ...
    def glEnable(self, cap: int) -> None: ...
    def glFinish(self) -> None: ...
    def glFlush(self) -> None: ...
    def glBlendFunc(self, sfactor: int, dfactor: int) -> None: ...
    def glLogicOp(self, opcode: int) -> None: ...
    def glStencilFunc(self, func: int, ref: int, mask: int) -> None: ...
    def glStencilOp(self, fail: int, zfail: int, zpass: int) -> None: ...
    def glDepthFunc(self, func: int) -> None: ...
    def glPixelStoref(self, pname: int, param: float) -> None: ...
    def glPixelStorei(self, pname: int, param: int) -> None: ...
    def glReadBuffer(self, mode: int) -> None: ...
    def glReadPixels(self, x: int, y: int, width: int, height: int, format: int, type: int) -> typing.Union[tuple[float, ...], tuple[int, ...]]: ...
    def glGetBooleanv(self, pname: int) -> typing.Optional[typing.Union[bool, tuple[bool, ...]]]: ...
    def glGetDoublev(self, pname: int) -> typing.Optional[typing.Union[float, tuple[float, ...]]]: ...
    def glGetError(self) -> int: ...
    def glGetFloatv(self, pname: int) -> typing.Optional[typing.Union[float, tuple[float, ...]]]: ...
    def glGetIntegerv(self, pname: int) -> typing.Optional[typing.Union[int, tuple[int, ...]]]: ...
    def glGetString(self, name: int) -> typing.Optional[str]: ...
    def glGetTexParameterfv(self, target: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetTexParameteriv(self, target: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int, int]]]: ...
    def glGetTexLevelParameterfv(self, target: int, level: int, pname: int) -> typing.Optional[float]: ...
    def glGetTexLevelParameteriv(self, target: int, level: int, pname: int) -> typing.Optional[int]: ...
    def glIsEnabled(self, cap: int) -> int: ...
    def glDepthRange(self, nearVal: float, farVal: float) -> None: ...
    def glViewport(self, x: int, y: int, width: int, height: int) -> None: ...
    def initializeOpenGLFunctions(self) -> bool: ...


class QOpenGLFunctions_2_1(QAbstractOpenGLFunctions):

    def __init__(self) -> None: ...

    def glVertexAttrib1d(self, index: int, x: float) -> None: ...
    def glVertexAttrib1dv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib1f(self, index: int, x: float) -> None: ...
    def glVertexAttrib1fv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib1s(self, index: int, x: int) -> None: ...
    def glVertexAttrib1sv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib2d(self, index: int, x: float, y: float) -> None: ...
    def glVertexAttrib2dv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib2f(self, index: int, x: float, y: float) -> None: ...
    def glVertexAttrib2fv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib2s(self, index: int, x: int, y: int) -> None: ...
    def glVertexAttrib2sv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib3d(self, index: int, x: float, y: float, z: float) -> None: ...
    def glVertexAttrib3dv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib3f(self, index: int, x: float, y: float, z: float) -> None: ...
    def glVertexAttrib3fv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib3s(self, index: int, x: int, y: int, z: int) -> None: ...
    def glVertexAttrib3sv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4Nbv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4Niv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4Nsv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4Nub(self, index: int, x: int, y: int, z: int, w: int) -> None: ...
    def glVertexAttrib4Nubv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4Nuiv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4Nusv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4bv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4d(self, index: int, x: float, y: float, z: float, w: float) -> None: ...
    def glVertexAttrib4dv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4f(self, index: int, x: float, y: float, z: float, w: float) -> None: ...
    def glVertexAttrib4fv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4iv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4s(self, index: int, x: int, y: int, z: int, w: int) -> None: ...
    def glVertexAttrib4sv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4ubv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4uiv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertexAttrib4usv(self, index: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glFogCoordf(self, coord: float) -> None: ...
    def glFogCoordfv(self, coord: PYQT_OPENGL_ARRAY) -> None: ...
    def glFogCoordd(self, coord: float) -> None: ...
    def glFogCoorddv(self, coord: PYQT_OPENGL_ARRAY) -> None: ...
    def glFogCoordPointer(self, type: int, stride: int, pointer: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3b(self, red: int, green: int, blue: int) -> None: ...
    def glSecondaryColor3bv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3d(self, red: float, green: float, blue: float) -> None: ...
    def glSecondaryColor3dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3f(self, red: float, green: float, blue: float) -> None: ...
    def glSecondaryColor3fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3i(self, red: int, green: int, blue: int) -> None: ...
    def glSecondaryColor3iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3s(self, red: int, green: int, blue: int) -> None: ...
    def glSecondaryColor3sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3ub(self, red: int, green: int, blue: int) -> None: ...
    def glSecondaryColor3ubv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3ui(self, red: int, green: int, blue: int) -> None: ...
    def glSecondaryColor3uiv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColor3us(self, red: int, green: int, blue: int) -> None: ...
    def glSecondaryColor3usv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glSecondaryColorPointer(self, size: int, type: int, stride: int, pointer: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos2d(self, x: float, y: float) -> None: ...
    def glWindowPos2dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos2f(self, x: float, y: float) -> None: ...
    def glWindowPos2fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos2i(self, x: int, y: int) -> None: ...
    def glWindowPos2iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos2s(self, x: int, y: int) -> None: ...
    def glWindowPos2sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos3d(self, x: float, y: float, z: float) -> None: ...
    def glWindowPos3dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos3f(self, x: float, y: float, z: float) -> None: ...
    def glWindowPos3fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos3i(self, x: int, y: int, z: int) -> None: ...
    def glWindowPos3iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glWindowPos3s(self, x: int, y: int, z: int) -> None: ...
    def glWindowPos3sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glClientActiveTexture(self, texture: int) -> None: ...
    def glMultiTexCoord1d(self, target: int, s: float) -> None: ...
    def glMultiTexCoord1dv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord1f(self, target: int, s: float) -> None: ...
    def glMultiTexCoord1fv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord1i(self, target: int, s: int) -> None: ...
    def glMultiTexCoord1iv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord1s(self, target: int, s: int) -> None: ...
    def glMultiTexCoord1sv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord2d(self, target: int, s: float, t: float) -> None: ...
    def glMultiTexCoord2dv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord2f(self, target: int, s: float, t: float) -> None: ...
    def glMultiTexCoord2fv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord2i(self, target: int, s: int, t: int) -> None: ...
    def glMultiTexCoord2iv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord2s(self, target: int, s: int, t: int) -> None: ...
    def glMultiTexCoord2sv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord3d(self, target: int, s: float, t: float, r: float) -> None: ...
    def glMultiTexCoord3dv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord3f(self, target: int, s: float, t: float, r: float) -> None: ...
    def glMultiTexCoord3fv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord3i(self, target: int, s: int, t: int, r: int) -> None: ...
    def glMultiTexCoord3iv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord3s(self, target: int, s: int, t: int, r: int) -> None: ...
    def glMultiTexCoord3sv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord4d(self, target: int, s: float, t: float, r: float, q: float) -> None: ...
    def glMultiTexCoord4dv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord4f(self, target: int, s: float, t: float, r: float, q: float) -> None: ...
    def glMultiTexCoord4fv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord4i(self, target: int, s: int, t: int, r: int, q: int) -> None: ...
    def glMultiTexCoord4iv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultiTexCoord4s(self, target: int, s: int, t: int, r: int, q: int) -> None: ...
    def glMultiTexCoord4sv(self, target: int, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glLoadTransposeMatrixf(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glLoadTransposeMatrixd(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultTransposeMatrixf(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultTransposeMatrixd(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glColorTable(self, target: int, internalformat: int, width: int, format: int, type: int, table: PYQT_OPENGL_ARRAY) -> None: ...
    def glColorTableParameterfv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glColorTableParameteriv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glCopyColorTable(self, target: int, internalformat: int, x: int, y: int, width: int) -> None: ...
    def glGetColorTableParameterfv(self, target: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetColorTableParameteriv(self, target: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int, int]]]: ...
    def glColorSubTable(self, target: int, start: int, count: int, format: int, type: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCopyColorSubTable(self, target: int, start: int, x: int, y: int, width: int) -> None: ...
    def glConvolutionFilter1D(self, target: int, internalformat: int, width: int, format: int, type: int, image: PYQT_OPENGL_ARRAY) -> None: ...
    def glConvolutionFilter2D(self, target: int, internalformat: int, width: int, height: int, format: int, type: int, image: PYQT_OPENGL_ARRAY) -> None: ...
    def glConvolutionParameterf(self, target: int, pname: int, params: float) -> None: ...
    def glConvolutionParameterfv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glConvolutionParameteri(self, target: int, pname: int, params: int) -> None: ...
    def glConvolutionParameteriv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glCopyConvolutionFilter1D(self, target: int, internalformat: int, x: int, y: int, width: int) -> None: ...
    def glCopyConvolutionFilter2D(self, target: int, internalformat: int, x: int, y: int, width: int, height: int) -> None: ...
    def glGetConvolutionParameterfv(self, target: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetConvolutionParameteriv(self, target: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int, int]]]: ...
    def glHistogram(self, target: int, width: int, internalformat: int, sink: int) -> None: ...
    def glMinmax(self, target: int, internalformat: int, sink: int) -> None: ...
    def glResetHistogram(self, target: int) -> None: ...
    def glResetMinmax(self, target: int) -> None: ...
    def glArrayElement(self, i: int) -> None: ...
    def glColorPointer(self, size: int, type: int, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glDisableClientState(self, array: int) -> None: ...
    def glEdgeFlagPointer(self, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glEnableClientState(self, array: int) -> None: ...
    def glIndexPointer(self, type: int, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glNormalPointer(self, type: int, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glTexCoordPointer(self, size: int, type: int, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glVertexPointer(self, size: int, type: int, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glPopClientAttrib(self) -> None: ...
    def glPushClientAttrib(self, mask: int) -> None: ...
    def glNewList(self, list: int, mode: int) -> None: ...
    def glEndList(self) -> None: ...
    def glCallList(self, list: int) -> None: ...
    def glDeleteLists(self, list: int, range: int) -> None: ...
    def glGenLists(self, range: int) -> int: ...
    def glListBase(self, base: int) -> None: ...
    def glBegin(self, mode: int) -> None: ...
    def glBitmap(self, width: int, height: int, xorig: float, yorig: float, xmove: float, ymove: float, bitmap: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3b(self, red: int, green: int, blue: int) -> None: ...
    def glColor3bv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3d(self, red: float, green: float, blue: float) -> None: ...
    def glColor3dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3f(self, red: float, green: float, blue: float) -> None: ...
    def glColor3fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3i(self, red: int, green: int, blue: int) -> None: ...
    def glColor3iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3s(self, red: int, green: int, blue: int) -> None: ...
    def glColor3sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3ub(self, red: int, green: int, blue: int) -> None: ...
    def glColor3ubv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3ui(self, red: int, green: int, blue: int) -> None: ...
    def glColor3uiv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor3us(self, red: int, green: int, blue: int) -> None: ...
    def glColor3usv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4b(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glColor4bv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4d(self, red: float, green: float, blue: float, alpha: float) -> None: ...
    def glColor4dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4f(self, red: float, green: float, blue: float, alpha: float) -> None: ...
    def glColor4fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4i(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glColor4iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4s(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glColor4sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4ub(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glColor4ubv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4ui(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glColor4uiv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glColor4us(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glColor4usv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glEdgeFlag(self, flag: int) -> None: ...
    def glEdgeFlagv(self, flag: PYQT_OPENGL_ARRAY) -> None: ...
    def glEnd(self) -> None: ...
    def glIndexd(self, c: float) -> None: ...
    def glIndexdv(self, c: PYQT_OPENGL_ARRAY) -> None: ...
    def glIndexf(self, c: float) -> None: ...
    def glIndexfv(self, c: PYQT_OPENGL_ARRAY) -> None: ...
    def glIndexi(self, c: int) -> None: ...
    def glIndexiv(self, c: PYQT_OPENGL_ARRAY) -> None: ...
    def glIndexs(self, c: int) -> None: ...
    def glIndexsv(self, c: PYQT_OPENGL_ARRAY) -> None: ...
    def glNormal3b(self, nx: int, ny: int, nz: int) -> None: ...
    def glNormal3bv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glNormal3d(self, nx: float, ny: float, nz: float) -> None: ...
    def glNormal3dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glNormal3f(self, nx: float, ny: float, nz: float) -> None: ...
    def glNormal3fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glNormal3i(self, nx: int, ny: int, nz: int) -> None: ...
    def glNormal3iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glNormal3s(self, nx: int, ny: int, nz: int) -> None: ...
    def glNormal3sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos2d(self, x: float, y: float) -> None: ...
    def glRasterPos2dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos2f(self, x: float, y: float) -> None: ...
    def glRasterPos2fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos2i(self, x: int, y: int) -> None: ...
    def glRasterPos2iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos2s(self, x: int, y: int) -> None: ...
    def glRasterPos2sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos3d(self, x: float, y: float, z: float) -> None: ...
    def glRasterPos3dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos3f(self, x: float, y: float, z: float) -> None: ...
    def glRasterPos3fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos3i(self, x: int, y: int, z: int) -> None: ...
    def glRasterPos3iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos3s(self, x: int, y: int, z: int) -> None: ...
    def glRasterPos3sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos4d(self, x: float, y: float, z: float, w: float) -> None: ...
    def glRasterPos4dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos4f(self, x: float, y: float, z: float, w: float) -> None: ...
    def glRasterPos4fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos4i(self, x: int, y: int, z: int, w: int) -> None: ...
    def glRasterPos4iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRasterPos4s(self, x: int, y: int, z: int, w: int) -> None: ...
    def glRasterPos4sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glRectd(self, x1: float, y1: float, x2: float, y2: float) -> None: ...
    def glRectf(self, x1: float, y1: float, x2: float, y2: float) -> None: ...
    def glRecti(self, x1: int, y1: int, x2: int, y2: int) -> None: ...
    def glRects(self, x1: int, y1: int, x2: int, y2: int) -> None: ...
    def glTexCoord1d(self, s: float) -> None: ...
    def glTexCoord1dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord1f(self, s: float) -> None: ...
    def glTexCoord1fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord1i(self, s: int) -> None: ...
    def glTexCoord1iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord1s(self, s: int) -> None: ...
    def glTexCoord1sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord2d(self, s: float, t: float) -> None: ...
    def glTexCoord2dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord2f(self, s: float, t: float) -> None: ...
    def glTexCoord2fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord2i(self, s: int, t: int) -> None: ...
    def glTexCoord2iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord2s(self, s: int, t: int) -> None: ...
    def glTexCoord2sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord3d(self, s: float, t: float, r: float) -> None: ...
    def glTexCoord3dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord3f(self, s: float, t: float, r: float) -> None: ...
    def glTexCoord3fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord3i(self, s: int, t: int, r: int) -> None: ...
    def glTexCoord3iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord3s(self, s: int, t: int, r: int) -> None: ...
    def glTexCoord3sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord4d(self, s: float, t: float, r: float, q: float) -> None: ...
    def glTexCoord4dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord4f(self, s: float, t: float, r: float, q: float) -> None: ...
    def glTexCoord4fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord4i(self, s: int, t: int, r: int, q: int) -> None: ...
    def glTexCoord4iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexCoord4s(self, s: int, t: int, r: int, q: int) -> None: ...
    def glTexCoord4sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex2d(self, x: float, y: float) -> None: ...
    def glVertex2dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex2f(self, x: float, y: float) -> None: ...
    def glVertex2fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex2i(self, x: int, y: int) -> None: ...
    def glVertex2iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex2s(self, x: int, y: int) -> None: ...
    def glVertex2sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex3d(self, x: float, y: float, z: float) -> None: ...
    def glVertex3dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex3f(self, x: float, y: float, z: float) -> None: ...
    def glVertex3fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex3i(self, x: int, y: int, z: int) -> None: ...
    def glVertex3iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex3s(self, x: int, y: int, z: int) -> None: ...
    def glVertex3sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex4d(self, x: float, y: float, z: float, w: float) -> None: ...
    def glVertex4dv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex4f(self, x: float, y: float, z: float, w: float) -> None: ...
    def glVertex4fv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex4i(self, x: int, y: int, z: int, w: int) -> None: ...
    def glVertex4iv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glVertex4s(self, x: int, y: int, z: int, w: int) -> None: ...
    def glVertex4sv(self, v: PYQT_OPENGL_ARRAY) -> None: ...
    def glClipPlane(self, plane: int, equation: PYQT_OPENGL_ARRAY) -> None: ...
    def glColorMaterial(self, face: int, mode: int) -> None: ...
    def glFogf(self, pname: int, param: float) -> None: ...
    def glFogfv(self, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glFogi(self, pname: int, param: int) -> None: ...
    def glFogiv(self, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glLightf(self, light: int, pname: int, param: float) -> None: ...
    def glLightfv(self, light: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glLighti(self, light: int, pname: int, param: int) -> None: ...
    def glLightiv(self, light: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glLightModelf(self, pname: int, param: float) -> None: ...
    def glLightModelfv(self, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glLightModeli(self, pname: int, param: int) -> None: ...
    def glLightModeliv(self, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glLineStipple(self, factor: int, pattern: int) -> None: ...
    def glMaterialf(self, face: int, pname: int, param: float) -> None: ...
    def glMaterialfv(self, face: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glMateriali(self, face: int, pname: int, param: int) -> None: ...
    def glMaterialiv(self, face: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glPolygonStipple(self, mask: PYQT_OPENGL_ARRAY) -> None: ...
    def glShadeModel(self, mode: int) -> None: ...
    def glTexEnvf(self, target: int, pname: int, param: float) -> None: ...
    def glTexEnvfv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexEnvi(self, target: int, pname: int, param: int) -> None: ...
    def glTexEnviv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexGend(self, coord: int, pname: int, param: float) -> None: ...
    def glTexGendv(self, coord: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexGenf(self, coord: int, pname: int, param: float) -> None: ...
    def glTexGenfv(self, coord: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexGeni(self, coord: int, pname: int, param: int) -> None: ...
    def glTexGeniv(self, coord: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glRenderMode(self, mode: int) -> int: ...
    def glInitNames(self) -> None: ...
    def glLoadName(self, name: int) -> None: ...
    def glPassThrough(self, token: float) -> None: ...
    def glPopName(self) -> None: ...
    def glPushName(self, name: int) -> None: ...
    def glClearAccum(self, red: float, green: float, blue: float, alpha: float) -> None: ...
    def glClearIndex(self, c: float) -> None: ...
    def glIndexMask(self, mask: int) -> None: ...
    def glAccum(self, op: int, value: float) -> None: ...
    def glPopAttrib(self) -> None: ...
    def glPushAttrib(self, mask: int) -> None: ...
    def glMap1d(self, target: int, u1: float, u2: float, stride: int, order: int, points: PYQT_OPENGL_ARRAY) -> None: ...
    def glMap1f(self, target: int, u1: float, u2: float, stride: int, order: int, points: PYQT_OPENGL_ARRAY) -> None: ...
    def glMap2d(self, target: int, u1: float, u2: float, ustride: int, uorder: int, v1: float, v2: float, vstride: int, vorder: int, points: PYQT_OPENGL_ARRAY) -> None: ...
    def glMap2f(self, target: int, u1: float, u2: float, ustride: int, uorder: int, v1: float, v2: float, vstride: int, vorder: int, points: PYQT_OPENGL_ARRAY) -> None: ...
    def glMapGrid1d(self, un: int, u1: float, u2: float) -> None: ...
    def glMapGrid1f(self, un: int, u1: float, u2: float) -> None: ...
    def glMapGrid2d(self, un: int, u1: float, u2: float, vn: int, v1: float, v2: float) -> None: ...
    def glMapGrid2f(self, un: int, u1: float, u2: float, vn: int, v1: float, v2: float) -> None: ...
    def glEvalCoord1d(self, u: float) -> None: ...
    def glEvalCoord1dv(self, u: PYQT_OPENGL_ARRAY) -> None: ...
    def glEvalCoord1f(self, u: float) -> None: ...
    def glEvalCoord1fv(self, u: PYQT_OPENGL_ARRAY) -> None: ...
    def glEvalCoord2d(self, u: float, v: float) -> None: ...
    def glEvalCoord2dv(self, u: PYQT_OPENGL_ARRAY) -> None: ...
    def glEvalCoord2f(self, u: float, v: float) -> None: ...
    def glEvalCoord2fv(self, u: PYQT_OPENGL_ARRAY) -> None: ...
    def glEvalMesh1(self, mode: int, i1: int, i2: int) -> None: ...
    def glEvalPoint1(self, i: int) -> None: ...
    def glEvalMesh2(self, mode: int, i1: int, i2: int, j1: int, j2: int) -> None: ...
    def glEvalPoint2(self, i: int, j: int) -> None: ...
    def glAlphaFunc(self, func: int, ref: float) -> None: ...
    def glPixelZoom(self, xfactor: float, yfactor: float) -> None: ...
    def glPixelTransferf(self, pname: int, param: float) -> None: ...
    def glPixelTransferi(self, pname: int, param: int) -> None: ...
    def glPixelMapfv(self, map: int, mapsize: int, values: PYQT_OPENGL_ARRAY) -> None: ...
    def glPixelMapuiv(self, map: int, mapsize: int, values: PYQT_OPENGL_ARRAY) -> None: ...
    def glPixelMapusv(self, map: int, mapsize: int, values: PYQT_OPENGL_ARRAY) -> None: ...
    def glCopyPixels(self, x: int, y: int, width: int, height: int, type: int) -> None: ...
    def glDrawPixels(self, width: int, height: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glGetClipPlane(self, plane: int) -> typing.Optional[tuple[float, float, float, float]]: ...
    def glGetLightfv(self, light: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float], tuple[float, float, float, float]]]: ...
    def glGetLightiv(self, light: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int], tuple[int, int, int, int]]]: ...
    def glGetMaterialfv(self, face: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float], tuple[float, float, float, float]]]: ...
    def glGetMaterialiv(self, face: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int], tuple[int, int, int, int]]]: ...
    def glGetTexEnvfv(self, target: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetTexEnviv(self, target: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int, int]]]: ...
    def glGetTexGendv(self, coord: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetTexGenfv(self, coord: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetTexGeniv(self, coord: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int, int]]]: ...
    def glIsList(self, list: int) -> int: ...
    def glFrustum(self, left: float, right: float, bottom: float, top: float, zNear: float, zFar: float) -> None: ...
    def glLoadIdentity(self) -> None: ...
    def glLoadMatrixf(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glLoadMatrixd(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glMatrixMode(self, mode: int) -> None: ...
    def glMultMatrixf(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glMultMatrixd(self, m: PYQT_OPENGL_ARRAY) -> None: ...
    def glOrtho(self, left: float, right: float, bottom: float, top: float, zNear: float, zFar: float) -> None: ...
    def glPopMatrix(self) -> None: ...
    def glPushMatrix(self) -> None: ...
    def glRotated(self, angle: float, x: float, y: float, z: float) -> None: ...
    def glRotatef(self, angle: float, x: float, y: float, z: float) -> None: ...
    def glScaled(self, x: float, y: float, z: float) -> None: ...
    def glScalef(self, x: float, y: float, z: float) -> None: ...
    def glTranslated(self, x: float, y: float, z: float) -> None: ...
    def glTranslatef(self, x: float, y: float, z: float) -> None: ...
    def glBlendEquationSeparate(self, modeRGB: int, modeAlpha: int) -> None: ...
    def glDrawBuffers(self, n: int, bufs: PYQT_OPENGL_ARRAY) -> None: ...
    def glStencilOpSeparate(self, face: int, sfail: int, dpfail: int, dppass: int) -> None: ...
    def glStencilFuncSeparate(self, face: int, func: int, ref: int, mask: int) -> None: ...
    def glStencilMaskSeparate(self, face: int, mask: int) -> None: ...
    def glAttachShader(self, program: int, shader: int) -> None: ...
    def glBindAttribLocation(self, program: int, index: int, name: typing.Optional[str]) -> None: ...
    def glCompileShader(self, shader: int) -> None: ...
    def glCreateProgram(self) -> int: ...
    def glCreateShader(self, type: int) -> int: ...
    def glDeleteProgram(self, program: int) -> None: ...
    def glDeleteShader(self, shader: int) -> None: ...
    def glDetachShader(self, program: int, shader: int) -> None: ...
    def glDisableVertexAttribArray(self, index: int) -> None: ...
    def glEnableVertexAttribArray(self, index: int) -> None: ...
    def glGetActiveAttrib(self, program: int, index: int) -> tuple[str, int, int]: ...
    def glGetActiveUniform(self, program: int, index: int) -> tuple[str, int, int]: ...
    def glGetAttachedShaders(self, program: int) -> tuple[int, ...]: ...
    def glGetAttribLocation(self, program: int, name: typing.Optional[str]) -> int: ...
    def glGetProgramiv(self, program: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int]]]: ...
    def glGetProgramInfoLog(self, program: int) -> bytes: ...
    def glGetShaderiv(self, shader: int, pname: int) -> typing.Optional[int]: ...
    def glGetShaderInfoLog(self, shader: int) -> bytes: ...
    def glGetShaderSource(self, shader: int) -> bytes: ...
    def glGetUniformLocation(self, program: int, name: typing.Optional[str]) -> int: ...
    def glGetVertexAttribdv(self, index: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetVertexAttribfv(self, index: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetVertexAttribiv(self, index: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int, int]]]: ...
    def glIsProgram(self, program: int) -> int: ...
    def glIsShader(self, shader: int) -> int: ...
    def glLinkProgram(self, program: int) -> None: ...
    def glUseProgram(self, program: int) -> None: ...
    def glUniform1f(self, location: int, v0: float) -> None: ...
    def glUniform2f(self, location: int, v0: float, v1: float) -> None: ...
    def glUniform3f(self, location: int, v0: float, v1: float, v2: float) -> None: ...
    def glUniform4f(self, location: int, v0: float, v1: float, v2: float, v3: float) -> None: ...
    def glUniform1i(self, location: int, v0: int) -> None: ...
    def glUniform2i(self, location: int, v0: int, v1: int) -> None: ...
    def glUniform3i(self, location: int, v0: int, v1: int, v2: int) -> None: ...
    def glUniform4i(self, location: int, v0: int, v1: int, v2: int, v3: int) -> None: ...
    def glUniform1fv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform2fv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform3fv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform4fv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform1iv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform2iv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform3iv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform4iv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniformMatrix2fv(self, location: int, count: int, transpose: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniformMatrix3fv(self, location: int, count: int, transpose: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniformMatrix4fv(self, location: int, count: int, transpose: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glValidateProgram(self, program: int) -> None: ...
    def glVertexAttribPointer(self, index: int, size: int, type: int, normalized: int, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glGenQueries(self, n: int) -> typing.Optional[typing.Union[int, tuple[int, ...]]]: ...
    def glDeleteQueries(self, n: int, ids: PYQT_OPENGL_ARRAY) -> None: ...
    def glIsQuery(self, id: int) -> int: ...
    def glBeginQuery(self, target: int, id: int) -> None: ...
    def glEndQuery(self, target: int) -> None: ...
    def glGetQueryiv(self, target: int, pname: int) -> typing.Optional[int]: ...
    def glBindBuffer(self, target: int, buffer: int) -> None: ...
    def glDeleteBuffers(self, n: int, buffers: PYQT_OPENGL_ARRAY) -> None: ...
    def glGenBuffers(self, n: int) -> typing.Optional[typing.Union[int, tuple[int, ...]]]: ...
    def glIsBuffer(self, buffer: int) -> int: ...
    def glBufferData(self, target: int, size: int, data: PYQT_OPENGL_ARRAY, usage: int) -> None: ...
    def glBufferSubData(self, target: int, offset: int, size: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glUnmapBuffer(self, target: int) -> int: ...
    def glGetBufferParameteriv(self, target: int, pname: int) -> typing.Optional[int]: ...
    def glBlendFuncSeparate(self, sfactorRGB: int, dfactorRGB: int, sfactorAlpha: int, dfactorAlpha: int) -> None: ...
    def glPointParameterf(self, pname: int, param: float) -> None: ...
    def glPointParameterfv(self, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glPointParameteri(self, pname: int, param: int) -> None: ...
    def glPointParameteriv(self, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glActiveTexture(self, texture: int) -> None: ...
    def glSampleCoverage(self, value: float, invert: int) -> None: ...
    def glCompressedTexImage3D(self, target: int, level: int, internalformat: int, width: int, height: int, depth: int, border: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexImage2D(self, target: int, level: int, internalformat: int, width: int, height: int, border: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexImage1D(self, target: int, level: int, internalformat: int, width: int, border: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexSubImage3D(self, target: int, level: int, xoffset: int, yoffset: int, zoffset: int, width: int, height: int, depth: int, format: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexSubImage2D(self, target: int, level: int, xoffset: int, yoffset: int, width: int, height: int, format: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexSubImage1D(self, target: int, level: int, xoffset: int, width: int, format: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glBlendColor(self, red: float, green: float, blue: float, alpha: float) -> None: ...
    def glBlendEquation(self, mode: int) -> None: ...
    def glDrawRangeElements(self, mode: int, start: int, end: int, count: int, type: int, indices: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexImage3D(self, target: int, level: int, internalformat: int, width: int, height: int, depth: int, border: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexSubImage3D(self, target: int, level: int, xoffset: int, yoffset: int, zoffset: int, width: int, height: int, depth: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glCopyTexSubImage3D(self, target: int, level: int, xoffset: int, yoffset: int, zoffset: int, x: int, y: int, width: int, height: int) -> None: ...
    def glDrawArrays(self, mode: int, first: int, count: int) -> None: ...
    def glDrawElements(self, mode: int, count: int, type: int, indices: PYQT_OPENGL_ARRAY) -> None: ...
    def glPolygonOffset(self, factor: float, units: float) -> None: ...
    def glCopyTexImage1D(self, target: int, level: int, internalformat: int, x: int, y: int, width: int, border: int) -> None: ...
    def glCopyTexImage2D(self, target: int, level: int, internalformat: int, x: int, y: int, width: int, height: int, border: int) -> None: ...
    def glCopyTexSubImage1D(self, target: int, level: int, xoffset: int, x: int, y: int, width: int) -> None: ...
    def glCopyTexSubImage2D(self, target: int, level: int, xoffset: int, yoffset: int, x: int, y: int, width: int, height: int) -> None: ...
    def glTexSubImage1D(self, target: int, level: int, xoffset: int, width: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexSubImage2D(self, target: int, level: int, xoffset: int, yoffset: int, width: int, height: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glBindTexture(self, target: int, texture: int) -> None: ...
    def glDeleteTextures(self, n: int, textures: PYQT_OPENGL_ARRAY) -> None: ...
    def glGenTextures(self, n: int) -> typing.Optional[typing.Union[int, tuple[int, ...]]]: ...
    def glIsTexture(self, texture: int) -> int: ...
    def glIndexub(self, c: int) -> None: ...
    def glIndexubv(self, c: PYQT_OPENGL_ARRAY) -> None: ...
    def glCullFace(self, mode: int) -> None: ...
    def glFrontFace(self, mode: int) -> None: ...
    def glHint(self, target: int, mode: int) -> None: ...
    def glLineWidth(self, width: float) -> None: ...
    def glPointSize(self, size: float) -> None: ...
    def glPolygonMode(self, face: int, mode: int) -> None: ...
    def glScissor(self, x: int, y: int, width: int, height: int) -> None: ...
    def glTexParameterf(self, target: int, pname: int, param: float) -> None: ...
    def glTexParameterfv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexParameteri(self, target: int, pname: int, param: int) -> None: ...
    def glTexParameteriv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexImage1D(self, target: int, level: int, internalformat: int, width: int, border: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexImage2D(self, target: int, level: int, internalformat: int, width: int, height: int, border: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glDrawBuffer(self, mode: int) -> None: ...
    def glClear(self, mask: int) -> None: ...
    def glClearColor(self, red: float, green: float, blue: float, alpha: float) -> None: ...
    def glClearStencil(self, s: int) -> None: ...
    def glClearDepth(self, depth: float) -> None: ...
    def glStencilMask(self, mask: int) -> None: ...
    def glColorMask(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glDepthMask(self, flag: int) -> None: ...
    def glDisable(self, cap: int) -> None: ...
    def glEnable(self, cap: int) -> None: ...
    def glFinish(self) -> None: ...
    def glFlush(self) -> None: ...
    def glBlendFunc(self, sfactor: int, dfactor: int) -> None: ...
    def glLogicOp(self, opcode: int) -> None: ...
    def glStencilFunc(self, func: int, ref: int, mask: int) -> None: ...
    def glStencilOp(self, fail: int, zfail: int, zpass: int) -> None: ...
    def glDepthFunc(self, func: int) -> None: ...
    def glPixelStoref(self, pname: int, param: float) -> None: ...
    def glPixelStorei(self, pname: int, param: int) -> None: ...
    def glReadBuffer(self, mode: int) -> None: ...
    def glReadPixels(self, x: int, y: int, width: int, height: int, format: int, type: int) -> typing.Union[tuple[float, ...], tuple[int, ...]]: ...
    def glGetBooleanv(self, pname: int) -> typing.Optional[typing.Union[bool, tuple[bool, ...]]]: ...
    def glGetDoublev(self, pname: int) -> typing.Optional[typing.Union[float, tuple[float, ...]]]: ...
    def glGetError(self) -> int: ...
    def glGetFloatv(self, pname: int) -> typing.Optional[typing.Union[float, tuple[float, ...]]]: ...
    def glGetIntegerv(self, pname: int) -> typing.Optional[typing.Union[int, tuple[int, ...]]]: ...
    def glGetString(self, name: int) -> typing.Optional[str]: ...
    def glGetTexParameterfv(self, target: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetTexParameteriv(self, target: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int, int]]]: ...
    def glGetTexLevelParameterfv(self, target: int, level: int, pname: int) -> typing.Optional[float]: ...
    def glGetTexLevelParameteriv(self, target: int, level: int, pname: int) -> typing.Optional[int]: ...
    def glIsEnabled(self, cap: int) -> int: ...
    def glDepthRange(self, nearVal: float, farVal: float) -> None: ...
    def glViewport(self, x: int, y: int, width: int, height: int) -> None: ...
    def initializeOpenGLFunctions(self) -> bool: ...


class QOpenGLFunctions_4_1_Core(QAbstractOpenGLFunctions):

    def __init__(self) -> None: ...

    def glReleaseShaderCompiler(self) -> None: ...
    def glDepthRangef(self, n: float, f: float) -> None: ...
    def glClearDepthf(self, dd: float) -> None: ...
    def glProgramParameteri(self, program: int, pname: int, value: int) -> None: ...
    def glUseProgramStages(self, pipeline: int, stages: int, program: int) -> None: ...
    def glActiveShaderProgram(self, pipeline: int, program: int) -> None: ...
    def glBindProgramPipeline(self, pipeline: int) -> None: ...
    def glIsProgramPipeline(self, pipeline: int) -> int: ...
    def glProgramUniform1i(self, program: int, location: int, v0: int) -> None: ...
    def glProgramUniform1f(self, program: int, location: int, v0: float) -> None: ...
    def glProgramUniform1d(self, program: int, location: int, v0: float) -> None: ...
    def glProgramUniform1ui(self, program: int, location: int, v0: int) -> None: ...
    def glProgramUniform2i(self, program: int, location: int, v0: int, v1: int) -> None: ...
    def glProgramUniform2f(self, program: int, location: int, v0: float, v1: float) -> None: ...
    def glProgramUniform2d(self, program: int, location: int, v0: float, v1: float) -> None: ...
    def glProgramUniform2ui(self, program: int, location: int, v0: int, v1: int) -> None: ...
    def glProgramUniform3i(self, program: int, location: int, v0: int, v1: int, v2: int) -> None: ...
    def glProgramUniform3f(self, program: int, location: int, v0: float, v1: float, v2: float) -> None: ...
    def glProgramUniform3d(self, program: int, location: int, v0: float, v1: float, v2: float) -> None: ...
    def glProgramUniform3ui(self, program: int, location: int, v0: int, v1: int, v2: int) -> None: ...
    def glProgramUniform4i(self, program: int, location: int, v0: int, v1: int, v2: int, v3: int) -> None: ...
    def glProgramUniform4f(self, program: int, location: int, v0: float, v1: float, v2: float, v3: float) -> None: ...
    def glProgramUniform4d(self, program: int, location: int, v0: float, v1: float, v2: float, v3: float) -> None: ...
    def glProgramUniform4ui(self, program: int, location: int, v0: int, v1: int, v2: int, v3: int) -> None: ...
    def glValidateProgramPipeline(self, pipeline: int) -> None: ...
    def glVertexAttribL1d(self, index: int, x: float) -> None: ...
    def glVertexAttribL2d(self, index: int, x: float, y: float) -> None: ...
    def glVertexAttribL3d(self, index: int, x: float, y: float, z: float) -> None: ...
    def glVertexAttribL4d(self, index: int, x: float, y: float, z: float, w: float) -> None: ...
    def glViewportIndexedf(self, index: int, x: float, y: float, w: float, h: float) -> None: ...
    def glScissorIndexed(self, index: int, left: int, bottom: int, width: int, height: int) -> None: ...
    def glDepthRangeIndexed(self, index: int, n: float, f: float) -> None: ...
    def glMinSampleShading(self, value: float) -> None: ...
    def glBlendEquationi(self, buf: int, mode: int) -> None: ...
    def glBlendEquationSeparatei(self, buf: int, modeRGB: int, modeAlpha: int) -> None: ...
    def glBlendFunci(self, buf: int, src: int, dst: int) -> None: ...
    def glBlendFuncSeparatei(self, buf: int, srcRGB: int, dstRGB: int, srcAlpha: int, dstAlpha: int) -> None: ...
    def glUniform1d(self, location: int, x: float) -> None: ...
    def glUniform2d(self, location: int, x: float, y: float) -> None: ...
    def glUniform3d(self, location: int, x: float, y: float, z: float) -> None: ...
    def glUniform4d(self, location: int, x: float, y: float, z: float, w: float) -> None: ...
    def glPatchParameteri(self, pname: int, value: int) -> None: ...
    def glBindTransformFeedback(self, target: int, id: int) -> None: ...
    def glIsTransformFeedback(self, id: int) -> int: ...
    def glPauseTransformFeedback(self) -> None: ...
    def glResumeTransformFeedback(self) -> None: ...
    def glDrawTransformFeedback(self, mode: int, id: int) -> None: ...
    def glDrawTransformFeedbackStream(self, mode: int, id: int, stream: int) -> None: ...
    def glBeginQueryIndexed(self, target: int, index: int, id: int) -> None: ...
    def glEndQueryIndexed(self, target: int, index: int) -> None: ...
    def glVertexAttribDivisor(self, index: int, divisor: int) -> None: ...
    def glIsSampler(self, sampler: int) -> int: ...
    def glBindSampler(self, unit: int, sampler: int) -> None: ...
    def glSamplerParameteri(self, sampler: int, pname: int, param: int) -> None: ...
    def glSamplerParameterf(self, sampler: int, pname: int, param: float) -> None: ...
    def glQueryCounter(self, id: int, target: int) -> None: ...
    def glVertexAttribP1ui(self, index: int, type: int, normalized: int, value: int) -> None: ...
    def glVertexAttribP2ui(self, index: int, type: int, normalized: int, value: int) -> None: ...
    def glVertexAttribP3ui(self, index: int, type: int, normalized: int, value: int) -> None: ...
    def glVertexAttribP4ui(self, index: int, type: int, normalized: int, value: int) -> None: ...
    def glFramebufferTexture(self, target: int, attachment: int, texture: int, level: int) -> None: ...
    def glProvokingVertex(self, mode: int) -> None: ...
    def glTexImage2DMultisample(self, target: int, samples: int, internalformat: int, width: int, height: int, fixedsamplelocations: int) -> None: ...
    def glTexImage3DMultisample(self, target: int, samples: int, internalformat: int, width: int, height: int, depth: int, fixedsamplelocations: int) -> None: ...
    def glSampleMaski(self, index: int, mask: int) -> None: ...
    def glDrawArraysInstanced(self, mode: int, first: int, count: int, instancecount: int) -> None: ...
    def glTexBuffer(self, target: int, internalformat: int, buffer: int) -> None: ...
    def glPrimitiveRestartIndex(self, index: int) -> None: ...
    def glUniformBlockBinding(self, program: int, uniformBlockIndex: int, uniformBlockBinding: int) -> None: ...
    def glColorMaski(self, index: int, r: int, g: int, b: int, a: int) -> None: ...
    def glEnablei(self, target: int, index: int) -> None: ...
    def glDisablei(self, target: int, index: int) -> None: ...
    def glIsEnabledi(self, target: int, index: int) -> int: ...
    def glBeginTransformFeedback(self, primitiveMode: int) -> None: ...
    def glEndTransformFeedback(self) -> None: ...
    def glBindBufferBase(self, target: int, index: int, buffer: int) -> None: ...
    def glClampColor(self, target: int, clamp: int) -> None: ...
    def glBeginConditionalRender(self, id: int, mode: int) -> None: ...
    def glEndConditionalRender(self) -> None: ...
    def glUniform1ui(self, location: int, v0: int) -> None: ...
    def glUniform2ui(self, location: int, v0: int, v1: int) -> None: ...
    def glUniform3ui(self, location: int, v0: int, v1: int, v2: int) -> None: ...
    def glUniform4ui(self, location: int, v0: int, v1: int, v2: int, v3: int) -> None: ...
    def glClearBufferfi(self, buffer: int, drawbuffer: int, depth: float, stencil: int) -> None: ...
    def glIsRenderbuffer(self, renderbuffer: int) -> int: ...
    def glBindRenderbuffer(self, target: int, renderbuffer: int) -> None: ...
    def glRenderbufferStorage(self, target: int, internalformat: int, width: int, height: int) -> None: ...
    def glIsFramebuffer(self, framebuffer: int) -> int: ...
    def glBindFramebuffer(self, target: int, framebuffer: int) -> None: ...
    def glCheckFramebufferStatus(self, target: int) -> int: ...
    def glFramebufferTexture1D(self, target: int, attachment: int, textarget: int, texture: int, level: int) -> None: ...
    def glFramebufferTexture2D(self, target: int, attachment: int, textarget: int, texture: int, level: int) -> None: ...
    def glFramebufferTexture3D(self, target: int, attachment: int, textarget: int, texture: int, level: int, zoffset: int) -> None: ...
    def glFramebufferRenderbuffer(self, target: int, attachment: int, renderbuffertarget: int, renderbuffer: int) -> None: ...
    def glGenerateMipmap(self, target: int) -> None: ...
    def glBlitFramebuffer(self, srcX0: int, srcY0: int, srcX1: int, srcY1: int, dstX0: int, dstY0: int, dstX1: int, dstY1: int, mask: int, filter: int) -> None: ...
    def glRenderbufferStorageMultisample(self, target: int, samples: int, internalformat: int, width: int, height: int) -> None: ...
    def glFramebufferTextureLayer(self, target: int, attachment: int, texture: int, level: int, layer: int) -> None: ...
    def glBindVertexArray(self, array: int) -> None: ...
    def glIsVertexArray(self, array: int) -> int: ...
    def glBlendEquationSeparate(self, modeRGB: int, modeAlpha: int) -> None: ...
    def glDrawBuffers(self, n: int, bufs: PYQT_OPENGL_ARRAY) -> None: ...
    def glStencilOpSeparate(self, face: int, sfail: int, dpfail: int, dppass: int) -> None: ...
    def glStencilFuncSeparate(self, face: int, func: int, ref: int, mask: int) -> None: ...
    def glStencilMaskSeparate(self, face: int, mask: int) -> None: ...
    def glAttachShader(self, program: int, shader: int) -> None: ...
    def glBindAttribLocation(self, program: int, index: int, name: typing.Optional[str]) -> None: ...
    def glCompileShader(self, shader: int) -> None: ...
    def glCreateProgram(self) -> int: ...
    def glCreateShader(self, type: int) -> int: ...
    def glDeleteProgram(self, program: int) -> None: ...
    def glDeleteShader(self, shader: int) -> None: ...
    def glDetachShader(self, program: int, shader: int) -> None: ...
    def glDisableVertexAttribArray(self, index: int) -> None: ...
    def glEnableVertexAttribArray(self, index: int) -> None: ...
    def glGetActiveAttrib(self, program: int, index: int) -> tuple[str, int, int]: ...
    def glGetActiveUniform(self, program: int, index: int) -> tuple[str, int, int]: ...
    def glGetAttachedShaders(self, program: int) -> tuple[int, ...]: ...
    def glGetAttribLocation(self, program: int, name: typing.Optional[str]) -> int: ...
    def glGetProgramiv(self, program: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int]]]: ...
    def glGetProgramInfoLog(self, program: int) -> bytes: ...
    def glGetShaderiv(self, shader: int, pname: int) -> typing.Optional[int]: ...
    def glGetShaderInfoLog(self, shader: int) -> bytes: ...
    def glGetShaderSource(self, shader: int) -> bytes: ...
    def glGetUniformLocation(self, program: int, name: typing.Optional[str]) -> int: ...
    def glGetVertexAttribdv(self, index: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetVertexAttribfv(self, index: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetVertexAttribiv(self, index: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int, int]]]: ...
    def glIsProgram(self, program: int) -> int: ...
    def glIsShader(self, shader: int) -> int: ...
    def glLinkProgram(self, program: int) -> None: ...
    def glUseProgram(self, program: int) -> None: ...
    def glUniform1f(self, location: int, v0: float) -> None: ...
    def glUniform2f(self, location: int, v0: float, v1: float) -> None: ...
    def glUniform3f(self, location: int, v0: float, v1: float, v2: float) -> None: ...
    def glUniform4f(self, location: int, v0: float, v1: float, v2: float, v3: float) -> None: ...
    def glUniform1i(self, location: int, v0: int) -> None: ...
    def glUniform2i(self, location: int, v0: int, v1: int) -> None: ...
    def glUniform3i(self, location: int, v0: int, v1: int, v2: int) -> None: ...
    def glUniform4i(self, location: int, v0: int, v1: int, v2: int, v3: int) -> None: ...
    def glUniform1fv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform2fv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform3fv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform4fv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform1iv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform2iv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform3iv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniform4iv(self, location: int, count: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniformMatrix2fv(self, location: int, count: int, transpose: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniformMatrix3fv(self, location: int, count: int, transpose: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glUniformMatrix4fv(self, location: int, count: int, transpose: int, value: PYQT_OPENGL_ARRAY) -> None: ...
    def glValidateProgram(self, program: int) -> None: ...
    def glVertexAttribPointer(self, index: int, size: int, type: int, normalized: int, stride: int, pointer: PYQT_OPENGL_BOUND_ARRAY) -> None: ...
    def glGenQueries(self, n: int) -> typing.Optional[typing.Union[int, tuple[int, ...]]]: ...
    def glDeleteQueries(self, n: int, ids: PYQT_OPENGL_ARRAY) -> None: ...
    def glIsQuery(self, id: int) -> int: ...
    def glBeginQuery(self, target: int, id: int) -> None: ...
    def glEndQuery(self, target: int) -> None: ...
    def glGetQueryiv(self, target: int, pname: int) -> typing.Optional[int]: ...
    def glBindBuffer(self, target: int, buffer: int) -> None: ...
    def glDeleteBuffers(self, n: int, buffers: PYQT_OPENGL_ARRAY) -> None: ...
    def glGenBuffers(self, n: int) -> typing.Optional[typing.Union[int, tuple[int, ...]]]: ...
    def glIsBuffer(self, buffer: int) -> int: ...
    def glBufferData(self, target: int, size: int, data: PYQT_OPENGL_ARRAY, usage: int) -> None: ...
    def glBufferSubData(self, target: int, offset: int, size: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glUnmapBuffer(self, target: int) -> int: ...
    def glGetBufferParameteriv(self, target: int, pname: int) -> typing.Optional[int]: ...
    def glBlendFuncSeparate(self, sfactorRGB: int, dfactorRGB: int, sfactorAlpha: int, dfactorAlpha: int) -> None: ...
    def glPointParameterf(self, pname: int, param: float) -> None: ...
    def glPointParameterfv(self, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glPointParameteri(self, pname: int, param: int) -> None: ...
    def glPointParameteriv(self, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glActiveTexture(self, texture: int) -> None: ...
    def glSampleCoverage(self, value: float, invert: int) -> None: ...
    def glCompressedTexImage3D(self, target: int, level: int, internalformat: int, width: int, height: int, depth: int, border: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexImage2D(self, target: int, level: int, internalformat: int, width: int, height: int, border: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexImage1D(self, target: int, level: int, internalformat: int, width: int, border: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexSubImage3D(self, target: int, level: int, xoffset: int, yoffset: int, zoffset: int, width: int, height: int, depth: int, format: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexSubImage2D(self, target: int, level: int, xoffset: int, yoffset: int, width: int, height: int, format: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glCompressedTexSubImage1D(self, target: int, level: int, xoffset: int, width: int, format: int, imageSize: int, data: PYQT_OPENGL_ARRAY) -> None: ...
    def glBlendColor(self, red: float, green: float, blue: float, alpha: float) -> None: ...
    def glBlendEquation(self, mode: int) -> None: ...
    def glDrawRangeElements(self, mode: int, start: int, end: int, count: int, type: int, indices: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexImage3D(self, target: int, level: int, internalformat: int, width: int, height: int, depth: int, border: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexSubImage3D(self, target: int, level: int, xoffset: int, yoffset: int, zoffset: int, width: int, height: int, depth: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glCopyTexSubImage3D(self, target: int, level: int, xoffset: int, yoffset: int, zoffset: int, x: int, y: int, width: int, height: int) -> None: ...
    def glDrawArrays(self, mode: int, first: int, count: int) -> None: ...
    def glDrawElements(self, mode: int, count: int, type: int, indices: PYQT_OPENGL_ARRAY) -> None: ...
    def glPolygonOffset(self, factor: float, units: float) -> None: ...
    def glCopyTexImage1D(self, target: int, level: int, internalformat: int, x: int, y: int, width: int, border: int) -> None: ...
    def glCopyTexImage2D(self, target: int, level: int, internalformat: int, x: int, y: int, width: int, height: int, border: int) -> None: ...
    def glCopyTexSubImage1D(self, target: int, level: int, xoffset: int, x: int, y: int, width: int) -> None: ...
    def glCopyTexSubImage2D(self, target: int, level: int, xoffset: int, yoffset: int, x: int, y: int, width: int, height: int) -> None: ...
    def glTexSubImage1D(self, target: int, level: int, xoffset: int, width: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexSubImage2D(self, target: int, level: int, xoffset: int, yoffset: int, width: int, height: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glBindTexture(self, target: int, texture: int) -> None: ...
    def glDeleteTextures(self, n: int, textures: PYQT_OPENGL_ARRAY) -> None: ...
    def glGenTextures(self, n: int) -> typing.Optional[typing.Union[int, tuple[int, ...]]]: ...
    def glIsTexture(self, texture: int) -> int: ...
    def glCullFace(self, mode: int) -> None: ...
    def glFrontFace(self, mode: int) -> None: ...
    def glHint(self, target: int, mode: int) -> None: ...
    def glLineWidth(self, width: float) -> None: ...
    def glPointSize(self, size: float) -> None: ...
    def glPolygonMode(self, face: int, mode: int) -> None: ...
    def glScissor(self, x: int, y: int, width: int, height: int) -> None: ...
    def glTexParameterf(self, target: int, pname: int, param: float) -> None: ...
    def glTexParameterfv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexParameteri(self, target: int, pname: int, param: int) -> None: ...
    def glTexParameteriv(self, target: int, pname: int, params: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexImage1D(self, target: int, level: int, internalformat: int, width: int, border: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glTexImage2D(self, target: int, level: int, internalformat: int, width: int, height: int, border: int, format: int, type: int, pixels: PYQT_OPENGL_ARRAY) -> None: ...
    def glDrawBuffer(self, mode: int) -> None: ...
    def glClear(self, mask: int) -> None: ...
    def glClearColor(self, red: float, green: float, blue: float, alpha: float) -> None: ...
    def glClearStencil(self, s: int) -> None: ...
    def glClearDepth(self, depth: float) -> None: ...
    def glStencilMask(self, mask: int) -> None: ...
    def glColorMask(self, red: int, green: int, blue: int, alpha: int) -> None: ...
    def glDepthMask(self, flag: int) -> None: ...
    def glDisable(self, cap: int) -> None: ...
    def glEnable(self, cap: int) -> None: ...
    def glFinish(self) -> None: ...
    def glFlush(self) -> None: ...
    def glBlendFunc(self, sfactor: int, dfactor: int) -> None: ...
    def glLogicOp(self, opcode: int) -> None: ...
    def glStencilFunc(self, func: int, ref: int, mask: int) -> None: ...
    def glStencilOp(self, fail: int, zfail: int, zpass: int) -> None: ...
    def glDepthFunc(self, func: int) -> None: ...
    def glPixelStoref(self, pname: int, param: float) -> None: ...
    def glPixelStorei(self, pname: int, param: int) -> None: ...
    def glReadBuffer(self, mode: int) -> None: ...
    def glReadPixels(self, x: int, y: int, width: int, height: int, format: int, type: int) -> typing.Union[tuple[float, ...], tuple[int, ...]]: ...
    def glGetBooleanv(self, pname: int) -> typing.Optional[typing.Union[bool, tuple[bool, ...]]]: ...
    def glGetDoublev(self, pname: int) -> typing.Optional[typing.Union[float, tuple[float, ...]]]: ...
    def glGetError(self) -> int: ...
    def glGetFloatv(self, pname: int) -> typing.Optional[typing.Union[float, tuple[float, ...]]]: ...
    def glGetIntegerv(self, pname: int) -> typing.Optional[typing.Union[int, tuple[int, ...]]]: ...
    def glGetString(self, name: int) -> typing.Optional[str]: ...
    def glGetTexParameterfv(self, target: int, pname: int) -> typing.Optional[typing.Union[float, tuple[float, float, float, float]]]: ...
    def glGetTexParameteriv(self, target: int, pname: int) -> typing.Optional[typing.Union[int, tuple[int, int, int, int]]]: ...
    def glGetTexLevelParameterfv(self, target: int, level: int, pname: int) -> typing.Optional[float]: ...
    def glGetTexLevelParameteriv(self, target: int, level: int, pname: int) -> typing.Optional[int]: ...
    def glIsEnabled(self, cap: int) -> int: ...
    def glDepthRange(self, nearVal: float, farVal: float) -> None: ...
    def glViewport(self, x: int, y: int, width: int, height: int) -> None: ...
    def initializeOpenGLFunctions(self) -> bool: ...


class QOpenGLVersionProfile(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, format: QtGui.QSurfaceFormat) -> None: ...
    @typing.overload
    def __init__(self, other: 'QOpenGLVersionProfile') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __hash__(self) -> int: ...
    def isValid(self) -> bool: ...
    def isLegacyVersion(self) -> bool: ...
    def hasProfiles(self) -> bool: ...
    def setProfile(self, profile: QtGui.QSurfaceFormat.OpenGLContextProfile) -> None: ...
    def profile(self) -> QtGui.QSurfaceFormat.OpenGLContextProfile: ...
    def setVersion(self, majorVersion: int, minorVersion: int) -> None: ...
    def version(self) -> tuple[int, int]: ...
