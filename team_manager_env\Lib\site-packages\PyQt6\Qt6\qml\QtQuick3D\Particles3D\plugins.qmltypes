import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquick3dparticle_p.h"
        name: "QQuick3DParticle"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D.Particles3D/Particle3D 6.2"]
        isCreatable: false
        exportMetaObjectRevisions: [1538]
        Enum {
            name: "FadeType"
            values: ["FadeNone", "FadeOpacity", "FadeScale"]
        }
        Enum {
            name: "AlignMode"
            values: [
                "AlignNone",
                "AlignTowardsTarget",
                "AlignTowardsStartVelocity"
            ]
        }
        Enum {
            name: "SortMode"
            values: ["SortNone", "SortNewest", "SortOldest", "SortDistance"]
        }
        Property {
            name: "maxAmount"
            type: "int"
            read: "maxAmount"
            write: "setMaxAmount"
            notify: "maxAmountChanged"
            index: 0
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            reset: "resetColor"
            notify: "colorChanged"
            index: 1
        }
        Property {
            name: "colorVariation"
            type: "QVector4D"
            read: "colorVariation"
            write: "setColorVariation"
            notify: "colorVariationChanged"
            index: 2
        }
        Property {
            name: "unifiedColorVariation"
            type: "bool"
            read: "unifiedColorVariation"
            write: "setUnifiedColorVariation"
            notify: "unifiedColorVariationChanged"
            index: 3
        }
        Property {
            name: "fadeInEffect"
            type: "FadeType"
            read: "fadeInEffect"
            write: "setFadeInEffect"
            notify: "fadeInEffectChanged"
            index: 4
        }
        Property {
            name: "fadeOutEffect"
            type: "FadeType"
            read: "fadeOutEffect"
            write: "setFadeOutEffect"
            notify: "fadeOutEffectChanged"
            index: 5
        }
        Property {
            name: "fadeInDuration"
            type: "int"
            read: "fadeInDuration"
            write: "setFadeInDuration"
            notify: "fadeInDurationChanged"
            index: 6
        }
        Property {
            name: "fadeOutDuration"
            type: "int"
            read: "fadeOutDuration"
            write: "setFadeOutDuration"
            notify: "fadeOutDurationChanged"
            index: 7
        }
        Property {
            name: "alignMode"
            type: "AlignMode"
            read: "alignMode"
            write: "setAlignMode"
            notify: "alignModeChanged"
            index: 8
        }
        Property {
            name: "alignTargetPosition"
            type: "QVector3D"
            read: "alignTargetPosition"
            write: "setAlignTargetPosition"
            notify: "alignTargetPositionChanged"
            index: 9
        }
        Property {
            name: "hasTransparency"
            type: "bool"
            read: "hasTransparency"
            write: "setHasTransparency"
            notify: "hasTransparencyChanged"
            index: 10
        }
        Property {
            name: "sortMode"
            type: "SortMode"
            read: "sortMode"
            write: "setSortMode"
            notify: "sortModeChanged"
            index: 11
        }
        Signal { name: "systemChanged" }
        Signal { name: "maxAmountChanged" }
        Signal { name: "colorChanged" }
        Signal { name: "colorVariationChanged" }
        Signal { name: "unifiedColorVariationChanged" }
        Signal { name: "fadeInEffectChanged" }
        Signal { name: "fadeOutEffectChanged" }
        Signal { name: "fadeInDurationChanged" }
        Signal { name: "fadeOutDurationChanged" }
        Signal { name: "alignModeChanged" }
        Signal { name: "alignTargetPositionChanged" }
        Signal { name: "hasTransparencyChanged" }
        Signal { name: "sortModeChanged" }
        Method {
            name: "setSystem"
            Parameter { name: "system"; type: "QQuick3DParticleSystem"; isPointer: true }
        }
        Method {
            name: "setMaxAmount"
            Parameter { name: "maxAmount"; type: "int" }
        }
        Method {
            name: "setColor"
            Parameter { name: "color"; type: "QColor" }
        }
        Method {
            name: "setColorVariation"
            Parameter { name: "colorVariation"; type: "QVector4D" }
        }
        Method {
            name: "setUnifiedColorVariation"
            Parameter { name: "unified"; type: "bool" }
        }
        Method {
            name: "setFadeInEffect"
            Parameter { name: "fadeInEffect"; type: "QQuick3DParticle::FadeType" }
        }
        Method {
            name: "setFadeOutEffect"
            Parameter { name: "fadeOutEffect"; type: "QQuick3DParticle::FadeType" }
        }
        Method {
            name: "setFadeInDuration"
            Parameter { name: "fadeInDuration"; type: "int" }
        }
        Method {
            name: "setFadeOutDuration"
            Parameter { name: "fadeOutDuration"; type: "int" }
        }
        Method {
            name: "setAlignMode"
            Parameter { name: "alignMode"; type: "QQuick3DParticle::AlignMode" }
        }
        Method {
            name: "setAlignTargetPosition"
            Parameter { name: "alignPosition"; type: "QVector3D" }
        }
        Method {
            name: "setHasTransparency"
            Parameter { name: "transparency"; type: "bool" }
        }
        Method {
            name: "setSortMode"
            Parameter { name: "sortMode"; type: "QQuick3DParticle::SortMode" }
        }
    }
    Component {
        file: "private/qquick3dparticleabstractshape_p.h"
        name: "QQuick3DParticleAbstractShape"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
    }
    Component {
        file: "private/qquick3dparticleaffector_p.h"
        name: "QQuick3DParticleAffector"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.Particles3D/Affector3D 6.2"]
        isCreatable: false
        exportMetaObjectRevisions: [1538]
        Property {
            name: "system"
            type: "QQuick3DParticleSystem"
            isPointer: true
            read: "system"
            write: "setSystem"
            notify: "systemChanged"
            index: 0
        }
        Property {
            name: "particles"
            type: "QQuick3DParticle"
            isList: true
            read: "particles"
            index: 1
            isReadonly: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 2
        }
        Signal { name: "update" }
        Signal { name: "systemChanged" }
        Signal { name: "enabledChanged" }
        Method {
            name: "setSystem"
            Parameter { name: "system"; type: "QQuick3DParticleSystem"; isPointer: true }
        }
        Method {
            name: "setEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "private/qquick3dparticleattractor_p.h"
        name: "QQuick3DParticleAttractor"
        accessSemantics: "reference"
        prototype: "QQuick3DParticleAffector"
        exports: ["QtQuick3D.Particles3D/Attractor3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "positionVariation"
            type: "QVector3D"
            read: "positionVariation"
            write: "setPositionVariation"
            notify: "positionVariationChanged"
            index: 0
        }
        Property {
            name: "shape"
            type: "QQuick3DParticleAbstractShape"
            isPointer: true
            read: "shape"
            write: "setShape"
            notify: "shapeChanged"
            index: 1
        }
        Property {
            name: "duration"
            type: "int"
            read: "duration"
            write: "setDuration"
            notify: "durationChanged"
            index: 2
        }
        Property {
            name: "durationVariation"
            type: "int"
            read: "durationVariation"
            write: "setDurationVariation"
            notify: "durationVariationChanged"
            index: 3
        }
        Property {
            name: "hideAtEnd"
            type: "bool"
            read: "hideAtEnd"
            write: "setHideAtEnd"
            notify: "hideAtEndChanged"
            index: 4
        }
        Property {
            name: "useCachedPositions"
            type: "bool"
            read: "useCachedPositions"
            write: "setUseCachedPositions"
            notify: "useCachedPositionsChanged"
            index: 5
        }
        Property {
            name: "positionsAmount"
            type: "int"
            read: "positionsAmount"
            write: "setPositionsAmount"
            notify: "positionsAmountChanged"
            index: 6
        }
        Signal { name: "positionVariationChanged" }
        Signal { name: "shapeChanged" }
        Signal { name: "durationChanged" }
        Signal { name: "durationVariationChanged" }
        Signal { name: "hideAtEndChanged" }
        Signal { name: "useCachedPositionsChanged" }
        Signal { name: "positionsAmountChanged" }
        Method {
            name: "setPositionVariation"
            Parameter { name: "positionVariation"; type: "QVector3D" }
        }
        Method {
            name: "setShape"
            Parameter { name: "shape"; type: "QQuick3DParticleAbstractShape"; isPointer: true }
        }
        Method {
            name: "setDuration"
            Parameter { name: "duration"; type: "int" }
        }
        Method {
            name: "setDurationVariation"
            Parameter { name: "durationVariation"; type: "int" }
        }
        Method {
            name: "setHideAtEnd"
            Parameter { name: "hideAtEnd"; type: "bool" }
        }
        Method {
            name: "setUseCachedPositions"
            Parameter { name: "useCachedPositions"; type: "bool" }
        }
        Method {
            name: "setPositionsAmount"
            Parameter { name: "positionsAmount"; type: "int" }
        }
    }
    Component {
        file: "private/qquick3dparticlecustomshape_p.h"
        name: "QQuick3DParticleCustomShape"
        accessSemantics: "reference"
        prototype: "QQuick3DParticleAbstractShape"
        exports: ["QtQuick3D.Particles3D/ParticleCustomShape3D 6.3"]
        exportMetaObjectRevisions: [1539]
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "randomizeData"
            type: "bool"
            read: "randomizeData"
            write: "setRandomizeData"
            notify: "randomizeDataChanged"
            index: 1
        }
        Signal { name: "sourceChanged" }
        Signal { name: "randomizeDataChanged" }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "setRandomizeData"
            Parameter { name: "random"; type: "bool" }
        }
    }
    Component {
        file: "private/qquick3dparticledirection_p.h"
        name: "QQuick3DParticleDirection"
        accessSemantics: "reference"
        prototype: "QObject"
    }
    Component {
        file: "private/qquick3dparticledynamicburst_p.h"
        name: "QQuick3DParticleDynamicBurst"
        accessSemantics: "reference"
        prototype: "QQuick3DParticleEmitBurst"
        exports: ["QtQuick3D.Particles3D/DynamicBurst3D 6.3"]
        exportMetaObjectRevisions: [1539]
        Enum {
            name: "TriggerMode"
            values: ["TriggerTime", "TriggerStart", "TriggerEnd"]
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 0
        }
        Property {
            name: "amountVariation"
            type: "int"
            read: "amountVariation"
            write: "setAmountVariation"
            notify: "amountVariationChanged"
            index: 1
        }
        Property {
            name: "triggerMode"
            type: "TriggerMode"
            read: "triggerMode"
            write: "setTriggerMode"
            notify: "triggerModeChanged"
            index: 2
        }
        Signal { name: "enabledChanged" }
        Signal { name: "amountVariationChanged" }
        Signal { name: "triggerModeChanged" }
        Method {
            name: "setEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setAmountVariation"
            Parameter { name: "value"; type: "int" }
        }
        Method {
            name: "setTriggerMode"
            Parameter { name: "mode"; type: "TriggerMode" }
        }
    }
    Component {
        file: "private/qquick3dparticleemitburst_p.h"
        name: "QQuick3DParticleEmitBurst"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtQuick3D.Particles3D/EmitBurst3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "time"
            type: "int"
            read: "time"
            write: "setTime"
            notify: "timeChanged"
            index: 0
        }
        Property {
            name: "amount"
            type: "int"
            read: "amount"
            write: "setAmount"
            notify: "amountChanged"
            index: 1
        }
        Property {
            name: "duration"
            type: "int"
            read: "duration"
            write: "setDuration"
            notify: "durationChanged"
            index: 2
        }
        Signal { name: "timeChanged" }
        Signal { name: "amountChanged" }
        Signal { name: "durationChanged" }
        Method {
            name: "setTime"
            Parameter { name: "time"; type: "int" }
        }
        Method {
            name: "setAmount"
            Parameter { name: "amount"; type: "int" }
        }
        Method {
            name: "setDuration"
            Parameter { name: "duration"; type: "int" }
        }
    }
    Component {
        file: "private/qquick3dparticleemitter_p.h"
        name: "QQuick3DParticleEmitter"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.Particles3D/ParticleEmitter3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "system"
            type: "QQuick3DParticleSystem"
            isPointer: true
            read: "system"
            write: "setSystem"
            notify: "systemChanged"
            index: 0
        }
        Property {
            name: "emitBursts"
            type: "QQuick3DParticleEmitBurst"
            isList: true
            read: "emitBursts"
            index: 1
            isReadonly: true
        }
        Property {
            name: "velocity"
            type: "QQuick3DParticleDirection"
            isPointer: true
            read: "velocity"
            write: "setVelocity"
            notify: "velocityChanged"
            index: 2
        }
        Property {
            name: "particle"
            type: "QQuick3DParticle"
            isPointer: true
            read: "particle"
            write: "setParticle"
            notify: "particleChanged"
            index: 3
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 4
        }
        Property {
            name: "shape"
            type: "QQuick3DParticleAbstractShape"
            isPointer: true
            read: "shape"
            write: "setShape"
            notify: "shapeChanged"
            index: 5
        }
        Property {
            name: "emitRate"
            type: "float"
            read: "emitRate"
            write: "setEmitRate"
            notify: "emitRateChanged"
            index: 6
        }
        Property {
            name: "lifeSpan"
            type: "int"
            read: "lifeSpan"
            write: "setLifeSpan"
            notify: "lifeSpanChanged"
            index: 7
        }
        Property {
            name: "lifeSpanVariation"
            type: "int"
            read: "lifeSpanVariation"
            write: "setLifeSpanVariation"
            notify: "lifeSpanVariationChanged"
            index: 8
        }
        Property {
            name: "particleScale"
            type: "float"
            read: "particleScale"
            write: "setParticleScale"
            notify: "particleScaleChanged"
            index: 9
        }
        Property {
            name: "particleEndScale"
            type: "float"
            read: "particleEndScale"
            write: "setParticleEndScale"
            notify: "particleEndScaleChanged"
            index: 10
        }
        Property {
            name: "particleScaleVariation"
            type: "float"
            read: "particleScaleVariation"
            write: "setParticleScaleVariation"
            notify: "particleScaleVariationChanged"
            index: 11
        }
        Property {
            name: "particleEndScaleVariation"
            type: "float"
            read: "particleEndScaleVariation"
            write: "setParticleEndScaleVariation"
            notify: "particleEndScaleVariationChanged"
            index: 12
        }
        Property {
            name: "particleRotation"
            type: "QVector3D"
            read: "particleRotation"
            write: "setParticleRotation"
            notify: "particleRotationChanged"
            index: 13
        }
        Property {
            name: "particleRotationVariation"
            type: "QVector3D"
            read: "particleRotationVariation"
            write: "setParticleRotationVariation"
            notify: "particleRotationVariationChanged"
            index: 14
        }
        Property {
            name: "particleRotationVelocity"
            type: "QVector3D"
            read: "particleRotationVelocity"
            write: "setParticleRotationVelocity"
            notify: "particleRotationVelocityChanged"
            index: 15
        }
        Property {
            name: "particleRotationVelocityVariation"
            type: "QVector3D"
            read: "particleRotationVelocityVariation"
            write: "setParticleRotationVelocityVariation"
            notify: "particleRotationVariationVelocityChanged"
            index: 16
        }
        Property {
            name: "depthBias"
            type: "float"
            read: "depthBias"
            write: "setDepthBias"
            notify: "depthBiasChanged"
            index: 17
        }
        Signal { name: "velocityChanged" }
        Signal { name: "systemChanged" }
        Signal { name: "emitRateChanged" }
        Signal { name: "particleScaleChanged" }
        Signal { name: "particleEndScaleChanged" }
        Signal { name: "particleScaleVariationChanged" }
        Signal { name: "particleEndScaleVariationChanged" }
        Signal { name: "lifeSpanChanged" }
        Signal { name: "lifeSpanVariationChanged" }
        Signal { name: "particleChanged" }
        Signal { name: "shapeChanged" }
        Signal { name: "particleRotationChanged" }
        Signal { name: "particleRotationVariationChanged" }
        Signal { name: "particleRotationVelocityChanged" }
        Signal { name: "particleRotationVariationVelocityChanged" }
        Signal { name: "enabledChanged" }
        Signal { name: "depthBiasChanged" }
        Method {
            name: "setEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setVelocity"
            Parameter { name: "velocity"; type: "QQuick3DParticleDirection"; isPointer: true }
        }
        Method {
            name: "setSystem"
            Parameter { name: "system"; type: "QQuick3DParticleSystem"; isPointer: true }
        }
        Method {
            name: "setEmitRate"
            Parameter { name: "emitRate"; type: "float" }
        }
        Method {
            name: "setParticleScale"
            Parameter { name: "particleScale"; type: "float" }
        }
        Method {
            name: "setParticleEndScale"
            Parameter { name: "particleEndScale"; type: "float" }
        }
        Method {
            name: "setParticleScaleVariation"
            Parameter { name: "particleScaleVariation"; type: "float" }
        }
        Method {
            name: "setParticleEndScaleVariation"
            Parameter { name: "particleEndScaleVariation"; type: "float" }
        }
        Method {
            name: "setLifeSpan"
            Parameter { name: "lifeSpan"; type: "int" }
        }
        Method {
            name: "setLifeSpanVariation"
            Parameter { name: "lifeSpanVariation"; type: "int" }
        }
        Method {
            name: "setParticle"
            Parameter { name: "particle"; type: "QQuick3DParticle"; isPointer: true }
        }
        Method {
            name: "setShape"
            Parameter { name: "shape"; type: "QQuick3DParticleAbstractShape"; isPointer: true }
        }
        Method {
            name: "setParticleRotation"
            Parameter { name: "particleRotation"; type: "QVector3D" }
        }
        Method {
            name: "setParticleRotationVariation"
            Parameter { name: "particleRotationVariation"; type: "QVector3D" }
        }
        Method {
            name: "setParticleRotationVelocity"
            Parameter { name: "particleRotationVelocity"; type: "QVector3D" }
        }
        Method {
            name: "setParticleRotationVelocityVariation"
            Parameter { name: "particleRotationVelocityVariation"; type: "QVector3D" }
        }
        Method {
            name: "setDepthBias"
            Parameter { name: "bias"; type: "float" }
        }
        Method {
            name: "burst"
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "burst"
            Parameter { name: "count"; type: "int" }
            Parameter { name: "duration"; type: "int" }
        }
        Method {
            name: "burst"
            Parameter { name: "count"; type: "int" }
            Parameter { name: "duration"; type: "int" }
            Parameter { name: "position"; type: "QVector3D" }
        }
    }
    Component {
        file: "private/qquick3dparticlegravity_p.h"
        name: "QQuick3DParticleGravity"
        accessSemantics: "reference"
        prototype: "QQuick3DParticleAffector"
        exports: ["QtQuick3D.Particles3D/Gravity3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "magnitude"
            type: "float"
            read: "magnitude"
            write: "setMagnitude"
            notify: "magnitudeChanged"
            index: 0
        }
        Property {
            name: "direction"
            type: "QVector3D"
            read: "direction"
            write: "setDirection"
            notify: "directionChanged"
            index: 1
        }
        Signal { name: "magnitudeChanged" }
        Signal { name: "directionChanged" }
        Method {
            name: "setDirection"
            Parameter { name: "direction"; type: "QVector3D" }
        }
        Method {
            name: "setMagnitude"
            Parameter { name: "magnitude"; type: "float" }
        }
    }
    Component {
        file: "private/qquick3dparticlelineparticle_p.h"
        name: "QQuick3DParticleLineParticle"
        accessSemantics: "reference"
        prototype: "QQuick3DParticleSpriteParticle"
        exports: ["QtQuick3D.Particles3D/LineParticle3D 6.4"]
        exportMetaObjectRevisions: [1540]
        Enum {
            name: "TexcoordMode"
            values: ["Absolute", "Relative", "Fill"]
        }
        Property {
            name: "segmentCount"
            type: "int"
            read: "segmentCount"
            write: "setSegmentCount"
            notify: "segmentCountChanged"
            index: 0
        }
        Property {
            name: "alphaFade"
            type: "float"
            read: "alphaFade"
            write: "setAlphaFade"
            notify: "alphaFadeChanged"
            index: 1
        }
        Property {
            name: "scaleMultiplier"
            type: "float"
            read: "scaleMultiplier"
            write: "setScaleMultiplier"
            notify: "scaleMultiplierChanged"
            index: 2
        }
        Property {
            name: "texcoordMultiplier"
            type: "float"
            read: "texcoordMultiplier"
            write: "setTexcoordMultiplier"
            notify: "texcoordMultiplierChanged"
            index: 3
        }
        Property {
            name: "length"
            type: "float"
            read: "length"
            write: "setLength"
            notify: "lengthChanged"
            index: 4
        }
        Property {
            name: "lengthVariation"
            type: "float"
            read: "lengthVariation"
            write: "setLengthVariation"
            notify: "lengthVariationChanged"
            index: 5
        }
        Property {
            name: "lengthDeltaMin"
            type: "float"
            read: "lengthDeltaMin"
            write: "setLengthDeltaMin"
            notify: "lengthDeltaMinChanged"
            index: 6
        }
        Property {
            name: "eolFadeOutDuration"
            type: "int"
            read: "eolFadeOutDuration"
            write: "setEolFadeOutDuration"
            notify: "eolFadeOutDurationChanged"
            index: 7
        }
        Property {
            name: "texcoordMode"
            type: "TexcoordMode"
            read: "texcoordMode"
            write: "setTexcoordMode"
            notify: "texcoordModeChanged"
            index: 8
        }
        Signal { name: "segmentCountChanged" }
        Signal { name: "alphaFadeChanged" }
        Signal { name: "scaleMultiplierChanged" }
        Signal { name: "texcoordMultiplierChanged" }
        Signal { name: "lengthChanged" }
        Signal { name: "lengthVariationChanged" }
        Signal { name: "lengthDeltaMinChanged" }
        Signal { name: "eolFadeOutDurationChanged" }
        Signal { name: "texcoordModeChanged" }
        Method {
            name: "setSegmentCount"
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "setAlphaFade"
            Parameter { name: "fade"; type: "float" }
        }
        Method {
            name: "setScaleMultiplier"
            Parameter { name: "multiplier"; type: "float" }
        }
        Method {
            name: "setTexcoordMultiplier"
            Parameter { name: "multiplier"; type: "float" }
        }
        Method {
            name: "setLength"
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "setLengthVariation"
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "setLengthDeltaMin"
            Parameter { name: "min"; type: "float" }
        }
        Method {
            name: "setEolFadeOutDuration"
            Parameter { name: "duration"; type: "int" }
        }
        Method {
            name: "setTexcoordMode"
            Parameter { name: "mode"; type: "QQuick3DParticleLineParticle::TexcoordMode" }
        }
    }
    Component {
        file: "private/qquick3dparticlemodelblendparticle_p.h"
        name: "QQuick3DParticleModelBlendParticle"
        accessSemantics: "reference"
        prototype: "QQuick3DParticle"
        exports: ["QtQuick3D.Particles3D/ModelBlendParticle3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Enum {
            name: "ModelBlendMode"
            values: ["Explode", "Construct", "Transfer"]
        }
        Enum {
            name: "ModelBlendEmitMode"
            values: ["Sequential", "Random", "Activation"]
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 0
        }
        Property {
            name: "endNode"
            type: "QQuick3DNode"
            isPointer: true
            read: "endNode"
            write: "setEndNode"
            notify: "endNodeChanged"
            index: 1
        }
        Property {
            name: "modelBlendMode"
            type: "ModelBlendMode"
            read: "modelBlendMode"
            write: "setModelBlendMode"
            notify: "modelBlendModeChanged"
            index: 2
        }
        Property {
            name: "endTime"
            type: "int"
            read: "endTime"
            write: "setEndTime"
            notify: "endTimeChanged"
            index: 3
        }
        Property {
            name: "activationNode"
            type: "QQuick3DNode"
            isPointer: true
            read: "activationNode"
            write: "setActivationNode"
            notify: "activationNodeChanged"
            index: 4
        }
        Property {
            name: "emitMode"
            type: "ModelBlendEmitMode"
            read: "emitMode"
            write: "setEmitMode"
            notify: "emitModeChanged"
            index: 5
        }
        Signal { name: "delegateChanged" }
        Signal { name: "blendFactorChanged" }
        Signal { name: "endNodeChanged" }
        Signal { name: "modelBlendModeChanged" }
        Signal { name: "endTimeChanged" }
        Signal { name: "activationNodeChanged" }
        Signal { name: "emitModeChanged" }
        Method {
            name: "setDelegate"
            Parameter { name: "setDelegate"; type: "QQmlComponent"; isPointer: true }
        }
        Method {
            name: "setEndNode"
            Parameter { name: "endNode"; type: "QQuick3DNode"; isPointer: true }
        }
        Method {
            name: "setEndTime"
            Parameter { name: "endTime"; type: "int" }
        }
        Method {
            name: "setModelBlendMode"
            Parameter { name: "mode"; type: "ModelBlendMode" }
        }
        Method {
            name: "setActivationNode"
            Parameter { name: "activationNode"; type: "QQuick3DNode"; isPointer: true }
        }
        Method {
            name: "setEmitMode"
            Parameter { name: "emitMode"; type: "ModelBlendEmitMode" }
        }
    }
    Component {
        file: "private/qquick3dparticlemodelparticle_p.h"
        name: "QQuick3DParticleModelParticle"
        accessSemantics: "reference"
        defaultProperty: "delegate"
        prototype: "QQuick3DParticle"
        exports: ["QtQuick3D.Particles3D/ModelParticle3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 0
        }
        Property {
            name: "instanceTable"
            type: "QQuick3DInstancing"
            isPointer: true
            read: "instanceTable"
            notify: "instanceTableChanged"
            index: 1
            isReadonly: true
        }
        Signal { name: "delegateChanged" }
        Signal { name: "instanceTableChanged" }
        Method {
            name: "setDelegate"
            Parameter { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        }
    }
    Component {
        file: "private/qquick3dparticlemodelshape_p.h"
        name: "QQuick3DParticleModelShape"
        accessSemantics: "reference"
        prototype: "QQuick3DParticleAbstractShape"
        exports: ["QtQuick3D.Particles3D/ParticleModelShape3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "fill"
            type: "bool"
            read: "fill"
            write: "setFill"
            notify: "fillChanged"
            index: 0
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 1
        }
        Signal { name: "fillChanged" }
        Signal { name: "delegateChanged" }
        Method {
            name: "setFill"
            Parameter { name: "fill"; type: "bool" }
        }
        Method {
            name: "setDelegate"
            Parameter { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        }
        Method {
            name: "getPosition"
            type: "QVector3D"
            Parameter { name: "particleIndex"; type: "int" }
        }
    }
    Component {
        file: "private/qquick3dparticlepointrotator_p.h"
        name: "QQuick3DParticlePointRotator"
        accessSemantics: "reference"
        prototype: "QQuick3DParticleAffector"
        exports: ["QtQuick3D.Particles3D/PointRotator3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "magnitude"
            type: "float"
            read: "magnitude"
            write: "setMagnitude"
            notify: "magnitudeChanged"
            index: 0
        }
        Property {
            name: "direction"
            type: "QVector3D"
            read: "direction"
            write: "setDirection"
            notify: "directionChanged"
            index: 1
        }
        Property {
            name: "pivotPoint"
            type: "QVector3D"
            read: "pivotPoint"
            write: "setPivotPoint"
            notify: "pivotPointChanged"
            index: 2
        }
        Signal { name: "magnitudeChanged" }
        Signal { name: "directionChanged" }
        Signal { name: "pivotPointChanged" }
        Method {
            name: "setMagnitude"
            Parameter { name: "magnitude"; type: "float" }
        }
        Method {
            name: "setDirection"
            Parameter { name: "direction"; type: "QVector3D" }
        }
        Method {
            name: "setPivotPoint"
            Parameter { name: "point"; type: "QVector3D" }
        }
    }
    Component {
        file: "private/qquick3dparticlerepeller_p.h"
        name: "QQuick3DParticleRepeller"
        accessSemantics: "reference"
        prototype: "QQuick3DParticleAffector"
        exports: ["QtQuick3D.Particles3D/Repeller3D 6.4"]
        exportMetaObjectRevisions: [1540]
        Property {
            name: "radius"
            type: "float"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 0
        }
        Property {
            name: "outerRadius"
            type: "float"
            read: "outerRadius"
            write: "setOuterRadius"
            notify: "outerRadiusChanged"
            index: 1
        }
        Property {
            name: "strength"
            type: "float"
            read: "strength"
            write: "setStrength"
            notify: "strengthChanged"
            index: 2
        }
        Signal { name: "radiusChanged" }
        Signal { name: "outerRadiusChanged" }
        Signal { name: "strengthChanged" }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setOuterRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setStrength"
            Parameter { name: "strength"; type: "float" }
        }
    }
    Component {
        file: "private/qquick3dparticlescaleaffector_p.h"
        name: "QQuick3DParticleScaleAffector"
        accessSemantics: "reference"
        prototype: "QQuick3DParticleAffector"
        exports: ["QtQuick3D.Particles3D/ScaleAffector3D 6.4"]
        exportMetaObjectRevisions: [1540]
        Enum {
            name: "ScalingType"
            values: [
                "Linear",
                "SewSaw",
                "SineWave",
                "AbsSineWave",
                "Step",
                "SmoothStep"
            ]
        }
        Property {
            name: "minSize"
            type: "float"
            read: "minSize"
            write: "setMinSize"
            notify: "minSizeChanged"
            index: 0
        }
        Property {
            name: "maxSize"
            type: "float"
            read: "maxSize"
            write: "setMaxSize"
            notify: "maxSizeChanged"
            index: 1
        }
        Property {
            name: "duration"
            type: "int"
            read: "duration"
            write: "setDuration"
            notify: "durationChanged"
            index: 2
        }
        Property {
            name: "type"
            type: "ScalingType"
            read: "type"
            write: "setType"
            notify: "typeChanged"
            index: 3
        }
        Property {
            name: "easingCurve"
            type: "QEasingCurve"
            read: "easingCurve"
            write: "setEasingCurve"
            notify: "easingCurveChanged"
            index: 4
        }
        Signal { name: "minSizeChanged" }
        Signal { name: "maxSizeChanged" }
        Signal { name: "durationChanged" }
        Signal { name: "typeChanged" }
        Signal { name: "easingCurveChanged" }
        Method {
            name: "setMinSize"
            Parameter { name: "size"; type: "float" }
        }
        Method {
            name: "setMaxSize"
            Parameter { name: "size"; type: "float" }
        }
        Method {
            name: "setDuration"
            Parameter { name: "duration"; type: "int" }
        }
        Method {
            name: "setType"
            Parameter { name: "type"; type: "ScalingType" }
        }
        Method {
            name: "setEasingCurve"
            Parameter { name: "curve"; type: "QEasingCurve" }
        }
    }
    Component {
        file: "private/qquick3dparticleshape_p.h"
        name: "QQuick3DParticleShape"
        accessSemantics: "reference"
        prototype: "QQuick3DParticleAbstractShape"
        exports: ["QtQuick3D.Particles3D/ParticleShape3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Enum {
            name: "ShapeType"
            values: ["Cube", "Sphere", "Cylinder"]
        }
        Property {
            name: "fill"
            type: "bool"
            read: "fill"
            write: "setFill"
            notify: "fillChanged"
            index: 0
        }
        Property {
            name: "type"
            type: "ShapeType"
            read: "type"
            write: "setType"
            notify: "typeChanged"
            index: 1
        }
        Property {
            name: "extents"
            type: "QVector3D"
            read: "extents"
            write: "setExtents"
            notify: "extentsChanged"
            index: 2
        }
        Signal { name: "fillChanged" }
        Signal { name: "typeChanged" }
        Signal { name: "extentsChanged" }
        Method {
            name: "setFill"
            Parameter { name: "fill"; type: "bool" }
        }
        Method {
            name: "setType"
            Parameter { name: "type"; type: "QQuick3DParticleShape::ShapeType" }
        }
        Method {
            name: "setExtents"
            Parameter { name: "extends"; type: "QVector3D" }
        }
    }
    Component {
        file: "private/qquick3dparticlespriteparticle_p.h"
        name: "QQuick3DParticleSpriteParticle"
        accessSemantics: "reference"
        prototype: "QQuick3DParticle"
        exports: [
            "QtQuick3D.Particles3D/SpriteParticle3D 6.2",
            "QtQuick3D.Particles3D/SpriteParticle3D 6.3",
            "QtQuick3D.Particles3D/SpriteParticle3D 6.4"
        ]
        exportMetaObjectRevisions: [1538, 1539, 1540]
        Enum {
            name: "BlendMode"
            values: ["SourceOver", "Screen", "Multiply"]
        }
        Property {
            name: "blendMode"
            type: "BlendMode"
            read: "blendMode"
            write: "setBlendMode"
            notify: "blendModeChanged"
            index: 0
        }
        Property {
            name: "sprite"
            type: "QQuick3DTexture"
            isPointer: true
            read: "sprite"
            write: "setSprite"
            notify: "spriteChanged"
            index: 1
        }
        Property {
            name: "spriteSequence"
            type: "QQuick3DParticleSpriteSequence"
            isPointer: true
            read: "spriteSequence"
            write: "setSpriteSequence"
            notify: "spriteSequenceChanged"
            index: 2
        }
        Property {
            name: "billboard"
            type: "bool"
            read: "billboard"
            write: "setBillboard"
            notify: "billboardChanged"
            index: 3
        }
        Property {
            name: "particleScale"
            type: "float"
            read: "particleScale"
            write: "setParticleScale"
            notify: "particleScaleChanged"
            index: 4
        }
        Property {
            name: "colorTable"
            type: "QQuick3DTexture"
            isPointer: true
            read: "colorTable"
            write: "setColorTable"
            notify: "colorTableChanged"
            index: 5
        }
        Property {
            name: "lights"
            revision: 1539
            type: "QQuick3DAbstractLight"
            isList: true
            read: "lights"
            notify: "lightsChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "offsetX"
            revision: 1539
            type: "float"
            read: "offsetX"
            write: "setOffsetX"
            notify: "offsetXChanged"
            index: 7
        }
        Property {
            name: "offsetY"
            revision: 1539
            type: "float"
            read: "offsetY"
            write: "setOffsetY"
            notify: "offsetYChanged"
            index: 8
        }
        Property {
            name: "castsReflections"
            revision: 1540
            type: "bool"
            read: "castsReflections"
            write: "setCastsReflections"
            notify: "castsReflectionsChanged"
            index: 9
        }
        Signal { name: "blendModeChanged" }
        Signal { name: "spriteChanged" }
        Signal { name: "spriteSequenceChanged" }
        Signal { name: "billboardChanged" }
        Signal { name: "particleScaleChanged" }
        Signal { name: "colorTableChanged" }
        Signal { name: "lightsChanged"; revision: 1539 }
        Signal { name: "offsetXChanged"; revision: 1539 }
        Signal { name: "offsetYChanged"; revision: 1539 }
        Signal { name: "castsReflectionsChanged"; revision: 1540 }
        Method {
            name: "setBlendMode"
            Parameter { name: "blendMode"; type: "QQuick3DParticleSpriteParticle::BlendMode" }
        }
        Method {
            name: "setSprite"
            Parameter { name: "sprite"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setSpriteSequence"
            Parameter { name: "spriteSequence"; type: "QQuick3DParticleSpriteSequence"; isPointer: true }
        }
        Method {
            name: "setBillboard"
            Parameter { name: "billboard"; type: "bool" }
        }
        Method {
            name: "setParticleScale"
            Parameter { name: "scale"; type: "float" }
        }
        Method {
            name: "setColorTable"
            Parameter { name: "colorTable"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setOffsetX"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setOffsetY"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setCastsReflections"
            revision: 1540
            Parameter { name: "castsReflections"; type: "bool" }
        }
        Method {
            name: "onLightDestroyed"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "private/qquick3dparticlespritesequence_p.h"
        name: "QQuick3DParticleSpriteSequence"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtQuick3D.Particles3D/SpriteSequence3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Enum {
            name: "AnimationDirection"
            values: [
                "Normal",
                "Reverse",
                "Alternate",
                "AlternateReverse",
                "SingleFrame"
            ]
        }
        Property {
            name: "frameCount"
            type: "int"
            read: "frameCount"
            write: "setFrameCount"
            notify: "frameCountChanged"
            index: 0
        }
        Property {
            name: "frameIndex"
            type: "int"
            read: "frameIndex"
            write: "setFrameIndex"
            notify: "frameIndexChanged"
            index: 1
        }
        Property {
            name: "interpolate"
            type: "bool"
            read: "interpolate"
            write: "setInterpolate"
            notify: "interpolateChanged"
            index: 2
        }
        Property {
            name: "duration"
            type: "int"
            read: "duration"
            write: "setDuration"
            notify: "durationChanged"
            index: 3
        }
        Property {
            name: "durationVariation"
            type: "int"
            read: "durationVariation"
            write: "setDurationVariation"
            notify: "durationVariationChanged"
            index: 4
        }
        Property {
            name: "randomStart"
            type: "bool"
            read: "randomStart"
            write: "setRandomStart"
            notify: "randomStartChanged"
            index: 5
        }
        Property {
            name: "animationDirection"
            type: "AnimationDirection"
            read: "animationDirection"
            write: "setAnimationDirection"
            notify: "animationDirectionChanged"
            index: 6
        }
        Signal { name: "frameCountChanged" }
        Signal { name: "frameIndexChanged" }
        Signal { name: "interpolateChanged" }
        Signal { name: "durationChanged" }
        Signal { name: "durationVariationChanged" }
        Signal { name: "randomStartChanged" }
        Signal { name: "animationDirectionChanged" }
        Method {
            name: "setFrameCount"
            Parameter { name: "frameCount"; type: "int" }
        }
        Method {
            name: "setFrameIndex"
            Parameter { name: "frameIndex"; type: "int" }
        }
        Method {
            name: "setInterpolate"
            Parameter { name: "interpolate"; type: "bool" }
        }
        Method {
            name: "setDuration"
            Parameter { name: "duration"; type: "int" }
        }
        Method {
            name: "setDurationVariation"
            Parameter { name: "durationVariation"; type: "int" }
        }
        Method {
            name: "setRandomStart"
            Parameter { name: "randomStart"; type: "bool" }
        }
        Method {
            name: "setAnimationDirection"
            Parameter {
                name: "animationDirection"
                type: "QQuick3DParticleSpriteSequence::AnimationDirection"
            }
        }
    }
    Component {
        file: "private/qquick3dparticlesystem_p.h"
        name: "QQuick3DParticleSystem"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.Particles3D/ParticleSystem3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "running"
            type: "bool"
            read: "isRunning"
            write: "setRunning"
            notify: "runningChanged"
            index: 0
        }
        Property {
            name: "paused"
            type: "bool"
            read: "isPaused"
            write: "setPaused"
            notify: "pausedChanged"
            index: 1
        }
        Property {
            name: "startTime"
            type: "int"
            read: "startTime"
            write: "setStartTime"
            notify: "startTimeChanged"
            index: 2
        }
        Property {
            name: "time"
            type: "int"
            read: "time"
            write: "setTime"
            notify: "timeChanged"
            index: 3
        }
        Property {
            name: "useRandomSeed"
            type: "bool"
            read: "useRandomSeed"
            write: "setUseRandomSeed"
            notify: "useRandomSeedChanged"
            index: 4
        }
        Property {
            name: "seed"
            type: "int"
            read: "seed"
            write: "setSeed"
            notify: "seedChanged"
            index: 5
        }
        Property {
            name: "logging"
            type: "bool"
            read: "logging"
            write: "setLogging"
            notify: "loggingChanged"
            index: 6
        }
        Property {
            name: "loggingData"
            type: "QQuick3DParticleSystemLogging"
            isPointer: true
            read: "loggingData"
            notify: "loggingDataChanged"
            index: 7
            isReadonly: true
        }
        Signal { name: "runningChanged" }
        Signal { name: "pausedChanged" }
        Signal { name: "timeChanged" }
        Signal { name: "startTimeChanged" }
        Signal { name: "useRandomSeedChanged" }
        Signal { name: "seedChanged" }
        Signal { name: "loggingChanged" }
        Signal { name: "loggingDataChanged" }
        Method {
            name: "setRunning"
            Parameter { name: "running"; type: "bool" }
        }
        Method {
            name: "setPaused"
            Parameter { name: "paused"; type: "bool" }
        }
        Method {
            name: "setStartTime"
            Parameter { name: "startTime"; type: "int" }
        }
        Method {
            name: "setTime"
            Parameter { name: "time"; type: "int" }
        }
        Method {
            name: "setUseRandomSeed"
            Parameter { name: "randomize"; type: "bool" }
        }
        Method {
            name: "setSeed"
            Parameter { name: "seed"; type: "int" }
        }
        Method {
            name: "setLogging"
            Parameter { name: "logging"; type: "bool" }
        }
        Method {
            name: "setEditorTime"
            Parameter { name: "time"; type: "int" }
        }
        Method { name: "reset" }
    }
    Component {
        file: "private/qquick3dparticlesystemlogging_p.h"
        name: "QQuick3DParticleSystemLogging"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "loggingInterval"
            type: "int"
            read: "loggingInterval"
            write: "setLoggingInterval"
            notify: "loggingIntervalChanged"
            index: 0
        }
        Property {
            name: "updates"
            type: "int"
            read: "updates"
            notify: "updatesChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "particlesMax"
            type: "int"
            read: "particlesMax"
            notify: "particlesMaxChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "particlesUsed"
            type: "int"
            read: "particlesUsed"
            notify: "particlesUsedChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "time"
            type: "float"
            read: "time"
            notify: "timeChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "timeAverage"
            type: "float"
            read: "timeAverage"
            notify: "timeAverageChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "timeDeviation"
            revision: 1539
            type: "float"
            read: "timeDeviation"
            notify: "timeDeviationChanged"
            index: 6
            isReadonly: true
        }
        Signal { name: "loggingIntervalChanged" }
        Signal { name: "updatesChanged" }
        Signal { name: "particlesMaxChanged" }
        Signal { name: "particlesUsedChanged" }
        Signal { name: "timeChanged" }
        Signal { name: "timeAverageChanged" }
        Signal { name: "timeDeviationChanged"; revision: 1539 }
        Method {
            name: "setLoggingInterval"
            Parameter { name: "interval"; type: "int" }
        }
    }
    Component {
        file: "private/qquick3dparticletargetdirection_p.h"
        name: "QQuick3DParticleTargetDirection"
        accessSemantics: "reference"
        prototype: "QQuick3DParticleDirection"
        exports: ["QtQuick3D.Particles3D/TargetDirection3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "position"
            type: "QVector3D"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 0
        }
        Property {
            name: "positionVariation"
            type: "QVector3D"
            read: "positionVariation"
            write: "setPositionVariation"
            notify: "positionVariationChanged"
            index: 1
        }
        Property {
            name: "normalized"
            type: "bool"
            read: "normalized"
            write: "setNormalized"
            notify: "normalizedChanged"
            index: 2
        }
        Property {
            name: "magnitude"
            type: "float"
            read: "magnitude"
            write: "setMagnitude"
            notify: "magnitudeChanged"
            index: 3
        }
        Property {
            name: "magnitudeVariation"
            type: "float"
            read: "magnitudeVariation"
            write: "setMagnitudeVariation"
            notify: "magnitudeChangedVariation"
            index: 4
        }
        Signal { name: "positionChanged" }
        Signal { name: "positionVariationChanged" }
        Signal { name: "normalizedChanged" }
        Signal { name: "magnitudeChanged" }
        Signal { name: "magnitudeChangedVariation" }
        Method {
            name: "setPositionVariation"
            Parameter { name: "positionVariation"; type: "QVector3D" }
        }
        Method {
            name: "setNormalized"
            Parameter { name: "normalized"; type: "bool" }
        }
        Method {
            name: "setMagnitude"
            Parameter { name: "magnitude"; type: "float" }
        }
        Method {
            name: "setMagnitudeVariation"
            Parameter { name: "magnitudeVariation"; type: "float" }
        }
    }
    Component {
        file: "private/qquick3dparticletrailemitter_p.h"
        name: "QQuick3DParticleTrailEmitter"
        accessSemantics: "reference"
        prototype: "QQuick3DParticleEmitter"
        exports: ["QtQuick3D.Particles3D/TrailEmitter3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "follow"
            type: "QQuick3DParticle"
            isPointer: true
            read: "follow"
            write: "setFollow"
            notify: "followChanged"
            index: 0
        }
        Signal { name: "followChanged" }
        Method {
            name: "setFollow"
            Parameter { name: "follow"; type: "QQuick3DParticle"; isPointer: true }
        }
        Method {
            name: "burst"
            Parameter { name: "count"; type: "int" }
        }
    }
    Component {
        file: "private/qquick3dparticlevectordirection_p.h"
        name: "QQuick3DParticleVectorDirection"
        accessSemantics: "reference"
        prototype: "QQuick3DParticleDirection"
        exports: ["QtQuick3D.Particles3D/VectorDirection3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "direction"
            type: "QVector3D"
            read: "direction"
            write: "setDirection"
            notify: "directionChanged"
            index: 0
        }
        Property {
            name: "directionVariation"
            type: "QVector3D"
            read: "directionVariation"
            write: "setDirectionVariation"
            notify: "directionVariationChanged"
            index: 1
        }
        Property {
            name: "normalized"
            type: "bool"
            read: "normalized"
            write: "setNormalized"
            notify: "normalizedChanged"
            index: 2
        }
        Signal { name: "directionChanged" }
        Signal { name: "directionVariationChanged" }
        Signal { name: "normalizedChanged" }
        Method {
            name: "setDirection"
            Parameter { name: "direction"; type: "QVector3D" }
        }
        Method {
            name: "setDirectionVariation"
            Parameter { name: "directionVariation"; type: "QVector3D" }
        }
        Method {
            name: "setNormalized"
            Parameter { name: "normalized"; type: "bool" }
        }
    }
    Component {
        file: "private/qquick3dparticlewander_p.h"
        name: "QQuick3DParticleWander"
        accessSemantics: "reference"
        prototype: "QQuick3DParticleAffector"
        exports: ["QtQuick3D.Particles3D/Wander3D 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "globalAmount"
            type: "QVector3D"
            read: "globalAmount"
            write: "setGlobalAmount"
            notify: "globalAmountChanged"
            index: 0
        }
        Property {
            name: "globalPace"
            type: "QVector3D"
            read: "globalPace"
            write: "setGlobalPace"
            notify: "globalPaceChanged"
            index: 1
        }
        Property {
            name: "globalPaceStart"
            type: "QVector3D"
            read: "globalPaceStart"
            write: "setGlobalPaceStart"
            notify: "globalPaceStartChanged"
            index: 2
        }
        Property {
            name: "uniqueAmount"
            type: "QVector3D"
            read: "uniqueAmount"
            write: "setUniqueAmount"
            notify: "uniqueAmountChanged"
            index: 3
        }
        Property {
            name: "uniquePace"
            type: "QVector3D"
            read: "uniquePace"
            write: "setUniquePace"
            notify: "uniquePaceChanged"
            index: 4
        }
        Property {
            name: "uniqueAmountVariation"
            type: "float"
            read: "uniqueAmountVariation"
            write: "setUniqueAmountVariation"
            notify: "uniqueAmountVariationChanged"
            index: 5
        }
        Property {
            name: "uniquePaceVariation"
            type: "float"
            read: "uniquePaceVariation"
            write: "setUniquePaceVariation"
            notify: "uniquePaceVariationChanged"
            index: 6
        }
        Property {
            name: "fadeInDuration"
            type: "int"
            read: "fadeInDuration"
            write: "setFadeInDuration"
            notify: "fadeInDurationChanged"
            index: 7
        }
        Property {
            name: "fadeOutDuration"
            type: "int"
            read: "fadeOutDuration"
            write: "setFadeOutDuration"
            notify: "fadeOutDurationChanged"
            index: 8
        }
        Signal { name: "globalAmountChanged" }
        Signal { name: "globalPaceChanged" }
        Signal { name: "globalPaceStartChanged" }
        Signal { name: "uniqueAmountChanged" }
        Signal { name: "uniquePaceChanged" }
        Signal { name: "uniqueAmountVariationChanged" }
        Signal { name: "uniquePaceVariationChanged" }
        Signal { name: "fadeInDurationChanged" }
        Signal { name: "fadeOutDurationChanged" }
        Method {
            name: "setGlobalAmount"
            Parameter { name: "globalAmount"; type: "QVector3D" }
        }
        Method {
            name: "setGlobalPace"
            Parameter { name: "globalPace"; type: "QVector3D" }
        }
        Method {
            name: "setGlobalPaceStart"
            Parameter { name: "globalPaceStart"; type: "QVector3D" }
        }
        Method {
            name: "setUniqueAmount"
            Parameter { name: "uniqueAmount"; type: "QVector3D" }
        }
        Method {
            name: "setUniquePace"
            Parameter { name: "uniquePace"; type: "QVector3D" }
        }
        Method {
            name: "setUniqueAmountVariation"
            Parameter { name: "uniqueAmountVariation"; type: "float" }
        }
        Method {
            name: "setUniquePaceVariation"
            Parameter { name: "uniquePaceVariation"; type: "float" }
        }
        Method {
            name: "setFadeInDuration"
            Parameter { name: "fadeInDuration"; type: "int" }
        }
        Method {
            name: "setFadeOutDuration"
            Parameter { name: "fadeOutDuration"; type: "int" }
        }
    }
}
