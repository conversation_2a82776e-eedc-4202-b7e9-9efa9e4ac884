// qreadwritelock.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QReadWriteLock
{
%TypeHeaderCode
#include <qreadwritelock.h>
%End

public:
    enum RecursionMode
    {
        NonRecursive,
        Recursive,
    };

    explicit QReadWriteLock(QReadWriteLock::RecursionMode recursionMode = QReadWriteLock::NonRecursive);
    ~QReadWriteLock();
    void lockForRead() /ReleaseGIL/;
%If (Qt_6_6_0 -)
    bool tryLockForRead(QDeadlineTimer timeout = {}) /ReleaseGIL/;
%End
%If (- Qt_6_6_0)
    bool tryLockForRead();
%End
    bool tryLockForRead(int timeout) /ReleaseGIL/;
    void lockForWrite() /ReleaseGIL/;
%If (Qt_6_6_0 -)
    bool tryLockForWrite(QDeadlineTimer timeout = {}) /ReleaseGIL/;
%End
%If (- Qt_6_6_0)
    bool tryLockForWrite();
%End
    bool tryLockForWrite(int timeout) /ReleaseGIL/;
    void unlock();

private:
    QReadWriteLock(const QReadWriteLock &);
};

class QReadLocker
{
%TypeHeaderCode
#include <qreadwritelock.h>
%End

public:
    QReadLocker(QReadWriteLock *areadWriteLock) /ReleaseGIL/;
    ~QReadLocker();
    void unlock();
    void relock() /ReleaseGIL/;
    QReadWriteLock *readWriteLock() const;
    SIP_PYOBJECT __enter__();
%MethodCode
        // Just return a reference to self.
        sipRes = sipSelf;
        Py_INCREF(sipRes);
%End

    void __exit__(SIP_PYOBJECT type, SIP_PYOBJECT value, SIP_PYOBJECT traceback);
%MethodCode
        sipCpp->unlock();
%End

private:
    QReadLocker(const QReadLocker &);
};

class QWriteLocker
{
%TypeHeaderCode
#include <qreadwritelock.h>
%End

public:
    QWriteLocker(QReadWriteLock *areadWriteLock) /ReleaseGIL/;
    ~QWriteLocker();
    void unlock();
    void relock() /ReleaseGIL/;
    QReadWriteLock *readWriteLock() const;
    SIP_PYOBJECT __enter__();
%MethodCode
        // Just return a reference to self.
        sipRes = sipSelf;
        Py_INCREF(sipRes);
%End

    void __exit__(SIP_PYOBJECT type, SIP_PYOBJECT value, SIP_PYOBJECT traceback);
%MethodCode
        sipCpp->unlock();
%End

private:
    QWriteLocker(const QWriteLocker &);
};
