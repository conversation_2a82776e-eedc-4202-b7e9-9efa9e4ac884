// qqmlproperty.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQmlProperty
{
%TypeHeaderCode
#include <qqmlproperty.h>
%End

public:
    enum PropertyTypeCategory
    {
        InvalidCategory,
        List,
        Object,
        Normal,
    };

    enum Type
    {
        Invalid,
        Property,
        SignalProperty,
    };

    QQmlProperty();
    QQmlProperty(QObject *);
    QQmlProperty(QObject *, QQmlContext *);
    QQmlProperty(QObject *, QQmlEngine *);
    QQmlProperty(QObject *, const QString &);
    QQmlProperty(QObject *, const QString &, QQmlContext *);
    QQmlProperty(QObject *, const QString &, QQmlEngine *);
    QQmlProperty(const QQmlProperty &);
    ~QQmlProperty();
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    bool operator==(const QQmlProperty &) const;
    QQmlProperty::Type type() const;
    bool isValid() const;
    bool isProperty() const;
    bool isSignalProperty() const;
%If (Qt_6_2_0 -)
    bool isBindable() const;
%End
    int propertyType() const;
    QQmlProperty::PropertyTypeCategory propertyTypeCategory() const;
    const char *propertyTypeName() const;
%If (Qt_6_1_0 -)
    QMetaType propertyMetaType() const;
%End
    QString name() const;
    QVariant read() const;
    static QVariant read(const QObject *, const QString &);
    static QVariant read(const QObject *, const QString &, QQmlContext *);
    static QVariant read(const QObject *, const QString &, QQmlEngine *);
    bool write(const QVariant &) const;
    static bool write(QObject *, const QString &, const QVariant &);
    static bool write(QObject *, const QString &, const QVariant &, QQmlContext *);
    static bool write(QObject *, const QString &, const QVariant &, QQmlEngine *);
    bool reset() const;
    bool hasNotifySignal() const;
    bool needsNotifySignal() const;
    bool connectNotifySignal(SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/) const;
%MethodCode
        QObject *receiver;
        QByteArray slot;
        
        if ((sipError = pyqt6_qtqml_get_connection_parts(a0, 0, "()", false, &receiver, slot)) == sipErrorNone)
        {
            sipRes = sipCpp->connectNotifySignal(receiver, slot.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(0, a0);
        }
%End

    bool connectNotifySignal(QObject *dest, int method) const;
    bool isWritable() const;
    bool isDesignable() const;
    bool isResettable() const;
    QObject *object() const;
    int index() const;
    QMetaProperty property() const;
    QMetaMethod method() const;
%If (Qt_6_3_0 -)
    void swap(QQmlProperty &other);
%End
};

typedef QList<QQmlProperty> QQmlProperties;

%ModuleHeaderCode
// Imports from QtCore.
typedef sipErrorState (*pyqt6_qtqml_get_connection_parts_t)(PyObject *, QObject *, const char *, bool, QObject **, QByteArray &);
extern pyqt6_qtqml_get_connection_parts_t pyqt6_qtqml_get_connection_parts;
%End

%ModuleCode
// Imports from QtCore.
pyqt6_qtqml_get_connection_parts_t pyqt6_qtqml_get_connection_parts;
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt6_qtqml_get_connection_parts = (pyqt6_qtqml_get_connection_parts_t)sipImportSymbol("pyqt6_get_connection_parts");
Q_ASSERT(pyqt6_qtqml_get_connection_parts);
%End
