// qdbusservicewatcher.sip generated by MetaSIP
//
// This file is part of the QtDBus Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDBusServiceWatcher : public QObject
{
%TypeHeaderCode
#include <qdbusservicewatcher.h>
%End

public:
    enum WatchModeFlag /BaseType=Flag/
    {
        WatchForRegistration,
        WatchForUnregistration,
        WatchForOwnerChange,
    };

    typedef QFlags<QDBusServiceWatcher::WatchModeFlag> WatchMode;
    explicit QDBusServiceWatcher(QObject *parent /TransferThis/ = 0);
    QDBusServiceWatcher(const QString &service, const QDBusConnection &connection, QDBusServiceWatcher::WatchMode watchMode = QDBusServiceWatcher::WatchForOwnerChange, QObject *parent /TransferThis/ = 0);
    virtual ~QDBusServiceWatcher();
    QStringList watchedServices() const;
    void setWatchedServices(const QStringList &services);
    void addWatchedService(const QString &newService);
    bool removeWatchedService(const QString &service);
    QDBusServiceWatcher::WatchMode watchMode() const;
    void setWatchMode(QDBusServiceWatcher::WatchMode mode);
    QDBusConnection connection() const;
    void setConnection(const QDBusConnection &connection);

signals:
    void serviceRegistered(const QString &service);
    void serviceUnregistered(const QString &service);
    void serviceOwnerChanged(const QString &service, const QString &oldOwner, const QString &newOwner);
};
