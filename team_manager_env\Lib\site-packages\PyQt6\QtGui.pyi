# The PEP 484 type hints stub file for the QtGui module.
#
# Generated by SIP 6.12.0
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt6.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICENSE included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import collections, re, typing, enum

try:
    from warnings import deprecated
except ImportError:
    pass

import PyQt6.sip

from PyQt6 import QtCore

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[collections.abc.Callable[..., Any], QtCore.pyqtBoundSignal]


class QAbstractFileIconProvider(PyQt6.sip.simplewrapper):

    class Option(enum.Flag):
        DontUseCustomDirectoryIcons = ... # type: QAbstractFileIconProvider.Option

    class IconType(enum.Enum):
        Computer = ... # type: QAbstractFileIconProvider.IconType
        Desktop = ... # type: QAbstractFileIconProvider.IconType
        Trashcan = ... # type: QAbstractFileIconProvider.IconType
        Network = ... # type: QAbstractFileIconProvider.IconType
        Drive = ... # type: QAbstractFileIconProvider.IconType
        Folder = ... # type: QAbstractFileIconProvider.IconType
        File = ... # type: QAbstractFileIconProvider.IconType

    def __init__(self) -> None: ...

    def options(self) -> 'QAbstractFileIconProvider.Option': ...
    def setOptions(self, a0: 'QAbstractFileIconProvider.Option') -> None: ...
    def type(self, a0: QtCore.QFileInfo) -> str: ...
    @typing.overload
    def icon(self, a0: 'QAbstractFileIconProvider.IconType') -> 'QIcon': ...
    @typing.overload
    def icon(self, a0: QtCore.QFileInfo) -> 'QIcon': ...


class QAbstractTextDocumentLayout(QtCore.QObject):

    class Selection(PyQt6.sip.simplewrapper):

        cursor = ... # type: 'QTextCursor'
        format = ... # type: 'QTextCharFormat'

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QAbstractTextDocumentLayout.Selection') -> None: ...

    class PaintContext(PyQt6.sip.simplewrapper):

        clip = ... # type: QtCore.QRectF
        cursorPosition = ... # type: int
        palette = ... # type: 'QPalette'
        selections = ... # type: collections.abc.Iterable['QAbstractTextDocumentLayout.Selection']

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QAbstractTextDocumentLayout.PaintContext') -> None: ...

    def __init__(self, doc: typing.Optional['QTextDocument']) -> None: ...

    def blockWithMarkerAt(self, pos: QtCore.QPointF) -> 'QTextBlock': ...
    def formatAt(self, pos: QtCore.QPointF) -> 'QTextFormat': ...
    def imageAt(self, pos: QtCore.QPointF) -> str: ...
    def format(self, pos: int) -> 'QTextCharFormat': ...
    def drawInlineObject(self, painter: typing.Optional['QPainter'], rect: QtCore.QRectF, object: 'QTextInlineObject', posInDocument: int, format: 'QTextFormat') -> None: ...
    def positionInlineObject(self, item: 'QTextInlineObject', posInDocument: int, format: 'QTextFormat') -> None: ...
    def resizeInlineObject(self, item: 'QTextInlineObject', posInDocument: int, format: 'QTextFormat') -> None: ...
    def documentChanged(self, from_: int, charsRemoved: int, charsAdded: int) -> None: ...
    updateBlock: typing.ClassVar[QtCore.pyqtSignal]
    pageCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    documentSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    update: typing.ClassVar[QtCore.pyqtSignal]
    def handlerForObject(self, objectType: int) -> typing.Optional['QTextObjectInterface']: ...
    def unregisterHandler(self, objectType: int, component: typing.Optional[QtCore.QObject] = ...) -> None: ...
    def registerHandler(self, objectType: int, component: typing.Optional[QtCore.QObject]) -> None: ...
    def document(self) -> typing.Optional['QTextDocument']: ...
    def paintDevice(self) -> typing.Optional['QPaintDevice']: ...
    def setPaintDevice(self, device: typing.Optional['QPaintDevice']) -> None: ...
    def blockBoundingRect(self, block: 'QTextBlock') -> QtCore.QRectF: ...
    def frameBoundingRect(self, frame: typing.Optional['QTextFrame']) -> QtCore.QRectF: ...
    def documentSize(self) -> QtCore.QSizeF: ...
    def pageCount(self) -> int: ...
    def anchorAt(self, pos: QtCore.QPointF) -> str: ...
    def hitTest(self, point: QtCore.QPointF, accuracy: QtCore.Qt.HitTestAccuracy) -> int: ...
    def draw(self, painter: typing.Optional['QPainter'], context: 'QAbstractTextDocumentLayout.PaintContext') -> None: ...


class QTextObjectInterface(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextObjectInterface') -> None: ...

    def drawObject(self, painter: typing.Optional['QPainter'], rect: QtCore.QRectF, doc: typing.Optional['QTextDocument'], posInDocument: int, format: 'QTextFormat') -> None: ...
    def intrinsicSize(self, doc: typing.Optional['QTextDocument'], posInDocument: int, format: 'QTextFormat') -> QtCore.QSizeF: ...


class QAction(QtCore.QObject):

    class Priority(enum.Enum):
        LowPriority = ... # type: QAction.Priority
        NormalPriority = ... # type: QAction.Priority
        HighPriority = ... # type: QAction.Priority

    class MenuRole(enum.Enum):
        NoRole = ... # type: QAction.MenuRole
        TextHeuristicRole = ... # type: QAction.MenuRole
        ApplicationSpecificRole = ... # type: QAction.MenuRole
        AboutQtRole = ... # type: QAction.MenuRole
        AboutRole = ... # type: QAction.MenuRole
        PreferencesRole = ... # type: QAction.MenuRole
        QuitRole = ... # type: QAction.MenuRole

    class ActionEvent(enum.Enum):
        Trigger = ... # type: QAction.ActionEvent
        Hover = ... # type: QAction.ActionEvent

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, text: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, icon: 'QIcon', text: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    visibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    checkableChanged: typing.ClassVar[QtCore.pyqtSignal]
    enabledChanged: typing.ClassVar[QtCore.pyqtSignal]
    def resetEnabled(self) -> None: ...
    def associatedObjects(self) -> list[QtCore.QObject]: ...
    def isShortcutVisibleInContextMenu(self) -> bool: ...
    def setShortcutVisibleInContextMenu(self, show: bool) -> None: ...
    def priority(self) -> 'QAction.Priority': ...
    def setPriority(self, priority: 'QAction.Priority') -> None: ...
    def isIconVisibleInMenu(self) -> bool: ...
    def setIconVisibleInMenu(self, visible: bool) -> None: ...
    def setMenu(self, menu: typing.Optional[QMenu]) -> None: ...
    def menu(self) -> typing.Optional[QMenu]: ...
    def menuRole(self) -> 'QAction.MenuRole': ...
    def setMenuRole(self, menuRole: 'QAction.MenuRole') -> None: ...
    def autoRepeat(self) -> bool: ...
    def setAutoRepeat(self, a0: bool) -> None: ...
    def shortcuts(self) -> list['QKeySequence']: ...
    @typing.overload
    def setShortcuts(self, shortcuts: collections.abc.Iterable[typing.Union['QKeySequence', 'QKeySequence.StandardKey', typing.Optional[str], int]]) -> None: ...
    @typing.overload
    def setShortcuts(self, a0: 'QKeySequence.StandardKey') -> None: ...
    toggled: typing.ClassVar[QtCore.pyqtSignal]
    hovered: typing.ClassVar[QtCore.pyqtSignal]
    triggered: typing.ClassVar[QtCore.pyqtSignal]
    changed: typing.ClassVar[QtCore.pyqtSignal]
    def setVisible(self, a0: bool) -> None: ...
    def setDisabled(self, b: bool) -> None: ...
    def setEnabled(self, a0: bool) -> None: ...
    def toggle(self) -> None: ...
    def setChecked(self, a0: bool) -> None: ...
    def hover(self) -> None: ...
    def trigger(self) -> None: ...
    def event(self, a0: typing.Optional[QtCore.QEvent]) -> bool: ...
    def showStatusText(self, object: typing.Optional[QtCore.QObject] = ...) -> bool: ...
    def activate(self, event: 'QAction.ActionEvent') -> None: ...
    def isVisible(self) -> bool: ...
    def isEnabled(self) -> bool: ...
    def isChecked(self) -> bool: ...
    def setData(self, var: typing.Any) -> None: ...
    def data(self) -> typing.Any: ...
    def isCheckable(self) -> bool: ...
    def setCheckable(self, a0: bool) -> None: ...
    def font(self) -> 'QFont': ...
    def setFont(self, font: 'QFont') -> None: ...
    def shortcutContext(self) -> QtCore.Qt.ShortcutContext: ...
    def setShortcutContext(self, context: QtCore.Qt.ShortcutContext) -> None: ...
    def shortcut(self) -> 'QKeySequence': ...
    def setShortcut(self, shortcut: typing.Union['QKeySequence', 'QKeySequence.StandardKey', typing.Optional[str], int]) -> None: ...
    def isSeparator(self) -> bool: ...
    def setSeparator(self, b: bool) -> None: ...
    def whatsThis(self) -> str: ...
    def setWhatsThis(self, what: typing.Optional[str]) -> None: ...
    def statusTip(self) -> str: ...
    def setStatusTip(self, statusTip: typing.Optional[str]) -> None: ...
    def toolTip(self) -> str: ...
    def setToolTip(self, tip: typing.Optional[str]) -> None: ...
    def iconText(self) -> str: ...
    def setIconText(self, text: typing.Optional[str]) -> None: ...
    def text(self) -> str: ...
    def setText(self, text: typing.Optional[str]) -> None: ...
    def icon(self) -> 'QIcon': ...
    def setIcon(self, icon: 'QIcon') -> None: ...
    def actionGroup(self) -> typing.Optional['QActionGroup']: ...
    def setActionGroup(self, group: typing.Optional['QActionGroup']) -> None: ...


class QActionGroup(QtCore.QObject):

    class ExclusionPolicy(enum.Enum):
        None_ = ... # type: QActionGroup.ExclusionPolicy
        Exclusive = ... # type: QActionGroup.ExclusionPolicy
        ExclusiveOptional = ... # type: QActionGroup.ExclusionPolicy

    def __init__(self, parent: typing.Optional[QtCore.QObject]) -> None: ...

    hovered: typing.ClassVar[QtCore.pyqtSignal]
    triggered: typing.ClassVar[QtCore.pyqtSignal]
    def setExclusionPolicy(self, policy: 'QActionGroup.ExclusionPolicy') -> None: ...
    def setExclusive(self, a0: bool) -> None: ...
    def setVisible(self, a0: bool) -> None: ...
    def setDisabled(self, b: bool) -> None: ...
    def setEnabled(self, a0: bool) -> None: ...
    def exclusionPolicy(self) -> 'QActionGroup.ExclusionPolicy': ...
    def isVisible(self) -> bool: ...
    def isEnabled(self) -> bool: ...
    def isExclusive(self) -> bool: ...
    def checkedAction(self) -> typing.Optional[QAction]: ...
    def actions(self) -> list[QAction]: ...
    def removeAction(self, a: typing.Optional[QAction]) -> None: ...
    @typing.overload
    def addAction(self, a: typing.Optional[QAction]) -> typing.Optional[QAction]: ...
    @typing.overload
    def addAction(self, text: typing.Optional[str]) -> typing.Optional[QAction]: ...
    @typing.overload
    def addAction(self, icon: 'QIcon', text: typing.Optional[str]) -> typing.Optional[QAction]: ...


class QBackingStore(PyQt6.sip.simplewrapper):

    def __init__(self, window: typing.Optional['QWindow']) -> None: ...

    def hasStaticContents(self) -> bool: ...
    def staticContents(self) -> 'QRegion': ...
    def setStaticContents(self, region: 'QRegion') -> None: ...
    def endPaint(self) -> None: ...
    def beginPaint(self, a0: 'QRegion') -> None: ...
    def scroll(self, area: 'QRegion', dx: int, dy: int) -> bool: ...
    def size(self) -> QtCore.QSize: ...
    def resize(self, size: QtCore.QSize) -> None: ...
    def flush(self, region: 'QRegion', window: typing.Optional['QWindow'] = ..., offset: QtCore.QPoint = ...) -> None: ...
    def paintDevice(self) -> typing.Optional['QPaintDevice']: ...
    def window(self) -> typing.Optional['QWindow']: ...


class QPaintDevice(PyQt6.sip.simplewrapper):

    class PaintDeviceMetric(enum.Enum):
        PdmWidth = ... # type: QPaintDevice.PaintDeviceMetric
        PdmHeight = ... # type: QPaintDevice.PaintDeviceMetric
        PdmWidthMM = ... # type: QPaintDevice.PaintDeviceMetric
        PdmHeightMM = ... # type: QPaintDevice.PaintDeviceMetric
        PdmNumColors = ... # type: QPaintDevice.PaintDeviceMetric
        PdmDepth = ... # type: QPaintDevice.PaintDeviceMetric
        PdmDpiX = ... # type: QPaintDevice.PaintDeviceMetric
        PdmDpiY = ... # type: QPaintDevice.PaintDeviceMetric
        PdmPhysicalDpiX = ... # type: QPaintDevice.PaintDeviceMetric
        PdmPhysicalDpiY = ... # type: QPaintDevice.PaintDeviceMetric
        PdmDevicePixelRatio = ... # type: QPaintDevice.PaintDeviceMetric
        PdmDevicePixelRatioScaled = ... # type: QPaintDevice.PaintDeviceMetric
        PdmDevicePixelRatioF_EncodedA = ... # type: QPaintDevice.PaintDeviceMetric
        PdmDevicePixelRatioF_EncodedB = ... # type: QPaintDevice.PaintDeviceMetric

    def __init__(self) -> None: ...

    @staticmethod
    def devicePixelRatioFScale() -> float: ...
    def devicePixelRatioF(self) -> float: ...
    def metric(self, metric: 'QPaintDevice.PaintDeviceMetric') -> int: ...
    def devicePixelRatio(self) -> float: ...
    def colorCount(self) -> int: ...
    def paintingActive(self) -> bool: ...
    def depth(self) -> int: ...
    def physicalDpiY(self) -> int: ...
    def physicalDpiX(self) -> int: ...
    def logicalDpiY(self) -> int: ...
    def logicalDpiX(self) -> int: ...
    def heightMM(self) -> int: ...
    def widthMM(self) -> int: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def paintEngine(self) -> typing.Optional['QPaintEngine']: ...


class QPixmap(QPaintDevice):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, w: int, h: int) -> None: ...
    @typing.overload
    def __init__(self, a0: QtCore.QSize) -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str], format: typing.Optional[str] = ..., flags: QtCore.Qt.ImageConversionFlag = ...) -> None: ...
    @typing.overload
    def __init__(self, xpm: list[str]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPixmap') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def deviceIndependentSize(self) -> QtCore.QSizeF: ...
    def setDevicePixelRatio(self, scaleFactor: float) -> None: ...
    def devicePixelRatio(self) -> float: ...
    def swap(self, other: 'QPixmap') -> None: ...
    @typing.overload
    def scroll(self, dx: int, dy: int, rect: QtCore.QRect) -> typing.Optional['QRegion']: ...
    @typing.overload
    def scroll(self, dx: int, dy: int, x: int, y: int, width: int, height: int) -> typing.Optional['QRegion']: ...
    def cacheKey(self) -> int: ...
    @staticmethod
    def trueMatrix(m: 'QTransform', w: int, h: int) -> 'QTransform': ...
    def transformed(self, transform: 'QTransform', mode: QtCore.Qt.TransformationMode = ...) -> 'QPixmap': ...
    def metric(self, a0: QPaintDevice.PaintDeviceMetric) -> int: ...
    def paintEngine(self) -> typing.Optional['QPaintEngine']: ...
    def isQBitmap(self) -> bool: ...
    def detach(self) -> None: ...
    @typing.overload
    def copy(self, rect: QtCore.QRect = ...) -> 'QPixmap': ...
    @typing.overload
    def copy(self, ax: int, ay: int, awidth: int, aheight: int) -> 'QPixmap': ...
    @typing.overload
    def save(self, fileName: typing.Optional[str], format: typing.Optional[str] = ..., quality: int = ...) -> bool: ...
    @typing.overload
    def save(self, device: typing.Optional[QtCore.QIODevice], format: typing.Optional[str] = ..., quality: int = ...) -> bool: ...
    @typing.overload
    def loadFromData(self, buf: typing.Optional[PyQt6.sip.array[bytes]], format: typing.Optional[str] = ..., flags: QtCore.Qt.ImageConversionFlag = ...) -> bool: ...
    @typing.overload
    def loadFromData(self, buf: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], format: typing.Optional[str] = ..., flags: QtCore.Qt.ImageConversionFlag = ...) -> bool: ...
    def load(self, fileName: typing.Optional[str], format: typing.Optional[str] = ..., flags: QtCore.Qt.ImageConversionFlag = ...) -> bool: ...
    def convertFromImage(self, img: 'QImage', flags: QtCore.Qt.ImageConversionFlag = ...) -> bool: ...
    @staticmethod
    def fromImageReader(imageReader: typing.Optional['QImageReader'], flags: QtCore.Qt.ImageConversionFlag = ...) -> 'QPixmap': ...
    @staticmethod
    def fromImage(image: 'QImage', flags: QtCore.Qt.ImageConversionFlag = ...) -> 'QPixmap': ...
    def toImage(self) -> 'QImage': ...
    def scaledToHeight(self, height: int, mode: QtCore.Qt.TransformationMode = ...) -> 'QPixmap': ...
    def scaledToWidth(self, width: int, mode: QtCore.Qt.TransformationMode = ...) -> 'QPixmap': ...
    @typing.overload
    def scaled(self, width: int, height: int, aspectRatioMode: QtCore.Qt.AspectRatioMode = ..., transformMode: QtCore.Qt.TransformationMode = ...) -> 'QPixmap': ...
    @typing.overload
    def scaled(self, size: QtCore.QSize, aspectRatioMode: QtCore.Qt.AspectRatioMode = ..., transformMode: QtCore.Qt.TransformationMode = ...) -> 'QPixmap': ...
    def createMaskFromColor(self, maskColor: typing.Union['QColor', QtCore.Qt.GlobalColor, int], mode: QtCore.Qt.MaskMode = ...) -> 'QBitmap': ...
    def createHeuristicMask(self, clipTight: bool = ...) -> 'QBitmap': ...
    def hasAlphaChannel(self) -> bool: ...
    def hasAlpha(self) -> bool: ...
    def setMask(self, a0: 'QBitmap') -> None: ...
    def mask(self) -> 'QBitmap': ...
    def fill(self, color: typing.Union['QColor', QtCore.Qt.GlobalColor, int] = ...) -> None: ...
    @staticmethod
    def defaultDepth() -> int: ...
    def depth(self) -> int: ...
    def rect(self) -> QtCore.QRect: ...
    def size(self) -> QtCore.QSize: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def devType(self) -> int: ...
    def isNull(self) -> bool: ...


class QBitmap(QPixmap):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, w: int, h: int) -> None: ...
    @typing.overload
    def __init__(self, a0: QtCore.QSize) -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str], format: typing.Optional[str] = ...) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QBitmap') -> None: ...

    def swap(self, other: 'QBitmap') -> None: ...
    def transformed(self, matrix: 'QTransform') -> 'QBitmap': ...
    @staticmethod
    def fromData(size: QtCore.QSize, bits: typing.Optional[bytes], format: 'QImage.Format' = ...) -> 'QBitmap': ...
    @staticmethod
    def fromPixmap(pixmap: QPixmap) -> 'QBitmap': ...
    @staticmethod
    def fromImage(image: 'QImage', flags: QtCore.Qt.ImageConversionFlag = ...) -> 'QBitmap': ...
    def clear(self) -> None: ...


class QColor(PyQt6.sip.simplewrapper):

    class NameFormat(enum.Enum):
        HexRgb = ... # type: QColor.NameFormat
        HexArgb = ... # type: QColor.NameFormat

    class Spec(enum.Enum):
        Invalid = ... # type: QColor.Spec
        Rgb = ... # type: QColor.Spec
        Hsv = ... # type: QColor.Spec
        Cmyk = ... # type: QColor.Spec
        Hsl = ... # type: QColor.Spec
        ExtendedRgb = ... # type: QColor.Spec

    @typing.overload
    def __init__(self, color: QtCore.Qt.GlobalColor) -> None: ...
    @typing.overload
    def __init__(self, rgb: int) -> None: ...
    @typing.overload
    def __init__(self, rgba64: 'QRgba64') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...
    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, r: int, g: int, b: int, alpha: int = ...) -> None: ...
    @typing.overload
    def __init__(self, name: str) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QColor') -> None: ...

    @staticmethod
    def isValidColorName(a0: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> bool: ...
    @staticmethod
    def fromString(name: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> 'QColor': ...
    def toExtendedRgb(self) -> 'QColor': ...
    @typing.overload
    @staticmethod
    def fromRgba64(r: int, g: int, b: int, alpha: int = ...) -> 'QColor': ...
    @typing.overload
    @staticmethod
    def fromRgba64(rgba: 'QRgba64') -> 'QColor': ...
    def setRgba64(self, rgba: 'QRgba64') -> None: ...
    def rgba64(self) -> 'QRgba64': ...
    @staticmethod
    def isValidColor(name: typing.Optional[str]) -> bool: ...
    @staticmethod
    def fromHslF(h: float, s: float, l: float, alpha: float = ...) -> 'QColor': ...
    @staticmethod
    def fromHsl(h: int, s: int, l: int, alpha: int = ...) -> 'QColor': ...
    def toHsl(self) -> 'QColor': ...
    def setHslF(self, h: float, s: float, l: float, alpha: float = ...) -> None: ...
    def getHslF(self) -> typing.Tuple[typing.Optional[float], typing.Optional[float], typing.Optional[float], typing.Optional[float]]: ...
    def setHsl(self, h: int, s: int, l: int, alpha: int = ...) -> None: ...
    def getHsl(self) -> typing.Tuple[typing.Optional[int], typing.Optional[int], typing.Optional[int], typing.Optional[int]]: ...
    def lightnessF(self) -> float: ...
    def hslSaturationF(self) -> float: ...
    def hslHueF(self) -> float: ...
    def lightness(self) -> int: ...
    def hslSaturation(self) -> int: ...
    def hslHue(self) -> int: ...
    def hsvSaturationF(self) -> float: ...
    def hsvHueF(self) -> float: ...
    def hsvSaturation(self) -> int: ...
    def hsvHue(self) -> int: ...
    def darker(self, factor: int = ...) -> 'QColor': ...
    def lighter(self, factor: int = ...) -> 'QColor': ...
    def isValid(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @staticmethod
    def fromCmykF(c: float, m: float, y: float, k: float, alpha: float = ...) -> 'QColor': ...
    @staticmethod
    def fromCmyk(c: int, m: int, y: int, k: int, alpha: int = ...) -> 'QColor': ...
    @staticmethod
    def fromHsvF(h: float, s: float, v: float, alpha: float = ...) -> 'QColor': ...
    @staticmethod
    def fromHsv(h: int, s: int, v: int, alpha: int = ...) -> 'QColor': ...
    @staticmethod
    def fromRgbF(r: float, g: float, b: float, alpha: float = ...) -> 'QColor': ...
    @staticmethod
    def fromRgba(rgba: int) -> 'QColor': ...
    @typing.overload
    @staticmethod
    def fromRgb(rgb: int) -> 'QColor': ...
    @typing.overload
    @staticmethod
    def fromRgb(r: int, g: int, b: int, alpha: int = ...) -> 'QColor': ...
    def convertTo(self, colorSpec: 'QColor.Spec') -> 'QColor': ...
    def toCmyk(self) -> 'QColor': ...
    def toHsv(self) -> 'QColor': ...
    def toRgb(self) -> 'QColor': ...
    def setCmykF(self, c: float, m: float, y: float, k: float, alpha: float = ...) -> None: ...
    def getCmykF(self) -> typing.Tuple[typing.Optional[float], typing.Optional[float], typing.Optional[float], typing.Optional[float], typing.Optional[float]]: ...
    def setCmyk(self, c: int, m: int, y: int, k: int, alpha: int = ...) -> None: ...
    def getCmyk(self) -> typing.Tuple[typing.Optional[int], typing.Optional[int], typing.Optional[int], typing.Optional[int], typing.Optional[int]]: ...
    def blackF(self) -> float: ...
    def yellowF(self) -> float: ...
    def magentaF(self) -> float: ...
    def cyanF(self) -> float: ...
    def black(self) -> int: ...
    def yellow(self) -> int: ...
    def magenta(self) -> int: ...
    def cyan(self) -> int: ...
    def setHsvF(self, h: float, s: float, v: float, alpha: float = ...) -> None: ...
    def getHsvF(self) -> typing.Tuple[typing.Optional[float], typing.Optional[float], typing.Optional[float], typing.Optional[float]]: ...
    def setHsv(self, h: int, s: int, v: int, alpha: int = ...) -> None: ...
    def getHsv(self) -> typing.Tuple[typing.Optional[int], typing.Optional[int], typing.Optional[int], typing.Optional[int]]: ...
    def valueF(self) -> float: ...
    def saturationF(self) -> float: ...
    def hueF(self) -> float: ...
    def value(self) -> int: ...
    def saturation(self) -> int: ...
    def hue(self) -> int: ...
    def rgb(self) -> int: ...
    def setRgba(self, rgba: int) -> None: ...
    def rgba(self) -> int: ...
    def setRgbF(self, r: float, g: float, b: float, alpha: float = ...) -> None: ...
    def getRgbF(self) -> typing.Tuple[typing.Optional[float], typing.Optional[float], typing.Optional[float], typing.Optional[float]]: ...
    @typing.overload
    def setRgb(self, r: int, g: int, b: int, alpha: int = ...) -> None: ...
    @typing.overload
    def setRgb(self, rgb: int) -> None: ...
    def getRgb(self) -> typing.Tuple[typing.Optional[int], typing.Optional[int], typing.Optional[int], typing.Optional[int]]: ...
    def setBlueF(self, blue: float) -> None: ...
    def setGreenF(self, green: float) -> None: ...
    def setRedF(self, red: float) -> None: ...
    def blueF(self) -> float: ...
    def greenF(self) -> float: ...
    def redF(self) -> float: ...
    def setBlue(self, blue: int) -> None: ...
    def setGreen(self, green: int) -> None: ...
    def setRed(self, red: int) -> None: ...
    def blue(self) -> int: ...
    def green(self) -> int: ...
    def red(self) -> int: ...
    def setAlphaF(self, alpha: float) -> None: ...
    def alphaF(self) -> float: ...
    def setAlpha(self, alpha: int) -> None: ...
    def alpha(self) -> int: ...
    def spec(self) -> 'QColor.Spec': ...
    @staticmethod
    def colorNames() -> list[str]: ...
    def setNamedColor(self, name: str) -> None: ...
    def name(self, format: 'QColor.NameFormat' = ...) -> str: ...


class QColorConstants(PyQt6.sip.simplewrapper):

    class Svg(PyQt6.sip.simplewrapper):

        aliceblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        antiquewhite = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        aqua = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        aquamarine = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        azure = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        beige = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        bisque = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        black = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        blanchedalmond = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        blue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        blueviolet = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        brown = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        burlywood = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        cadetblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        chartreuse = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        chocolate = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        coral = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        cornflowerblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        cornsilk = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        crimson = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        cyan = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkcyan = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkgoldenrod = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkgray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkgreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkgrey = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkkhaki = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkmagenta = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkolivegreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkorange = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkorchid = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkred = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darksalmon = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkseagreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkslateblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkslategray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkslategrey = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkturquoise = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        darkviolet = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        deeppink = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        deepskyblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        dimgray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        dimgrey = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        dodgerblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        firebrick = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        floralwhite = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        forestgreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        fuchsia = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        gainsboro = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        ghostwhite = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        gold = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        goldenrod = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        gray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        green = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        greenyellow = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        grey = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        honeydew = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        hotpink = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        indianred = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        indigo = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        ivory = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        khaki = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lavender = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lavenderblush = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lawngreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lemonchiffon = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightcoral = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightcyan = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightgoldenrodyellow = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightgray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightgreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightgrey = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightpink = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightsalmon = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightseagreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightskyblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightslategray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightslategrey = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightsteelblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lightyellow = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        lime = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        limegreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        linen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        magenta = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        maroon = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        mediumaquamarine = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        mediumblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        mediumorchid = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        mediumpurple = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        mediumseagreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        mediumslateblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        mediumspringgreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        mediumturquoise = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        mediumvioletred = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        midnightblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        mintcream = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        mistyrose = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        moccasin = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        navajowhite = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        navy = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        oldlace = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        olive = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        olivedrab = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        orange = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        orangered = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        orchid = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        palegoldenrod = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        palegreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        paleturquoise = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        palevioletred = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        papayawhip = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        peachpuff = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        peru = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        pink = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        plum = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        powderblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        purple = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        red = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        rosybrown = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        royalblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        saddlebrown = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        salmon = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        sandybrown = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        seagreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        seashell = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        sienna = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        silver = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        skyblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        slateblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        slategray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        slategrey = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        snow = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        springgreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        steelblue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        tan = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        teal = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        thistle = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        tomato = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        turquoise = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        violet = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        wheat = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        white = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        whitesmoke = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        yellow = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
        yellowgreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]

    Black = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    Blue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    Color0 = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    Color1 = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    Cyan = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    DarkBlue = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    DarkCyan = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    DarkGray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    DarkGreen = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    DarkMagenta = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    DarkRed = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    DarkYellow = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    Gray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    Green = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    LightGray = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    Magenta = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    Red = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    Transparent = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    White = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]
    Yellow = ... # type: typing.Union[QColor, QtCore.Qt.GlobalColor, int]


class QBrush(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, bs: QtCore.Qt.BrushStyle) -> None: ...
    @typing.overload
    def __init__(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor, int], style: QtCore.Qt.BrushStyle = ...) -> None: ...
    @typing.overload
    def __init__(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor, int], pixmap: QPixmap) -> None: ...
    @typing.overload
    def __init__(self, pixmap: QPixmap) -> None: ...
    @typing.overload
    def __init__(self, image: 'QImage') -> None: ...
    @typing.overload
    def __init__(self, brush: typing.Union['QBrush', typing.Union[QColor, QtCore.Qt.GlobalColor, int], 'QGradient']) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def swap(self, other: 'QBrush') -> None: ...
    def transform(self) -> 'QTransform': ...
    def setTransform(self, a0: 'QTransform') -> None: ...
    def textureImage(self) -> 'QImage': ...
    def setTextureImage(self, image: 'QImage') -> None: ...
    def color(self) -> QColor: ...
    def style(self) -> QtCore.Qt.BrushStyle: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isOpaque(self) -> bool: ...
    def gradient(self) -> typing.Optional['QGradient']: ...
    @typing.overload
    def setColor(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def setColor(self, acolor: QtCore.Qt.GlobalColor) -> None: ...
    def setTexture(self, pixmap: QPixmap) -> None: ...
    def texture(self) -> QPixmap: ...
    def setStyle(self, a0: QtCore.Qt.BrushStyle) -> None: ...


class QGradient(PyQt6.sip.simplewrapper):

    class Preset(enum.Enum):
        WarmFlame = ... # type: QGradient.Preset
        NightFade = ... # type: QGradient.Preset
        SpringWarmth = ... # type: QGradient.Preset
        JuicyPeach = ... # type: QGradient.Preset
        YoungPassion = ... # type: QGradient.Preset
        LadyLips = ... # type: QGradient.Preset
        SunnyMorning = ... # type: QGradient.Preset
        RainyAshville = ... # type: QGradient.Preset
        FrozenDreams = ... # type: QGradient.Preset
        WinterNeva = ... # type: QGradient.Preset
        DustyGrass = ... # type: QGradient.Preset
        TemptingAzure = ... # type: QGradient.Preset
        HeavyRain = ... # type: QGradient.Preset
        AmyCrisp = ... # type: QGradient.Preset
        MeanFruit = ... # type: QGradient.Preset
        DeepBlue = ... # type: QGradient.Preset
        RipeMalinka = ... # type: QGradient.Preset
        CloudyKnoxville = ... # type: QGradient.Preset
        MalibuBeach = ... # type: QGradient.Preset
        NewLife = ... # type: QGradient.Preset
        TrueSunset = ... # type: QGradient.Preset
        MorpheusDen = ... # type: QGradient.Preset
        RareWind = ... # type: QGradient.Preset
        NearMoon = ... # type: QGradient.Preset
        WildApple = ... # type: QGradient.Preset
        SaintPetersburg = ... # type: QGradient.Preset
        PlumPlate = ... # type: QGradient.Preset
        EverlastingSky = ... # type: QGradient.Preset
        HappyFisher = ... # type: QGradient.Preset
        Blessing = ... # type: QGradient.Preset
        SharpeyeEagle = ... # type: QGradient.Preset
        LadogaBottom = ... # type: QGradient.Preset
        LemonGate = ... # type: QGradient.Preset
        ItmeoBranding = ... # type: QGradient.Preset
        ZeusMiracle = ... # type: QGradient.Preset
        OldHat = ... # type: QGradient.Preset
        StarWine = ... # type: QGradient.Preset
        HappyAcid = ... # type: QGradient.Preset
        AwesomePine = ... # type: QGradient.Preset
        NewYork = ... # type: QGradient.Preset
        ShyRainbow = ... # type: QGradient.Preset
        MixedHopes = ... # type: QGradient.Preset
        FlyHigh = ... # type: QGradient.Preset
        StrongBliss = ... # type: QGradient.Preset
        FreshMilk = ... # type: QGradient.Preset
        SnowAgain = ... # type: QGradient.Preset
        FebruaryInk = ... # type: QGradient.Preset
        KindSteel = ... # type: QGradient.Preset
        SoftGrass = ... # type: QGradient.Preset
        GrownEarly = ... # type: QGradient.Preset
        SharpBlues = ... # type: QGradient.Preset
        ShadyWater = ... # type: QGradient.Preset
        DirtyBeauty = ... # type: QGradient.Preset
        GreatWhale = ... # type: QGradient.Preset
        TeenNotebook = ... # type: QGradient.Preset
        PoliteRumors = ... # type: QGradient.Preset
        SweetPeriod = ... # type: QGradient.Preset
        WideMatrix = ... # type: QGradient.Preset
        SoftCherish = ... # type: QGradient.Preset
        RedSalvation = ... # type: QGradient.Preset
        BurningSpring = ... # type: QGradient.Preset
        NightParty = ... # type: QGradient.Preset
        SkyGlider = ... # type: QGradient.Preset
        HeavenPeach = ... # type: QGradient.Preset
        PurpleDivision = ... # type: QGradient.Preset
        AquaSplash = ... # type: QGradient.Preset
        SpikyNaga = ... # type: QGradient.Preset
        LoveKiss = ... # type: QGradient.Preset
        CleanMirror = ... # type: QGradient.Preset
        PremiumDark = ... # type: QGradient.Preset
        ColdEvening = ... # type: QGradient.Preset
        CochitiLake = ... # type: QGradient.Preset
        SummerGames = ... # type: QGradient.Preset
        PassionateBed = ... # type: QGradient.Preset
        MountainRock = ... # type: QGradient.Preset
        DesertHump = ... # type: QGradient.Preset
        JungleDay = ... # type: QGradient.Preset
        PhoenixStart = ... # type: QGradient.Preset
        OctoberSilence = ... # type: QGradient.Preset
        FarawayRiver = ... # type: QGradient.Preset
        AlchemistLab = ... # type: QGradient.Preset
        OverSun = ... # type: QGradient.Preset
        PremiumWhite = ... # type: QGradient.Preset
        MarsParty = ... # type: QGradient.Preset
        EternalConstance = ... # type: QGradient.Preset
        JapanBlush = ... # type: QGradient.Preset
        SmilingRain = ... # type: QGradient.Preset
        CloudyApple = ... # type: QGradient.Preset
        BigMango = ... # type: QGradient.Preset
        HealthyWater = ... # type: QGradient.Preset
        AmourAmour = ... # type: QGradient.Preset
        RiskyConcrete = ... # type: QGradient.Preset
        StrongStick = ... # type: QGradient.Preset
        ViciousStance = ... # type: QGradient.Preset
        PaloAlto = ... # type: QGradient.Preset
        HappyMemories = ... # type: QGradient.Preset
        MidnightBloom = ... # type: QGradient.Preset
        Crystalline = ... # type: QGradient.Preset
        PartyBliss = ... # type: QGradient.Preset
        ConfidentCloud = ... # type: QGradient.Preset
        LeCocktail = ... # type: QGradient.Preset
        RiverCity = ... # type: QGradient.Preset
        FrozenBerry = ... # type: QGradient.Preset
        ChildCare = ... # type: QGradient.Preset
        FlyingLemon = ... # type: QGradient.Preset
        NewRetrowave = ... # type: QGradient.Preset
        HiddenJaguar = ... # type: QGradient.Preset
        AboveTheSky = ... # type: QGradient.Preset
        Nega = ... # type: QGradient.Preset
        DenseWater = ... # type: QGradient.Preset
        Seashore = ... # type: QGradient.Preset
        MarbleWall = ... # type: QGradient.Preset
        CheerfulCaramel = ... # type: QGradient.Preset
        NightSky = ... # type: QGradient.Preset
        MagicLake = ... # type: QGradient.Preset
        YoungGrass = ... # type: QGradient.Preset
        ColorfulPeach = ... # type: QGradient.Preset
        GentleCare = ... # type: QGradient.Preset
        PlumBath = ... # type: QGradient.Preset
        HappyUnicorn = ... # type: QGradient.Preset
        AfricanField = ... # type: QGradient.Preset
        SolidStone = ... # type: QGradient.Preset
        OrangeJuice = ... # type: QGradient.Preset
        GlassWater = ... # type: QGradient.Preset
        NorthMiracle = ... # type: QGradient.Preset
        FruitBlend = ... # type: QGradient.Preset
        MillenniumPine = ... # type: QGradient.Preset
        HighFlight = ... # type: QGradient.Preset
        MoleHall = ... # type: QGradient.Preset
        SpaceShift = ... # type: QGradient.Preset
        ForestInei = ... # type: QGradient.Preset
        RoyalGarden = ... # type: QGradient.Preset
        RichMetal = ... # type: QGradient.Preset
        JuicyCake = ... # type: QGradient.Preset
        SmartIndigo = ... # type: QGradient.Preset
        SandStrike = ... # type: QGradient.Preset
        NorseBeauty = ... # type: QGradient.Preset
        AquaGuidance = ... # type: QGradient.Preset
        SunVeggie = ... # type: QGradient.Preset
        SeaLord = ... # type: QGradient.Preset
        BlackSea = ... # type: QGradient.Preset
        GrassShampoo = ... # type: QGradient.Preset
        LandingAircraft = ... # type: QGradient.Preset
        WitchDance = ... # type: QGradient.Preset
        SleeplessNight = ... # type: QGradient.Preset
        AngelCare = ... # type: QGradient.Preset
        CrystalRiver = ... # type: QGradient.Preset
        SoftLipstick = ... # type: QGradient.Preset
        SaltMountain = ... # type: QGradient.Preset
        PerfectWhite = ... # type: QGradient.Preset
        FreshOasis = ... # type: QGradient.Preset
        StrictNovember = ... # type: QGradient.Preset
        MorningSalad = ... # type: QGradient.Preset
        DeepRelief = ... # type: QGradient.Preset
        SeaStrike = ... # type: QGradient.Preset
        NightCall = ... # type: QGradient.Preset
        SupremeSky = ... # type: QGradient.Preset
        LightBlue = ... # type: QGradient.Preset
        MindCrawl = ... # type: QGradient.Preset
        LilyMeadow = ... # type: QGradient.Preset
        SugarLollipop = ... # type: QGradient.Preset
        SweetDessert = ... # type: QGradient.Preset
        MagicRay = ... # type: QGradient.Preset
        TeenParty = ... # type: QGradient.Preset
        FrozenHeat = ... # type: QGradient.Preset
        GagarinView = ... # type: QGradient.Preset
        FabledSunset = ... # type: QGradient.Preset
        PerfectBlue = ... # type: QGradient.Preset
        NumPresets = ... # type: QGradient.Preset

    class Spread(enum.Enum):
        PadSpread = ... # type: QGradient.Spread
        ReflectSpread = ... # type: QGradient.Spread
        RepeatSpread = ... # type: QGradient.Spread

    class Type(enum.Enum):
        LinearGradient = ... # type: QGradient.Type
        RadialGradient = ... # type: QGradient.Type
        ConicalGradient = ... # type: QGradient.Type
        NoGradient = ... # type: QGradient.Type

    class CoordinateMode(enum.Enum):
        LogicalMode = ... # type: QGradient.CoordinateMode
        StretchToDeviceMode = ... # type: QGradient.CoordinateMode
        ObjectBoundingMode = ... # type: QGradient.CoordinateMode
        ObjectMode = ... # type: QGradient.CoordinateMode

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QGradient.Preset') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QGradient') -> None: ...

    def setCoordinateMode(self, mode: 'QGradient.CoordinateMode') -> None: ...
    def coordinateMode(self) -> 'QGradient.CoordinateMode': ...
    def setSpread(self, aspread: 'QGradient.Spread') -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def stops(self) -> list[tuple[float, QColor]]: ...
    def setStops(self, stops: collections.abc.Iterable[tuple[float, typing.Union[QColor, QtCore.Qt.GlobalColor, int]]]) -> None: ...
    def setColorAt(self, pos: float, color: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def spread(self) -> 'QGradient.Spread': ...
    def type(self) -> 'QGradient.Type': ...


class QLinearGradient(QGradient):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, start: QtCore.QPointF, finalStop: QtCore.QPointF) -> None: ...
    @typing.overload
    def __init__(self, xStart: float, yStart: float, xFinalStop: float, yFinalStop: float) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QLinearGradient') -> None: ...

    @typing.overload
    def setFinalStop(self, stop: QtCore.QPointF) -> None: ...
    @typing.overload
    def setFinalStop(self, x: float, y: float) -> None: ...
    @typing.overload
    def setStart(self, start: QtCore.QPointF) -> None: ...
    @typing.overload
    def setStart(self, x: float, y: float) -> None: ...
    def finalStop(self) -> QtCore.QPointF: ...
    def start(self) -> QtCore.QPointF: ...


class QRadialGradient(QGradient):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, center: QtCore.QPointF, radius: float, focalPoint: QtCore.QPointF) -> None: ...
    @typing.overload
    def __init__(self, center: QtCore.QPointF, centerRadius: float, focalPoint: QtCore.QPointF, focalRadius: float) -> None: ...
    @typing.overload
    def __init__(self, center: QtCore.QPointF, radius: float) -> None: ...
    @typing.overload
    def __init__(self, cx: float, cy: float, radius: float, fx: float, fy: float) -> None: ...
    @typing.overload
    def __init__(self, cx: float, cy: float, centerRadius: float, fx: float, fy: float, focalRadius: float) -> None: ...
    @typing.overload
    def __init__(self, cx: float, cy: float, radius: float) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QRadialGradient') -> None: ...

    def setFocalRadius(self, radius: float) -> None: ...
    def focalRadius(self) -> float: ...
    def setCenterRadius(self, radius: float) -> None: ...
    def centerRadius(self) -> float: ...
    def setRadius(self, radius: float) -> None: ...
    @typing.overload
    def setFocalPoint(self, focalPoint: QtCore.QPointF) -> None: ...
    @typing.overload
    def setFocalPoint(self, x: float, y: float) -> None: ...
    @typing.overload
    def setCenter(self, center: QtCore.QPointF) -> None: ...
    @typing.overload
    def setCenter(self, x: float, y: float) -> None: ...
    def radius(self) -> float: ...
    def focalPoint(self) -> QtCore.QPointF: ...
    def center(self) -> QtCore.QPointF: ...


class QConicalGradient(QGradient):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, center: QtCore.QPointF, startAngle: float) -> None: ...
    @typing.overload
    def __init__(self, cx: float, cy: float, startAngle: float) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QConicalGradient') -> None: ...

    def setAngle(self, angle: float) -> None: ...
    @typing.overload
    def setCenter(self, center: QtCore.QPointF) -> None: ...
    @typing.overload
    def setCenter(self, x: float, y: float) -> None: ...
    def angle(self) -> float: ...
    def center(self) -> QtCore.QPointF: ...


class QClipboard(QtCore.QObject):

    class Mode(enum.Enum):
        Clipboard = ... # type: QClipboard.Mode
        Selection = ... # type: QClipboard.Mode
        FindBuffer = ... # type: QClipboard.Mode

    selectionChanged: typing.ClassVar[QtCore.pyqtSignal]
    findBufferChanged: typing.ClassVar[QtCore.pyqtSignal]
    dataChanged: typing.ClassVar[QtCore.pyqtSignal]
    changed: typing.ClassVar[QtCore.pyqtSignal]
    def setPixmap(self, a0: QPixmap, mode: 'QClipboard.Mode' = ...) -> None: ...
    def setImage(self, a0: 'QImage', mode: 'QClipboard.Mode' = ...) -> None: ...
    def pixmap(self, mode: 'QClipboard.Mode' = ...) -> QPixmap: ...
    def image(self, mode: 'QClipboard.Mode' = ...) -> 'QImage': ...
    def setMimeData(self, data: typing.Optional[QtCore.QMimeData], mode: 'QClipboard.Mode' = ...) -> None: ...
    def mimeData(self, mode: 'QClipboard.Mode' = ...) -> typing.Optional[QtCore.QMimeData]: ...
    def setText(self, a0: typing.Optional[str], mode: 'QClipboard.Mode' = ...) -> None: ...
    @typing.overload
    def text(self, mode: 'QClipboard.Mode' = ...) -> str: ...
    @typing.overload
    def text(self, subtype: typing.Optional[str], mode: 'QClipboard.Mode' = ...) -> tuple[str, str]: ...
    def ownsSelection(self) -> bool: ...
    def ownsFindBuffer(self) -> bool: ...
    def ownsClipboard(self) -> bool: ...
    def supportsSelection(self) -> bool: ...
    def supportsFindBuffer(self) -> bool: ...
    def clear(self, mode: 'QClipboard.Mode' = ...) -> None: ...


class QColorSpace(PyQt6.sip.simplewrapper):

    class TransformModel(enum.Enum):
        ThreeComponentMatrix = ... # type: QColorSpace.TransformModel
        ElementListProcessing = ... # type: QColorSpace.TransformModel

    class TransferFunction(enum.Enum):
        Custom = ... # type: QColorSpace.TransferFunction
        Linear = ... # type: QColorSpace.TransferFunction
        Gamma = ... # type: QColorSpace.TransferFunction
        SRgb = ... # type: QColorSpace.TransferFunction
        ProPhotoRgb = ... # type: QColorSpace.TransferFunction
        Bt2020 = ... # type: QColorSpace.TransferFunction
        St2084 = ... # type: QColorSpace.TransferFunction
        Hlg = ... # type: QColorSpace.TransferFunction

    class Primaries(enum.Enum):
        Custom = ... # type: QColorSpace.Primaries
        SRgb = ... # type: QColorSpace.Primaries
        AdobeRgb = ... # type: QColorSpace.Primaries
        DciP3D65 = ... # type: QColorSpace.Primaries
        ProPhotoRgb = ... # type: QColorSpace.Primaries
        Bt2020 = ... # type: QColorSpace.Primaries

    class NamedColorSpace(enum.Enum):
        SRgb = ... # type: QColorSpace.NamedColorSpace
        SRgbLinear = ... # type: QColorSpace.NamedColorSpace
        AdobeRgb = ... # type: QColorSpace.NamedColorSpace
        DisplayP3 = ... # type: QColorSpace.NamedColorSpace
        ProPhotoRgb = ... # type: QColorSpace.NamedColorSpace
        Bt2020 = ... # type: QColorSpace.NamedColorSpace
        Bt2100Pq = ... # type: QColorSpace.NamedColorSpace
        Bt2100Hlg = ... # type: QColorSpace.NamedColorSpace

    class ColorModel(enum.Enum):
        Undefined = ... # type: QColorSpace.ColorModel
        Rgb = ... # type: QColorSpace.ColorModel
        Gray = ... # type: QColorSpace.ColorModel
        Cmyk = ... # type: QColorSpace.ColorModel

    class PrimaryPoints(PyQt6.sip.simplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QColorSpace.PrimaryPoints') -> None: ...

        def isValid(self) -> bool: ...
        @staticmethod
        def fromPrimaries(primaries: 'QColorSpace.Primaries') -> 'QColorSpace.PrimaryPoints': ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, namedColorSpace: 'QColorSpace.NamedColorSpace') -> None: ...
    @typing.overload
    def __init__(self, primaries: 'QColorSpace.Primaries', fun: 'QColorSpace.TransferFunction', gamma: float = ...) -> None: ...
    @typing.overload
    def __init__(self, primaries: 'QColorSpace.Primaries', gamma: float) -> None: ...
    @typing.overload
    def __init__(self, primaries: 'QColorSpace.Primaries', transferFunctionTable: collections.abc.Iterable[int]) -> None: ...
    @typing.overload
    def __init__(self, whitePoint: QtCore.QPointF, redPoint: QtCore.QPointF, greenPoint: QtCore.QPointF, bluePoint: QtCore.QPointF, fun: 'QColorSpace.TransferFunction', gamma: float = ...) -> None: ...
    @typing.overload
    def __init__(self, whitePoint: QtCore.QPointF, redPoint: QtCore.QPointF, greenPoint: QtCore.QPointF, bluePoint: QtCore.QPointF, redTransferFunctionTable: collections.abc.Iterable[int], greenTransferFunctionTable: collections.abc.Iterable[int], blueTransferFunctionTable: collections.abc.Iterable[int]) -> None: ...
    @typing.overload
    def __init__(self, whitePoint: QtCore.QPointF, redPoint: QtCore.QPointF, greenPoint: QtCore.QPointF, bluePoint: QtCore.QPointF, transferFunctionTable: collections.abc.Iterable[int]) -> None: ...
    @typing.overload
    def __init__(self, whitePoint: QtCore.QPointF, transferFunctionTable: collections.abc.Iterable[int]) -> None: ...
    @typing.overload
    def __init__(self, whitePoint: QtCore.QPointF, transferFunction: 'QColorSpace.TransferFunction', gamma: float = ...) -> None: ...
    @typing.overload
    def __init__(self, colorSpace: 'QColorSpace') -> None: ...
    @typing.overload
    def __init__(self, primaryPoints: 'QColorSpace.PrimaryPoints', transferFunction: 'QColorSpace.TransferFunction', gamma: float = ...) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def primaryPoints(self) -> 'QColorSpace.PrimaryPoints': ...
    def setPrimaryPoints(self, primaryPoints: 'QColorSpace.PrimaryPoints') -> None: ...
    def isValidTarget(self) -> bool: ...
    def colorModel(self) -> 'QColorSpace.ColorModel': ...
    def transformModel(self) -> 'QColorSpace.TransformModel': ...
    def whitePoint(self) -> QtCore.QPointF: ...
    def setWhitePoint(self, whitePoint: QtCore.QPointF) -> None: ...
    def setDescription(self, description: typing.Optional[str]) -> None: ...
    def description(self) -> str: ...
    def transformationToColorSpace(self, colorspace: 'QColorSpace') -> 'QColorTransform': ...
    def iccProfile(self) -> QtCore.QByteArray: ...
    @staticmethod
    def fromIccProfile(iccProfile: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> 'QColorSpace': ...
    def isValid(self) -> bool: ...
    @typing.overload
    def setPrimaries(self, primariesId: 'QColorSpace.Primaries') -> None: ...
    @typing.overload
    def setPrimaries(self, whitePoint: QtCore.QPointF, redPoint: QtCore.QPointF, greenPoint: QtCore.QPointF, bluePoint: QtCore.QPointF) -> None: ...
    def withTransferFunctions(self, redTransferFunctionTable: collections.abc.Iterable[int], greenTransferFunctionTable: collections.abc.Iterable[int], blueTransferFunctionTable: collections.abc.Iterable[int]) -> 'QColorSpace': ...
    @typing.overload
    def withTransferFunction(self, transferFunctionTable: collections.abc.Iterable[int]) -> 'QColorSpace': ...
    @typing.overload
    def withTransferFunction(self, transferFunction: 'QColorSpace.TransferFunction', gamma: float = ...) -> 'QColorSpace': ...
    def setTransferFunctions(self, redTransferFunctionTable: collections.abc.Iterable[int], greenTransferFunctionTable: collections.abc.Iterable[int], blueTransferFunctionTable: collections.abc.Iterable[int]) -> None: ...
    @typing.overload
    def setTransferFunction(self, transferFunction: 'QColorSpace.TransferFunction', gamma: float = ...) -> None: ...
    @typing.overload
    def setTransferFunction(self, transferFunctionTable: collections.abc.Iterable[int]) -> None: ...
    def gamma(self) -> float: ...
    def transferFunction(self) -> 'QColorSpace.TransferFunction': ...
    def primaries(self) -> 'QColorSpace.Primaries': ...
    def swap(self, colorSpace: 'QColorSpace') -> None: ...


class QColorTransform(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, colorTransform: 'QColorTransform') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isIdentity(self) -> bool: ...
    @typing.overload
    def map(self, argb: int) -> int: ...
    @typing.overload
    def map(self, rgba64: 'QRgba64') -> 'QRgba64': ...
    @typing.overload
    def map(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> QColor: ...
    def swap(self, other: 'QColorTransform') -> None: ...


class QCursor(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, bitmap: QBitmap, mask: QBitmap, hotX: int = ..., hotY: int = ...) -> None: ...
    @typing.overload
    def __init__(self, pixmap: QPixmap, hotX: int = ..., hotY: int = ...) -> None: ...
    @typing.overload
    def __init__(self, cursor: typing.Union['QCursor', QtCore.Qt.CursorShape]) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def swap(self, other: typing.Union['QCursor', QtCore.Qt.CursorShape]) -> None: ...
    @typing.overload
    @staticmethod
    def setPos(x: int, y: int) -> None: ...
    @typing.overload
    @staticmethod
    def setPos(p: QtCore.QPoint) -> None: ...
    @typing.overload
    @staticmethod
    def setPos(screen: typing.Optional['QScreen'], x: int, y: int) -> None: ...
    @typing.overload
    @staticmethod
    def setPos(screen: typing.Optional['QScreen'], p: QtCore.QPoint) -> None: ...
    @typing.overload
    @staticmethod
    def pos() -> QtCore.QPoint: ...
    @typing.overload
    @staticmethod
    def pos(screen: typing.Optional['QScreen']) -> QtCore.QPoint: ...
    def hotSpot(self) -> QtCore.QPoint: ...
    def pixmap(self) -> QPixmap: ...
    def mask(self) -> QBitmap: ...
    def bitmap(self) -> QBitmap: ...
    def setShape(self, newShape: QtCore.Qt.CursorShape) -> None: ...
    def shape(self) -> QtCore.Qt.CursorShape: ...


class QDesktopServices(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QDesktopServices') -> None: ...

    @staticmethod
    def unsetUrlHandler(scheme: typing.Optional[str]) -> None: ...
    @typing.overload
    @staticmethod
    def setUrlHandler(scheme: typing.Optional[str], receiver: typing.Optional[QtCore.QObject], method: typing.Optional[str]) -> None: ...
    @typing.overload
    @staticmethod
    def setUrlHandler(scheme: typing.Optional[str], method: collections.abc.Callable[[QtCore.QUrl], None]) -> None: ...
    @staticmethod
    def openUrl(url: QtCore.QUrl) -> bool: ...


class QDrag(QtCore.QObject):

    def __init__(self, dragSource: typing.Optional[QtCore.QObject]) -> None: ...

    @staticmethod
    def cancel() -> None: ...
    def defaultAction(self) -> QtCore.Qt.DropAction: ...
    def supportedActions(self) -> QtCore.Qt.DropAction: ...
    def dragCursor(self, action: QtCore.Qt.DropAction) -> QPixmap: ...
    targetChanged: typing.ClassVar[QtCore.pyqtSignal]
    actionChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setDragCursor(self, cursor: QPixmap, action: QtCore.Qt.DropAction) -> None: ...
    def target(self) -> typing.Optional[QtCore.QObject]: ...
    def source(self) -> typing.Optional[QtCore.QObject]: ...
    def hotSpot(self) -> QtCore.QPoint: ...
    def setHotSpot(self, hotspot: QtCore.QPoint) -> None: ...
    def pixmap(self) -> QPixmap: ...
    def setPixmap(self, a0: QPixmap) -> None: ...
    def mimeData(self) -> typing.Optional[QtCore.QMimeData]: ...
    def setMimeData(self, data: typing.Optional[QtCore.QMimeData]) -> None: ...
    @typing.overload
    def exec(self, supportedActions: QtCore.Qt.DropAction = ...) -> QtCore.Qt.DropAction: ...
    @typing.overload
    def exec(self, supportedActions: QtCore.Qt.DropAction, defaultDropAction: QtCore.Qt.DropAction) -> QtCore.Qt.DropAction: ...


class QInputEvent(QtCore.QEvent):

    def clone(self) -> typing.Optional['QInputEvent']: ...
    def deviceType(self) -> 'QInputDevice.DeviceType': ...
    def device(self) -> typing.Optional['QInputDevice']: ...
    def timestamp(self) -> int: ...
    def modifiers(self) -> QtCore.Qt.KeyboardModifier: ...


class QKeyEvent(QInputEvent):

    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, key: int, modifiers: QtCore.Qt.KeyboardModifier, nativeScanCode: int, nativeVirtualKey: int, nativeModifiers: int, text: typing.Optional[str] = ..., autorep: bool = ..., count: int = ..., device: typing.Optional['QInputDevice'] = ...) -> None: ...
    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, key: int, modifiers: QtCore.Qt.KeyboardModifier, text: typing.Optional[str] = ..., autorep: bool = ..., count: int = ...) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def clone(self) -> typing.Optional['QKeyEvent']: ...
    def keyCombination(self) -> QtCore.QKeyCombination: ...
    def nativeVirtualKey(self) -> int: ...
    def nativeScanCode(self) -> int: ...
    def nativeModifiers(self) -> int: ...
    def matches(self, key: 'QKeySequence.StandardKey') -> bool: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def isAutoRepeat(self) -> bool: ...
    def text(self) -> str: ...
    def modifiers(self) -> QtCore.Qt.KeyboardModifier: ...
    def key(self) -> int: ...


class QFocusEvent(QtCore.QEvent):

    def __init__(self, type: QtCore.QEvent.Type, reason: QtCore.Qt.FocusReason = ...) -> None: ...

    def clone(self) -> typing.Optional['QFocusEvent']: ...
    def reason(self) -> QtCore.Qt.FocusReason: ...
    def lostFocus(self) -> bool: ...
    def gotFocus(self) -> bool: ...


class QPaintEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, paintRegion: 'QRegion') -> None: ...
    @typing.overload
    def __init__(self, paintRect: QtCore.QRect) -> None: ...

    def clone(self) -> typing.Optional['QPaintEvent']: ...
    def region(self) -> 'QRegion': ...
    def rect(self) -> QtCore.QRect: ...


class QMoveEvent(QtCore.QEvent):

    def __init__(self, pos: QtCore.QPoint, oldPos: QtCore.QPoint) -> None: ...

    def clone(self) -> typing.Optional['QMoveEvent']: ...
    def oldPos(self) -> QtCore.QPoint: ...
    def pos(self) -> QtCore.QPoint: ...


class QResizeEvent(QtCore.QEvent):

    def __init__(self, size: QtCore.QSize, oldSize: QtCore.QSize) -> None: ...

    def clone(self) -> typing.Optional['QResizeEvent']: ...
    def oldSize(self) -> QtCore.QSize: ...
    def size(self) -> QtCore.QSize: ...


class QCloseEvent(QtCore.QEvent):

    def __init__(self) -> None: ...

    def clone(self) -> typing.Optional['QCloseEvent']: ...


class QIconDragEvent(QtCore.QEvent):

    def __init__(self) -> None: ...

    def clone(self) -> typing.Optional['QIconDragEvent']: ...


class QShowEvent(QtCore.QEvent):

    def __init__(self) -> None: ...

    def clone(self) -> typing.Optional['QShowEvent']: ...


class QHideEvent(QtCore.QEvent):

    def __init__(self) -> None: ...

    def clone(self) -> typing.Optional['QHideEvent']: ...


class QContextMenuEvent(QInputEvent):

    class Reason(enum.Enum):
        Mouse = ... # type: QContextMenuEvent.Reason
        Keyboard = ... # type: QContextMenuEvent.Reason
        Other = ... # type: QContextMenuEvent.Reason

    @typing.overload
    def __init__(self, reason: 'QContextMenuEvent.Reason', pos: QtCore.QPoint, globalPos: QtCore.QPoint, modifiers: QtCore.Qt.KeyboardModifier = ...) -> None: ...
    @typing.overload
    def __init__(self, reason: 'QContextMenuEvent.Reason', pos: QtCore.QPoint) -> None: ...

    def clone(self) -> typing.Optional['QContextMenuEvent']: ...
    def reason(self) -> 'QContextMenuEvent.Reason': ...
    def globalPos(self) -> QtCore.QPoint: ...
    def pos(self) -> QtCore.QPoint: ...
    def globalY(self) -> int: ...
    def globalX(self) -> int: ...
    def y(self) -> int: ...
    def x(self) -> int: ...


class QInputMethodEvent(QtCore.QEvent):

    class AttributeType(enum.Enum):
        TextFormat = ... # type: QInputMethodEvent.AttributeType
        Cursor = ... # type: QInputMethodEvent.AttributeType
        Language = ... # type: QInputMethodEvent.AttributeType
        Ruby = ... # type: QInputMethodEvent.AttributeType
        Selection = ... # type: QInputMethodEvent.AttributeType

    class Attribute(PyQt6.sip.simplewrapper):

        length = ... # type: int
        start = ... # type: int
        type = ... # type: 'QInputMethodEvent.AttributeType'
        value = ... # type: typing.Any

        @typing.overload
        def __init__(self, t: 'QInputMethodEvent.AttributeType', s: int, l: int, val: typing.Any) -> None: ...
        @typing.overload
        def __init__(self, typ: 'QInputMethodEvent.AttributeType', s: int, l: int) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QInputMethodEvent.Attribute') -> None: ...

        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, preeditText: typing.Optional[str], attributes: collections.abc.Iterable['QInputMethodEvent.Attribute']) -> None: ...

    def clone(self) -> typing.Optional['QInputMethodEvent']: ...
    def replacementLength(self) -> int: ...
    def replacementStart(self) -> int: ...
    def commitString(self) -> str: ...
    def preeditString(self) -> str: ...
    def attributes(self) -> list['QInputMethodEvent.Attribute']: ...
    def setCommitString(self, commitString: typing.Optional[str], from_: int = ..., length: int = ...) -> None: ...


class QInputMethodQueryEvent(QtCore.QEvent):

    def __init__(self, queries: QtCore.Qt.InputMethodQuery) -> None: ...

    def clone(self) -> typing.Optional['QInputMethodQueryEvent']: ...
    def value(self, query: QtCore.Qt.InputMethodQuery) -> typing.Any: ...
    def setValue(self, query: QtCore.Qt.InputMethodQuery, value: typing.Any) -> None: ...
    def queries(self) -> QtCore.Qt.InputMethodQuery: ...


class QDropEvent(QtCore.QEvent):

    def __init__(self, pos: QtCore.QPointF, actions: QtCore.Qt.DropAction, data: typing.Optional[QtCore.QMimeData], buttons: QtCore.Qt.MouseButton, modifiers: QtCore.Qt.KeyboardModifier, type: QtCore.QEvent.Type = ...) -> None: ...

    def clone(self) -> typing.Optional['QDropEvent']: ...
    def modifiers(self) -> QtCore.Qt.KeyboardModifier: ...
    def buttons(self) -> QtCore.Qt.MouseButton: ...
    def position(self) -> QtCore.QPointF: ...
    def mimeData(self) -> typing.Optional[QtCore.QMimeData]: ...
    def source(self) -> typing.Optional[QtCore.QObject]: ...
    def setDropAction(self, action: QtCore.Qt.DropAction) -> None: ...
    def dropAction(self) -> QtCore.Qt.DropAction: ...
    def acceptProposedAction(self) -> None: ...
    def proposedAction(self) -> QtCore.Qt.DropAction: ...
    def possibleActions(self) -> QtCore.Qt.DropAction: ...


class QDragMoveEvent(QDropEvent):

    def __init__(self, pos: QtCore.QPoint, actions: QtCore.Qt.DropAction, data: typing.Optional[QtCore.QMimeData], buttons: QtCore.Qt.MouseButton, modifiers: QtCore.Qt.KeyboardModifier, type: QtCore.QEvent.Type = ...) -> None: ...

    def clone(self) -> typing.Optional['QDragMoveEvent']: ...
    @typing.overload
    def ignore(self) -> None: ...
    @typing.overload
    def ignore(self, r: QtCore.QRect) -> None: ...
    @typing.overload
    def accept(self) -> None: ...
    @typing.overload
    def accept(self, r: QtCore.QRect) -> None: ...
    def answerRect(self) -> QtCore.QRect: ...


class QDragEnterEvent(QDragMoveEvent):

    def __init__(self, pos: QtCore.QPoint, actions: QtCore.Qt.DropAction, data: typing.Optional[QtCore.QMimeData], buttons: QtCore.Qt.MouseButton, modifiers: QtCore.Qt.KeyboardModifier) -> None: ...

    def clone(self) -> typing.Optional['QDragEnterEvent']: ...


class QDragLeaveEvent(QtCore.QEvent):

    def __init__(self) -> None: ...

    def clone(self) -> typing.Optional['QDragLeaveEvent']: ...


class QHelpEvent(QtCore.QEvent):

    def __init__(self, type: QtCore.QEvent.Type, pos: QtCore.QPoint, globalPos: QtCore.QPoint) -> None: ...

    def clone(self) -> typing.Optional['QHelpEvent']: ...
    def globalPos(self) -> QtCore.QPoint: ...
    def pos(self) -> QtCore.QPoint: ...
    def globalY(self) -> int: ...
    def globalX(self) -> int: ...
    def y(self) -> int: ...
    def x(self) -> int: ...


class QStatusTipEvent(QtCore.QEvent):

    def __init__(self, tip: typing.Optional[str]) -> None: ...

    def clone(self) -> typing.Optional['QStatusTipEvent']: ...
    def tip(self) -> str: ...


class QWhatsThisClickedEvent(QtCore.QEvent):

    def __init__(self, href: typing.Optional[str]) -> None: ...

    def clone(self) -> typing.Optional['QWhatsThisClickedEvent']: ...
    def href(self) -> str: ...


class QActionEvent(QtCore.QEvent):

    def __init__(self, type: int, action: typing.Optional[QAction], before: typing.Optional[QAction] = ...) -> None: ...

    def clone(self) -> typing.Optional['QActionEvent']: ...
    def before(self) -> typing.Optional[QAction]: ...
    def action(self) -> typing.Optional[QAction]: ...


class QFileOpenEvent(QtCore.QEvent):

    def clone(self) -> typing.Optional['QFileOpenEvent']: ...
    def openFile(self, file: QtCore.QFile, flags: QtCore.QIODeviceBase.OpenModeFlag) -> bool: ...
    def url(self) -> QtCore.QUrl: ...
    def file(self) -> str: ...


class QShortcutEvent(QtCore.QEvent):

    @typing.overload
    def __init__(self, key: typing.Union['QKeySequence', 'QKeySequence.StandardKey', typing.Optional[str], int], id: int, ambiguous: bool = ...) -> None: ...
    @typing.overload
    def __init__(self, key: typing.Union['QKeySequence', 'QKeySequence.StandardKey', typing.Optional[str], int], shortcut: typing.Optional['QShortcut'] = ..., ambiguous: bool = ...) -> None: ...

    def clone(self) -> typing.Optional['QShortcutEvent']: ...
    def shortcutId(self) -> int: ...
    def key(self) -> 'QKeySequence': ...
    def isAmbiguous(self) -> bool: ...


class QWindowStateChangeEvent(QtCore.QEvent):

    def clone(self) -> typing.Optional['QWindowStateChangeEvent']: ...
    def oldState(self) -> QtCore.Qt.WindowState: ...


class QExposeEvent(QtCore.QEvent):

    def __init__(self, rgn: 'QRegion') -> None: ...

    def clone(self) -> typing.Optional['QExposeEvent']: ...


class QScrollPrepareEvent(QtCore.QEvent):

    def __init__(self, startPos: QtCore.QPointF) -> None: ...

    def clone(self) -> typing.Optional['QScrollPrepareEvent']: ...
    def setContentPos(self, pos: QtCore.QPointF) -> None: ...
    def setContentPosRange(self, rect: QtCore.QRectF) -> None: ...
    def setViewportSize(self, size: QtCore.QSizeF) -> None: ...
    def contentPos(self) -> QtCore.QPointF: ...
    def contentPosRange(self) -> QtCore.QRectF: ...
    def viewportSize(self) -> QtCore.QSizeF: ...
    def startPos(self) -> QtCore.QPointF: ...


class QScrollEvent(QtCore.QEvent):

    class ScrollState(enum.Enum):
        ScrollStarted = ... # type: QScrollEvent.ScrollState
        ScrollUpdated = ... # type: QScrollEvent.ScrollState
        ScrollFinished = ... # type: QScrollEvent.ScrollState

    def __init__(self, contentPos: QtCore.QPointF, overshoot: QtCore.QPointF, scrollState: 'QScrollEvent.ScrollState') -> None: ...

    def clone(self) -> typing.Optional['QScrollEvent']: ...
    def scrollState(self) -> 'QScrollEvent.ScrollState': ...
    def overshootDistance(self) -> QtCore.QPointF: ...
    def contentPos(self) -> QtCore.QPointF: ...


class QPlatformSurfaceEvent(QtCore.QEvent):

    class SurfaceEventType(enum.Enum):
        SurfaceCreated = ... # type: QPlatformSurfaceEvent.SurfaceEventType
        SurfaceAboutToBeDestroyed = ... # type: QPlatformSurfaceEvent.SurfaceEventType

    def __init__(self, surfaceEventType: 'QPlatformSurfaceEvent.SurfaceEventType') -> None: ...

    def clone(self) -> typing.Optional['QPlatformSurfaceEvent']: ...
    def surfaceEventType(self) -> 'QPlatformSurfaceEvent.SurfaceEventType': ...


class QPointerEvent(QInputEvent):

    def clone(self) -> typing.Optional['QPointerEvent']: ...
    def setAccepted(self, accepted: bool) -> None: ...
    def allPointsAccepted(self) -> bool: ...
    def isEndEvent(self) -> bool: ...
    def isUpdateEvent(self) -> bool: ...
    def isBeginEvent(self) -> bool: ...
    def pointById(self, id: int) -> typing.Optional['QEventPoint']: ...
    def points(self) -> list['QEventPoint']: ...
    def point(self, i: int) -> 'QEventPoint': ...
    def pointCount(self) -> int: ...
    def pointerType(self) -> 'QPointingDevice.PointerType': ...
    def pointingDevice(self) -> typing.Optional['QPointingDevice']: ...


class QSinglePointEvent(QPointerEvent):

    def clone(self) -> typing.Optional['QSinglePointEvent']: ...
    def setExclusivePointGrabber(self, exclusiveGrabber: typing.Optional[QtCore.QObject]) -> None: ...
    def exclusivePointGrabber(self) -> typing.Optional[QtCore.QObject]: ...
    def isEndEvent(self) -> bool: ...
    def isUpdateEvent(self) -> bool: ...
    def isBeginEvent(self) -> bool: ...
    def globalPosition(self) -> QtCore.QPointF: ...
    def scenePosition(self) -> QtCore.QPointF: ...
    def position(self) -> QtCore.QPointF: ...
    def buttons(self) -> QtCore.Qt.MouseButton: ...
    def button(self) -> QtCore.Qt.MouseButton: ...


class QEnterEvent(QSinglePointEvent):

    def __init__(self, localPos: QtCore.QPointF, scenePos: QtCore.QPointF, globalPos: QtCore.QPointF, device: typing.Optional['QPointingDevice'] = ...) -> None: ...

    def clone(self) -> typing.Optional['QEnterEvent']: ...


class QMouseEvent(QSinglePointEvent):

    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, localPos: QtCore.QPointF, button: QtCore.Qt.MouseButton, buttons: QtCore.Qt.MouseButton, modifiers: QtCore.Qt.KeyboardModifier, device: typing.Optional['QPointingDevice'] = ...) -> None: ...
    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, localPos: QtCore.QPointF, globalPos: QtCore.QPointF, button: QtCore.Qt.MouseButton, buttons: QtCore.Qt.MouseButton, modifiers: QtCore.Qt.KeyboardModifier, device: typing.Optional['QPointingDevice'] = ...) -> None: ...
    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, localPos: QtCore.QPointF, scenePos: QtCore.QPointF, globalPos: QtCore.QPointF, button: QtCore.Qt.MouseButton, buttons: QtCore.Qt.MouseButton, modifiers: QtCore.Qt.KeyboardModifier, device: typing.Optional['QPointingDevice'] = ...) -> None: ...

    def clone(self) -> typing.Optional['QMouseEvent']: ...
    def flags(self) -> QtCore.Qt.MouseEventFlag: ...
    def pos(self) -> QtCore.QPoint: ...


class QHoverEvent(QSinglePointEvent):

    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, pos: QtCore.QPointF, globalPos: QtCore.QPointF, oldPos: QtCore.QPointF, modifiers: QtCore.Qt.KeyboardModifier = ..., device: typing.Optional['QPointingDevice'] = ...) -> None: ...
    @typing.overload
    def __init__(self, type: QtCore.QEvent.Type, pos: QtCore.QPointF, oldPos: QtCore.QPointF, modifiers: QtCore.Qt.KeyboardModifier = ..., device: typing.Optional['QPointingDevice'] = ...) -> None: ...

    def clone(self) -> typing.Optional['QHoverEvent']: ...
    def oldPosF(self) -> QtCore.QPointF: ...
    def oldPos(self) -> QtCore.QPoint: ...
    def isUpdateEvent(self) -> bool: ...


class QWheelEvent(QSinglePointEvent):

    def __init__(self, pos: QtCore.QPointF, globalPos: QtCore.QPointF, pixelDelta: QtCore.QPoint, angleDelta: QtCore.QPoint, buttons: QtCore.Qt.MouseButton, modifiers: QtCore.Qt.KeyboardModifier, phase: QtCore.Qt.ScrollPhase, inverted: bool, source: QtCore.Qt.MouseEventSource = ..., device: typing.Optional['QPointingDevice'] = ...) -> None: ...

    def clone(self) -> typing.Optional['QWheelEvent']: ...
    def isEndEvent(self) -> bool: ...
    def isUpdateEvent(self) -> bool: ...
    def isBeginEvent(self) -> bool: ...
    def inverted(self) -> bool: ...
    def phase(self) -> QtCore.Qt.ScrollPhase: ...
    def angleDelta(self) -> QtCore.QPoint: ...
    def pixelDelta(self) -> QtCore.QPoint: ...


class QTabletEvent(QSinglePointEvent):

    def __init__(self, t: QtCore.QEvent.Type, device: typing.Optional['QPointingDevice'], pos: QtCore.QPointF, globalPos: QtCore.QPointF, pressure: float, xTilt: float, yTilt: float, tangentialPressure: float, rotation: float, z: float, keyState: QtCore.Qt.KeyboardModifier, button: QtCore.Qt.MouseButton, buttons: QtCore.Qt.MouseButton) -> None: ...

    def clone(self) -> typing.Optional['QTabletEvent']: ...
    def yTilt(self) -> float: ...
    def xTilt(self) -> float: ...
    def tangentialPressure(self) -> float: ...
    def z(self) -> float: ...
    def rotation(self) -> float: ...
    def pressure(self) -> float: ...


class QNativeGestureEvent(QSinglePointEvent):

    @typing.overload
    def __init__(self, type: QtCore.Qt.NativeGestureType, dev: typing.Optional['QPointingDevice'], fingerCount: int, localPos: QtCore.QPointF, scenePos: QtCore.QPointF, globalPos: QtCore.QPointF, value: float, delta: QtCore.QPointF, sequenceId: int = ...) -> None: ...
    @typing.overload
    def __init__(self, type: QtCore.Qt.NativeGestureType, dev: typing.Optional['QPointingDevice'], localPos: QtCore.QPointF, scenePos: QtCore.QPointF, globalPos: QtCore.QPointF, value: float, sequenceId: int, intArgument: int) -> None: ...

    def delta(self) -> QtCore.QPointF: ...
    def fingerCount(self) -> int: ...
    def clone(self) -> typing.Optional['QNativeGestureEvent']: ...
    def value(self) -> float: ...
    def gestureType(self) -> QtCore.Qt.NativeGestureType: ...


class QTouchEvent(QPointerEvent):

    def __init__(self, eventType: QtCore.QEvent.Type, device: typing.Optional['QPointingDevice'] = ..., modifiers: QtCore.Qt.KeyboardModifier = ..., touchPoints: collections.abc.Iterable['QEventPoint'] = ...) -> None: ...

    def clone(self) -> typing.Optional['QTouchEvent']: ...
    def isEndEvent(self) -> bool: ...
    def isUpdateEvent(self) -> bool: ...
    def isBeginEvent(self) -> bool: ...
    def touchPointStates(self) -> 'QEventPoint.State': ...
    def target(self) -> typing.Optional[QtCore.QObject]: ...


class QChildWindowEvent(QtCore.QEvent):

    def __init__(self, type: QtCore.QEvent.Type, childWindow: typing.Optional['QWindow']) -> None: ...

    def clone(self) -> typing.Optional['QChildWindowEvent']: ...
    def child(self) -> typing.Optional['QWindow']: ...


class QEventPoint(PyQt6.sip.simplewrapper):

    class State(enum.Flag):
        Unknown = ... # type: QEventPoint.State
        Stationary = ... # type: QEventPoint.State
        Pressed = ... # type: QEventPoint.State
        Updated = ... # type: QEventPoint.State
        Released = ... # type: QEventPoint.State

    @typing.overload
    def __init__(self, pointId: int, state: 'QEventPoint.State', scenePosition: QtCore.QPointF, globalPosition: QtCore.QPointF) -> None: ...
    @typing.overload
    def __init__(self, other: 'QEventPoint') -> None: ...

    def setAccepted(self, accepted: bool = ...) -> None: ...
    def isAccepted(self) -> bool: ...
    def ellipseDiameters(self) -> QtCore.QSizeF: ...
    def rotation(self) -> float: ...
    def pressure(self) -> float: ...
    def timeHeld(self) -> float: ...
    def pressTimestamp(self) -> int: ...
    def lastTimestamp(self) -> int: ...
    def timestamp(self) -> int: ...
    def uniqueId(self) -> 'QPointingDeviceUniqueId': ...
    def id(self) -> int: ...
    def device(self) -> typing.Optional['QPointingDevice']: ...
    def state(self) -> 'QEventPoint.State': ...
    def velocity(self) -> 'QVector2D': ...
    def normalizedPosition(self) -> QtCore.QPointF: ...
    def globalLastPosition(self) -> QtCore.QPointF: ...
    def globalGrabPosition(self) -> QtCore.QPointF: ...
    def globalPressPosition(self) -> QtCore.QPointF: ...
    def globalPosition(self) -> QtCore.QPointF: ...
    def sceneLastPosition(self) -> QtCore.QPointF: ...
    def sceneGrabPosition(self) -> QtCore.QPointF: ...
    def scenePressPosition(self) -> QtCore.QPointF: ...
    def scenePosition(self) -> QtCore.QPointF: ...
    def lastPosition(self) -> QtCore.QPointF: ...
    def grabPosition(self) -> QtCore.QPointF: ...
    def pressPosition(self) -> QtCore.QPointF: ...
    def position(self) -> QtCore.QPointF: ...
    def swap(self, other: 'QEventPoint') -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QFileSystemModel(QtCore.QAbstractItemModel):

    class Option(enum.Flag):
        DontWatchForChanges = ... # type: QFileSystemModel.Option
        DontResolveSymlinks = ... # type: QFileSystemModel.Option
        DontUseCustomDirectoryIcons = ... # type: QFileSystemModel.Option

    class Roles(enum.IntEnum):
        FileIconRole = ... # type: QFileSystemModel.Roles
        FilePathRole = ... # type: QFileSystemModel.Roles
        FileNameRole = ... # type: QFileSystemModel.Roles
        FilePermissions = ... # type: QFileSystemModel.Roles
        FileInfoRole = ... # type: QFileSystemModel.Roles

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def roleNames(self) -> dict[int, QtCore.QByteArray]: ...
    def options(self) -> 'QFileSystemModel.Option': ...
    def setOptions(self, options: 'QFileSystemModel.Option') -> None: ...
    def testOption(self, option: 'QFileSystemModel.Option') -> bool: ...
    def setOption(self, option: 'QFileSystemModel.Option', on: bool = ...) -> None: ...
    def sibling(self, row: int, column: int, idx: QtCore.QModelIndex) -> QtCore.QModelIndex: ...
    def timerEvent(self, event: typing.Optional[QtCore.QTimerEvent]) -> None: ...
    def event(self, event: typing.Optional[QtCore.QEvent]) -> bool: ...
    directoryLoaded: typing.ClassVar[QtCore.pyqtSignal]
    rootPathChanged: typing.ClassVar[QtCore.pyqtSignal]
    fileRenamed: typing.ClassVar[QtCore.pyqtSignal]
    def remove(self, index: QtCore.QModelIndex) -> bool: ...
    def fileInfo(self, aindex: QtCore.QModelIndex) -> QtCore.QFileInfo: ...
    def fileIcon(self, aindex: QtCore.QModelIndex) -> 'QIcon': ...
    def fileName(self, aindex: QtCore.QModelIndex) -> str: ...
    def rmdir(self, index: QtCore.QModelIndex) -> bool: ...
    def permissions(self, index: QtCore.QModelIndex) -> QtCore.QFileDevice.Permission: ...
    def mkdir(self, parent: QtCore.QModelIndex, name: typing.Optional[str]) -> QtCore.QModelIndex: ...
    @typing.overload
    def lastModified(self, index: QtCore.QModelIndex) -> QtCore.QDateTime: ...
    @typing.overload
    def lastModified(self, index: QtCore.QModelIndex, tz: QtCore.QTimeZone) -> QtCore.QDateTime: ...
    def type(self, index: QtCore.QModelIndex) -> str: ...
    def size(self, index: QtCore.QModelIndex) -> int: ...
    def isDir(self, index: QtCore.QModelIndex) -> bool: ...
    def filePath(self, index: QtCore.QModelIndex) -> str: ...
    def nameFilters(self) -> list[str]: ...
    def setNameFilters(self, filters: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    def nameFilterDisables(self) -> bool: ...
    def setNameFilterDisables(self, enable: bool) -> None: ...
    def isReadOnly(self) -> bool: ...
    def setReadOnly(self, enable: bool) -> None: ...
    def resolveSymlinks(self) -> bool: ...
    def setResolveSymlinks(self, enable: bool) -> None: ...
    def filter(self) -> QtCore.QDir.Filter: ...
    def setFilter(self, filters: QtCore.QDir.Filter) -> None: ...
    def iconProvider(self) -> typing.Optional[QAbstractFileIconProvider]: ...
    def setIconProvider(self, provider: typing.Optional[QAbstractFileIconProvider]) -> None: ...
    def rootDirectory(self) -> QtCore.QDir: ...
    def rootPath(self) -> str: ...
    def setRootPath(self, path: typing.Optional[str]) -> QtCore.QModelIndex: ...
    def supportedDropActions(self) -> QtCore.Qt.DropAction: ...
    def dropMimeData(self, data: typing.Optional[QtCore.QMimeData], action: QtCore.Qt.DropAction, row: int, column: int, parent: QtCore.QModelIndex) -> bool: ...
    def mimeData(self, indexes: collections.abc.Iterable[QtCore.QModelIndex]) -> typing.Optional[QtCore.QMimeData]: ...
    def mimeTypes(self) -> list[str]: ...
    def sort(self, column: int, order: QtCore.Qt.SortOrder = ...) -> None: ...
    def flags(self, index: QtCore.QModelIndex) -> QtCore.Qt.ItemFlag: ...
    def headerData(self, section: int, orientation: QtCore.Qt.Orientation, role: int = ...) -> typing.Any: ...
    def setData(self, index: QtCore.QModelIndex, value: typing.Any, role: int = ...) -> bool: ...
    def data(self, index: QtCore.QModelIndex, role: int = ...) -> typing.Any: ...
    def myComputer(self, role: int = ...) -> typing.Any: ...
    def columnCount(self, parent: QtCore.QModelIndex = ...) -> int: ...
    def rowCount(self, parent: QtCore.QModelIndex = ...) -> int: ...
    def fetchMore(self, parent: QtCore.QModelIndex) -> None: ...
    def canFetchMore(self, parent: QtCore.QModelIndex) -> bool: ...
    def hasChildren(self, parent: QtCore.QModelIndex = ...) -> bool: ...
    def parent(self, child: QtCore.QModelIndex) -> QtCore.QModelIndex: ...
    @typing.overload
    def index(self, row: int, column: int, parent: QtCore.QModelIndex = ...) -> QtCore.QModelIndex: ...
    @typing.overload
    def index(self, path: typing.Optional[str], column: int = ...) -> QtCore.QModelIndex: ...


class QFont(PyQt6.sip.simplewrapper):

    class HintingPreference(enum.Enum):
        PreferDefaultHinting = ... # type: QFont.HintingPreference
        PreferNoHinting = ... # type: QFont.HintingPreference
        PreferVerticalHinting = ... # type: QFont.HintingPreference
        PreferFullHinting = ... # type: QFont.HintingPreference

    class SpacingType(enum.Enum):
        PercentageSpacing = ... # type: QFont.SpacingType
        AbsoluteSpacing = ... # type: QFont.SpacingType

    class Capitalization(enum.Enum):
        MixedCase = ... # type: QFont.Capitalization
        AllUppercase = ... # type: QFont.Capitalization
        AllLowercase = ... # type: QFont.Capitalization
        SmallCaps = ... # type: QFont.Capitalization
        Capitalize = ... # type: QFont.Capitalization

    class Stretch(enum.IntEnum):
        AnyStretch = ... # type: QFont.Stretch
        UltraCondensed = ... # type: QFont.Stretch
        ExtraCondensed = ... # type: QFont.Stretch
        Condensed = ... # type: QFont.Stretch
        SemiCondensed = ... # type: QFont.Stretch
        Unstretched = ... # type: QFont.Stretch
        SemiExpanded = ... # type: QFont.Stretch
        Expanded = ... # type: QFont.Stretch
        ExtraExpanded = ... # type: QFont.Stretch
        UltraExpanded = ... # type: QFont.Stretch

    class Style(enum.Enum):
        StyleNormal = ... # type: QFont.Style
        StyleItalic = ... # type: QFont.Style
        StyleOblique = ... # type: QFont.Style

    class Weight(enum.IntEnum):
        Thin = ... # type: QFont.Weight
        ExtraLight = ... # type: QFont.Weight
        Light = ... # type: QFont.Weight
        Normal = ... # type: QFont.Weight
        Medium = ... # type: QFont.Weight
        DemiBold = ... # type: QFont.Weight
        Bold = ... # type: QFont.Weight
        ExtraBold = ... # type: QFont.Weight
        Black = ... # type: QFont.Weight

    class StyleStrategy(enum.Flag):
        PreferDefault = ... # type: QFont.StyleStrategy
        PreferBitmap = ... # type: QFont.StyleStrategy
        PreferDevice = ... # type: QFont.StyleStrategy
        PreferOutline = ... # type: QFont.StyleStrategy
        ForceOutline = ... # type: QFont.StyleStrategy
        PreferMatch = ... # type: QFont.StyleStrategy
        PreferQuality = ... # type: QFont.StyleStrategy
        PreferAntialias = ... # type: QFont.StyleStrategy
        NoAntialias = ... # type: QFont.StyleStrategy
        NoSubpixelAntialias = ... # type: QFont.StyleStrategy
        NoFontMerging = ... # type: QFont.StyleStrategy
        PreferNoShaping = ... # type: QFont.StyleStrategy
        ContextFontMerging = ... # type: QFont.StyleStrategy
        PreferTypoLineMetrics = ... # type: QFont.StyleStrategy

    class StyleHint(enum.Enum):
        Helvetica = ... # type: QFont.StyleHint
        SansSerif = ... # type: QFont.StyleHint
        Times = ... # type: QFont.StyleHint
        Serif = ... # type: QFont.StyleHint
        Courier = ... # type: QFont.StyleHint
        TypeWriter = ... # type: QFont.StyleHint
        OldEnglish = ... # type: QFont.StyleHint
        Decorative = ... # type: QFont.StyleHint
        System = ... # type: QFont.StyleHint
        AnyStyle = ... # type: QFont.StyleHint
        Cursive = ... # type: QFont.StyleHint
        Monospace = ... # type: QFont.StyleHint
        Fantasy = ... # type: QFont.StyleHint

    class Tag(PyQt6.sip.simplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, view: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QFont.Tag') -> None: ...

        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __ge__(self, rhs: 'QFont.Tag') -> bool: ...
        def __le__(self, rhs: 'QFont.Tag') -> bool: ...
        def __gt__(self, rhs: 'QFont.Tag') -> bool: ...
        def __lt__(self, rhs: 'QFont.Tag') -> bool: ...
        def __hash__(self) -> int: ...
        @staticmethod
        def fromString(view: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> typing.Optional['QFont.Tag']: ...
        @staticmethod
        def fromValue(value: int) -> typing.Optional['QFont.Tag']: ...
        def toString(self) -> QtCore.QByteArray: ...
        def value(self) -> int: ...
        def isValid(self) -> bool: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, families: collections.abc.Iterable[typing.Optional[str]], pointSize: int = ..., weight: int = ..., italic: bool = ...) -> None: ...
    @typing.overload
    def __init__(self, family: typing.Optional[str], pointSize: int = ..., weight: int = ..., italic: bool = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QFont') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def __ge__(self, a0: 'QFont') -> bool: ...
    def variableAxisTags(self) -> list['QFont.Tag']: ...
    def clearVariableAxes(self) -> None: ...
    def variableAxisValue(self, tag: 'QFont.Tag') -> float: ...
    def isVariableAxisSet(self, tag: 'QFont.Tag') -> bool: ...
    def unsetVariableAxis(self, tag: 'QFont.Tag') -> None: ...
    def setVariableAxis(self, tag: 'QFont.Tag', value: float) -> None: ...
    def clearFeatures(self) -> None: ...
    def featureTags(self) -> list['QFont.Tag']: ...
    def isFeatureSet(self, tag: 'QFont.Tag') -> bool: ...
    def featureValue(self, tag: 'QFont.Tag') -> int: ...
    def unsetFeature(self, tag: 'QFont.Tag') -> None: ...
    def setFeature(self, tag: 'QFont.Tag', value: int) -> None: ...
    def setFamilies(self, a0: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    def families(self) -> list[str]: ...
    def __hash__(self) -> int: ...
    def swap(self, other: 'QFont') -> None: ...
    def hintingPreference(self) -> 'QFont.HintingPreference': ...
    def setHintingPreference(self, hintingPreference: 'QFont.HintingPreference') -> None: ...
    def setStyleName(self, styleName: typing.Optional[str]) -> None: ...
    def styleName(self) -> str: ...
    def capitalization(self) -> 'QFont.Capitalization': ...
    def setCapitalization(self, a0: 'QFont.Capitalization') -> None: ...
    def setWordSpacing(self, spacing: float) -> None: ...
    def wordSpacing(self) -> float: ...
    def setLetterSpacing(self, type: 'QFont.SpacingType', spacing: float) -> None: ...
    def letterSpacingType(self) -> 'QFont.SpacingType': ...
    def letterSpacing(self) -> float: ...
    def setItalic(self, b: bool) -> None: ...
    def italic(self) -> bool: ...
    def setBold(self, enable: bool) -> None: ...
    def bold(self) -> bool: ...
    def resolve(self, a0: 'QFont') -> 'QFont': ...
    def defaultFamily(self) -> str: ...
    @staticmethod
    def cacheStatistics() -> None: ...
    @staticmethod
    def cleanup() -> None: ...
    @staticmethod
    def initialize() -> None: ...
    @staticmethod
    def removeSubstitutions(a0: typing.Optional[str]) -> None: ...
    @staticmethod
    def insertSubstitutions(a0: typing.Optional[str], a1: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    @staticmethod
    def insertSubstitution(a0: typing.Optional[str], a1: typing.Optional[str]) -> None: ...
    @staticmethod
    def substitutions() -> list[str]: ...
    @staticmethod
    def substitutes(a0: typing.Optional[str]) -> list[str]: ...
    @staticmethod
    def substitute(a0: typing.Optional[str]) -> str: ...
    def fromString(self, a0: typing.Optional[str]) -> bool: ...
    def toString(self) -> str: ...
    def key(self) -> str: ...
    def isCopyOf(self, a0: 'QFont') -> bool: ...
    def __lt__(self, a0: 'QFont') -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def exactMatch(self) -> bool: ...
    def setStretch(self, a0: int) -> None: ...
    def stretch(self) -> int: ...
    def setStyleStrategy(self, s: 'QFont.StyleStrategy') -> None: ...
    def setStyleHint(self, hint: 'QFont.StyleHint', strategy: 'QFont.StyleStrategy' = ...) -> None: ...
    def styleStrategy(self) -> 'QFont.StyleStrategy': ...
    def styleHint(self) -> 'QFont.StyleHint': ...
    def setKerning(self, a0: bool) -> None: ...
    def kerning(self) -> bool: ...
    def setFixedPitch(self, a0: bool) -> None: ...
    def fixedPitch(self) -> bool: ...
    def setStrikeOut(self, a0: bool) -> None: ...
    def strikeOut(self) -> bool: ...
    def setOverline(self, a0: bool) -> None: ...
    def overline(self) -> bool: ...
    def setUnderline(self, a0: bool) -> None: ...
    def underline(self) -> bool: ...
    def style(self) -> 'QFont.Style': ...
    def setStyle(self, style: 'QFont.Style') -> None: ...
    def setWeight(self, weight: int) -> None: ...
    def weight(self) -> int: ...
    def setPixelSize(self, a0: int) -> None: ...
    def pixelSize(self) -> int: ...
    def setPointSizeF(self, a0: float) -> None: ...
    def pointSizeF(self) -> float: ...
    def setPointSize(self, a0: int) -> None: ...
    def pointSize(self) -> int: ...
    def setFamily(self, a0: typing.Optional[str]) -> None: ...
    def family(self) -> str: ...


class QFontDatabase(PyQt6.sip.simplewrapper):

    class SystemFont(enum.Enum):
        GeneralFont = ... # type: QFontDatabase.SystemFont
        FixedFont = ... # type: QFontDatabase.SystemFont
        TitleFont = ... # type: QFontDatabase.SystemFont
        SmallestReadableFont = ... # type: QFontDatabase.SystemFont

    class WritingSystem(enum.Enum):
        Any = ... # type: QFontDatabase.WritingSystem
        Latin = ... # type: QFontDatabase.WritingSystem
        Greek = ... # type: QFontDatabase.WritingSystem
        Cyrillic = ... # type: QFontDatabase.WritingSystem
        Armenian = ... # type: QFontDatabase.WritingSystem
        Hebrew = ... # type: QFontDatabase.WritingSystem
        Arabic = ... # type: QFontDatabase.WritingSystem
        Syriac = ... # type: QFontDatabase.WritingSystem
        Thaana = ... # type: QFontDatabase.WritingSystem
        Devanagari = ... # type: QFontDatabase.WritingSystem
        Bengali = ... # type: QFontDatabase.WritingSystem
        Gurmukhi = ... # type: QFontDatabase.WritingSystem
        Gujarati = ... # type: QFontDatabase.WritingSystem
        Oriya = ... # type: QFontDatabase.WritingSystem
        Tamil = ... # type: QFontDatabase.WritingSystem
        Telugu = ... # type: QFontDatabase.WritingSystem
        Kannada = ... # type: QFontDatabase.WritingSystem
        Malayalam = ... # type: QFontDatabase.WritingSystem
        Sinhala = ... # type: QFontDatabase.WritingSystem
        Thai = ... # type: QFontDatabase.WritingSystem
        Lao = ... # type: QFontDatabase.WritingSystem
        Tibetan = ... # type: QFontDatabase.WritingSystem
        Myanmar = ... # type: QFontDatabase.WritingSystem
        Georgian = ... # type: QFontDatabase.WritingSystem
        Khmer = ... # type: QFontDatabase.WritingSystem
        SimplifiedChinese = ... # type: QFontDatabase.WritingSystem
        TraditionalChinese = ... # type: QFontDatabase.WritingSystem
        Japanese = ... # type: QFontDatabase.WritingSystem
        Korean = ... # type: QFontDatabase.WritingSystem
        Vietnamese = ... # type: QFontDatabase.WritingSystem
        Other = ... # type: QFontDatabase.WritingSystem
        Symbol = ... # type: QFontDatabase.WritingSystem
        Ogham = ... # type: QFontDatabase.WritingSystem
        Runic = ... # type: QFontDatabase.WritingSystem
        Nko = ... # type: QFontDatabase.WritingSystem

    def __init__(self, a0: 'QFontDatabase') -> None: ...

    @staticmethod
    def applicationEmojiFontFamilies() -> list[str]: ...
    @staticmethod
    def setApplicationEmojiFontFamilies(familyNames: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    @staticmethod
    def removeApplicationEmojiFontFamily(familyName: typing.Optional[str]) -> bool: ...
    @staticmethod
    def addApplicationEmojiFontFamily(familyName: typing.Optional[str]) -> None: ...
    @staticmethod
    def applicationFallbackFontFamilies(script: QtCore.QChar.Script) -> list[str]: ...
    @staticmethod
    def setApplicationFallbackFontFamilies(a0: QtCore.QChar.Script, familyNames: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    @staticmethod
    def removeApplicationFallbackFontFamily(script: QtCore.QChar.Script, familyName: typing.Optional[str]) -> bool: ...
    @staticmethod
    def addApplicationFallbackFontFamily(script: QtCore.QChar.Script, familyName: typing.Optional[str]) -> None: ...
    @staticmethod
    def isPrivateFamily(family: typing.Optional[str]) -> bool: ...
    @staticmethod
    def systemFont(type: 'QFontDatabase.SystemFont') -> QFont: ...
    @staticmethod
    def removeAllApplicationFonts() -> bool: ...
    @staticmethod
    def removeApplicationFont(id: int) -> bool: ...
    @staticmethod
    def applicationFontFamilies(id: int) -> list[str]: ...
    @staticmethod
    def addApplicationFontFromData(fontData: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> int: ...
    @staticmethod
    def addApplicationFont(fileName: typing.Optional[str]) -> int: ...
    @staticmethod
    def writingSystemSample(writingSystem: 'QFontDatabase.WritingSystem') -> str: ...
    @staticmethod
    def writingSystemName(writingSystem: 'QFontDatabase.WritingSystem') -> str: ...
    @staticmethod
    def weight(family: typing.Optional[str], style: typing.Optional[str]) -> int: ...
    @staticmethod
    def bold(family: typing.Optional[str], style: typing.Optional[str]) -> bool: ...
    @staticmethod
    def italic(family: typing.Optional[str], style: typing.Optional[str]) -> bool: ...
    @staticmethod
    def isFixedPitch(family: typing.Optional[str], style: typing.Optional[str] = ...) -> bool: ...
    @staticmethod
    def isScalable(family: typing.Optional[str], style: typing.Optional[str] = ...) -> bool: ...
    @staticmethod
    def isSmoothlyScalable(family: typing.Optional[str], style: typing.Optional[str] = ...) -> bool: ...
    @staticmethod
    def isBitmapScalable(family: typing.Optional[str], style: typing.Optional[str] = ...) -> bool: ...
    @staticmethod
    def font(family: typing.Optional[str], style: typing.Optional[str], pointSize: int) -> QFont: ...
    @typing.overload
    @staticmethod
    def styleString(fontInfo: 'QFontInfo') -> str: ...
    @typing.overload
    @staticmethod
    def styleString(font: QFont) -> str: ...
    @staticmethod
    def smoothSizes(family: typing.Optional[str], style: typing.Optional[str]) -> list[int]: ...
    @staticmethod
    def pointSizes(family: typing.Optional[str], style: typing.Optional[str] = ...) -> list[int]: ...
    @staticmethod
    def styles(family: typing.Optional[str]) -> list[str]: ...
    @staticmethod
    def families(writingSystem: 'QFontDatabase.WritingSystem' = ...) -> list[str]: ...
    @typing.overload
    @staticmethod
    def writingSystems(family: typing.Optional[str]) -> list['QFontDatabase.WritingSystem']: ...
    @typing.overload
    @staticmethod
    def writingSystems() -> list['QFontDatabase.WritingSystem']: ...
    @staticmethod
    def standardSizes() -> list[int]: ...


class QFontInfo(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self, a0: QFont) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QFontInfo') -> None: ...

    def variableAxes(self) -> list['QFontVariableAxis']: ...
    def swap(self, other: 'QFontInfo') -> None: ...
    def styleName(self) -> str: ...
    def exactMatch(self) -> bool: ...
    def styleHint(self) -> QFont.StyleHint: ...
    def fixedPitch(self) -> bool: ...
    def bold(self) -> bool: ...
    def weight(self) -> int: ...
    def style(self) -> QFont.Style: ...
    def italic(self) -> bool: ...
    def pointSizeF(self) -> float: ...
    def pointSize(self) -> int: ...
    def pixelSize(self) -> int: ...
    def family(self) -> str: ...


class QFontMetrics(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self, a0: QFont) -> None: ...
    @typing.overload
    def __init__(self, font: QFont, pd: typing.Optional[QPaintDevice]) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QFontMetrics') -> None: ...

    def fontDpi(self) -> float: ...
    @typing.overload
    def horizontalAdvance(self, a0: typing.Optional[str], textOption: 'QTextOption') -> int: ...
    @typing.overload
    def horizontalAdvance(self, a0: typing.Optional[str], length: int = ...) -> int: ...
    def capHeight(self) -> int: ...
    def swap(self, other: 'QFontMetrics') -> None: ...
    def inFontUcs4(self, character: int) -> bool: ...
    @typing.overload
    def tightBoundingRect(self, text: typing.Optional[str], textOption: 'QTextOption') -> QtCore.QRect: ...
    @typing.overload
    def tightBoundingRect(self, text: typing.Optional[str]) -> QtCore.QRect: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def elidedText(self, text: typing.Optional[str], mode: QtCore.Qt.TextElideMode, width: int, flags: int = ...) -> str: ...
    def averageCharWidth(self) -> int: ...
    def lineWidth(self) -> int: ...
    def strikeOutPos(self) -> int: ...
    def overlinePos(self) -> int: ...
    def underlinePos(self) -> int: ...
    def size(self, flags: int, text: typing.Optional[str], tabStops: int = ..., tabArray: typing.Optional[list[int]] = ...) -> QtCore.QSize: ...
    def boundingRectChar(self, a0: str) -> QtCore.QRect: ...
    @typing.overload
    def boundingRect(self, text: typing.Optional[str], textOption: 'QTextOption') -> QtCore.QRect: ...
    @typing.overload
    def boundingRect(self, text: typing.Optional[str]) -> QtCore.QRect: ...
    @typing.overload
    def boundingRect(self, rect: QtCore.QRect, flags: int, text: typing.Optional[str], tabStops: int = ..., tabArray: typing.Optional[list[int]] = ...) -> QtCore.QRect: ...
    @typing.overload
    def boundingRect(self, x: int, y: int, width: int, height: int, flags: int, text: typing.Optional[str], tabStops: int = ..., tabArray: typing.Optional[list[int]] = ...) -> QtCore.QRect: ...
    def rightBearing(self, a0: str) -> int: ...
    def leftBearing(self, a0: str) -> int: ...
    def inFont(self, a0: str) -> bool: ...
    def xHeight(self) -> int: ...
    def maxWidth(self) -> int: ...
    def minRightBearing(self) -> int: ...
    def minLeftBearing(self) -> int: ...
    def lineSpacing(self) -> int: ...
    def leading(self) -> int: ...
    def height(self) -> int: ...
    def descent(self) -> int: ...
    def ascent(self) -> int: ...


class QFontMetricsF(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self, a0: QFont) -> None: ...
    @typing.overload
    def __init__(self, font: QFont, pd: typing.Optional[QPaintDevice]) -> None: ...
    @typing.overload
    def __init__(self, a0: QFontMetrics) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QFontMetricsF') -> None: ...

    def fontDpi(self) -> float: ...
    @typing.overload
    def horizontalAdvance(self, string: typing.Optional[str], textOption: 'QTextOption') -> float: ...
    @typing.overload
    def horizontalAdvance(self, string: typing.Optional[str], length: int = ...) -> float: ...
    def capHeight(self) -> float: ...
    def swap(self, other: 'QFontMetricsF') -> None: ...
    def inFontUcs4(self, character: int) -> bool: ...
    @typing.overload
    def tightBoundingRect(self, text: typing.Optional[str], textOption: 'QTextOption') -> QtCore.QRectF: ...
    @typing.overload
    def tightBoundingRect(self, text: typing.Optional[str]) -> QtCore.QRectF: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def elidedText(self, text: typing.Optional[str], mode: QtCore.Qt.TextElideMode, width: float, flags: int = ...) -> str: ...
    def averageCharWidth(self) -> float: ...
    def lineWidth(self) -> float: ...
    def strikeOutPos(self) -> float: ...
    def overlinePos(self) -> float: ...
    def underlinePos(self) -> float: ...
    def size(self, flags: int, text: typing.Optional[str], tabStops: int = ..., tabArray: typing.Optional[list[int]] = ...) -> QtCore.QSizeF: ...
    def boundingRectChar(self, a0: str) -> QtCore.QRectF: ...
    @typing.overload
    def boundingRect(self, text: typing.Optional[str], textOption: 'QTextOption') -> QtCore.QRectF: ...
    @typing.overload
    def boundingRect(self, string: typing.Optional[str]) -> QtCore.QRectF: ...
    @typing.overload
    def boundingRect(self, rect: QtCore.QRectF, flags: int, text: typing.Optional[str], tabStops: int = ..., tabArray: typing.Optional[list[int]] = ...) -> QtCore.QRectF: ...
    def rightBearing(self, a0: str) -> float: ...
    def leftBearing(self, a0: str) -> float: ...
    def inFont(self, a0: str) -> bool: ...
    def xHeight(self) -> float: ...
    def maxWidth(self) -> float: ...
    def minRightBearing(self) -> float: ...
    def minLeftBearing(self) -> float: ...
    def lineSpacing(self) -> float: ...
    def leading(self) -> float: ...
    def height(self) -> float: ...
    def descent(self) -> float: ...
    def ascent(self) -> float: ...


class QFontVariableAxis(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, axis: 'QFontVariableAxis') -> None: ...

    def setDefaultValue(self, defaultValue: float) -> None: ...
    def defaultValue(self) -> float: ...
    def setMaximumValue(self, maximumValue: float) -> None: ...
    def maximumValue(self) -> float: ...
    def setMinimumValue(self, minimumValue: float) -> None: ...
    def minimumValue(self) -> float: ...
    def setName(self, name: typing.Optional[str]) -> None: ...
    def name(self) -> str: ...
    def setTag(self, tag: QFont.Tag) -> None: ...
    def tag(self) -> QFont.Tag: ...
    def swap(self, other: 'QFontVariableAxis') -> None: ...


class QMatrix4x3(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix4x3') -> None: ...
    @typing.overload
    def __init__(self, values: collections.abc.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix4x3': ...
    def __imul__(self, a0: float) -> 'QMatrix4x3': ...
    def __isub__(self, a0: 'QMatrix4x3') -> 'QMatrix4x3': ...
    def __iadd__(self, a0: 'QMatrix4x3') -> 'QMatrix4x3': ...
    def transposed(self) -> 'QMatrix3x4': ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> list[float]: ...
    def data(self) -> list[float]: ...
    def __repr__(self) -> str: ...


class QMatrix4x2(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix4x2') -> None: ...
    @typing.overload
    def __init__(self, values: collections.abc.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix4x2': ...
    def __imul__(self, a0: float) -> 'QMatrix4x2': ...
    def __isub__(self, a0: 'QMatrix4x2') -> 'QMatrix4x2': ...
    def __iadd__(self, a0: 'QMatrix4x2') -> 'QMatrix4x2': ...
    def transposed(self) -> 'QMatrix2x4': ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> list[float]: ...
    def data(self) -> list[float]: ...
    def __repr__(self) -> str: ...


class QMatrix3x4(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix3x4') -> None: ...
    @typing.overload
    def __init__(self, values: collections.abc.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix3x4': ...
    def __imul__(self, a0: float) -> 'QMatrix3x4': ...
    def __isub__(self, a0: 'QMatrix3x4') -> 'QMatrix3x4': ...
    def __iadd__(self, a0: 'QMatrix3x4') -> 'QMatrix3x4': ...
    def transposed(self) -> QMatrix4x3: ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> list[float]: ...
    def data(self) -> list[float]: ...
    def __repr__(self) -> str: ...


class QMatrix3x3(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix3x3') -> None: ...
    @typing.overload
    def __init__(self, values: collections.abc.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix3x3': ...
    def __imul__(self, a0: float) -> 'QMatrix3x3': ...
    def __isub__(self, a0: 'QMatrix3x3') -> 'QMatrix3x3': ...
    def __iadd__(self, a0: 'QMatrix3x3') -> 'QMatrix3x3': ...
    def transposed(self) -> 'QMatrix3x3': ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> list[float]: ...
    def data(self) -> list[float]: ...
    def __repr__(self) -> str: ...


class QMatrix3x2(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix3x2') -> None: ...
    @typing.overload
    def __init__(self, values: collections.abc.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix3x2': ...
    def __imul__(self, a0: float) -> 'QMatrix3x2': ...
    def __isub__(self, a0: 'QMatrix3x2') -> 'QMatrix3x2': ...
    def __iadd__(self, a0: 'QMatrix3x2') -> 'QMatrix3x2': ...
    def transposed(self) -> 'QMatrix2x3': ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> list[float]: ...
    def data(self) -> list[float]: ...
    def __repr__(self) -> str: ...


class QMatrix2x4(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix2x4') -> None: ...
    @typing.overload
    def __init__(self, values: collections.abc.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix2x4': ...
    def __imul__(self, a0: float) -> 'QMatrix2x4': ...
    def __isub__(self, a0: 'QMatrix2x4') -> 'QMatrix2x4': ...
    def __iadd__(self, a0: 'QMatrix2x4') -> 'QMatrix2x4': ...
    def transposed(self) -> QMatrix4x2: ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> list[float]: ...
    def data(self) -> list[float]: ...
    def __repr__(self) -> str: ...


class QMatrix2x3(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix2x3') -> None: ...
    @typing.overload
    def __init__(self, values: collections.abc.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix2x3': ...
    def __imul__(self, a0: float) -> 'QMatrix2x3': ...
    def __isub__(self, a0: 'QMatrix2x3') -> 'QMatrix2x3': ...
    def __iadd__(self, a0: 'QMatrix2x3') -> 'QMatrix2x3': ...
    def transposed(self) -> QMatrix3x2: ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> list[float]: ...
    def data(self) -> list[float]: ...
    def __repr__(self) -> str: ...


class QMatrix2x2(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMatrix2x2') -> None: ...
    @typing.overload
    def __init__(self, values: collections.abc.Sequence[float]) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, a0: float) -> 'QMatrix2x2': ...
    def __imul__(self, a0: float) -> 'QMatrix2x2': ...
    def __isub__(self, a0: 'QMatrix2x2') -> 'QMatrix2x2': ...
    def __iadd__(self, a0: 'QMatrix2x2') -> 'QMatrix2x2': ...
    def transposed(self) -> 'QMatrix2x2': ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def copyDataTo(self) -> list[float]: ...
    def data(self) -> list[float]: ...
    def __repr__(self) -> str: ...


class QGlyphRun(PyQt6.sip.simplewrapper):

    class GlyphRunFlag(enum.Flag):
        Overline = ... # type: QGlyphRun.GlyphRunFlag
        Underline = ... # type: QGlyphRun.GlyphRunFlag
        StrikeOut = ... # type: QGlyphRun.GlyphRunFlag
        RightToLeft = ... # type: QGlyphRun.GlyphRunFlag
        SplitLigature = ... # type: QGlyphRun.GlyphRunFlag

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QGlyphRun') -> None: ...

    def sourceString(self) -> str: ...
    def setSourceString(self, sourceString: typing.Optional[str]) -> None: ...
    def setStringIndexes(self, stringIndexes: collections.abc.Iterable[int]) -> None: ...
    def stringIndexes(self) -> list[int]: ...
    def swap(self, other: 'QGlyphRun') -> None: ...
    def isEmpty(self) -> bool: ...
    def boundingRect(self) -> QtCore.QRectF: ...
    def setBoundingRect(self, boundingRect: QtCore.QRectF) -> None: ...
    def flags(self) -> 'QGlyphRun.GlyphRunFlag': ...
    def setFlags(self, flags: 'QGlyphRun.GlyphRunFlag') -> None: ...
    def setFlag(self, flag: 'QGlyphRun.GlyphRunFlag', enabled: bool = ...) -> None: ...
    def isRightToLeft(self) -> bool: ...
    def setRightToLeft(self, on: bool) -> None: ...
    def strikeOut(self) -> bool: ...
    def setStrikeOut(self, strikeOut: bool) -> None: ...
    def underline(self) -> bool: ...
    def setUnderline(self, underline: bool) -> None: ...
    def overline(self) -> bool: ...
    def setOverline(self, overline: bool) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def clear(self) -> None: ...
    def setPositions(self, positions: collections.abc.Iterable[QtCore.QPointF]) -> None: ...
    def positions(self) -> list[QtCore.QPointF]: ...
    def setGlyphIndexes(self, glyphIndexes: collections.abc.Iterable[int]) -> None: ...
    def glyphIndexes(self) -> list[int]: ...
    def setRawFont(self, rawFont: 'QRawFont') -> None: ...
    def rawFont(self) -> 'QRawFont': ...


class QGuiApplication(QtCore.QCoreApplication):

    def __init__(self, argv: list[str]) -> None: ...

    def event(self, a0: typing.Optional[QtCore.QEvent]) -> bool: ...
    def setBadgeNumber(self, number: int) -> None: ...
    @staticmethod
    def highDpiScaleFactorRoundingPolicy() -> QtCore.Qt.HighDpiScaleFactorRoundingPolicy: ...
    @staticmethod
    def setHighDpiScaleFactorRoundingPolicy(policy: QtCore.Qt.HighDpiScaleFactorRoundingPolicy) -> None: ...
    @staticmethod
    def screenAt(point: QtCore.QPoint) -> typing.Optional['QScreen']: ...
    @staticmethod
    def desktopFileName() -> str: ...
    @staticmethod
    def setDesktopFileName(name: typing.Optional[str]) -> None: ...
    primaryScreenChanged: typing.ClassVar[QtCore.pyqtSignal]
    layoutDirectionChanged: typing.ClassVar[QtCore.pyqtSignal]
    screenRemoved: typing.ClassVar[QtCore.pyqtSignal]
    @staticmethod
    def windowIcon() -> 'QIcon': ...
    @staticmethod
    def setWindowIcon(icon: 'QIcon') -> None: ...
    @staticmethod
    def sync() -> None: ...
    @staticmethod
    def applicationState() -> QtCore.Qt.ApplicationState: ...
    def isSavingSession(self) -> bool: ...
    def sessionKey(self) -> str: ...
    def sessionId(self) -> str: ...
    def isSessionRestored(self) -> bool: ...
    def devicePixelRatio(self) -> float: ...
    @staticmethod
    def inputMethod() -> typing.Optional['QInputMethod']: ...
    @staticmethod
    def styleHints() -> typing.Optional['QStyleHints']: ...
    @staticmethod
    def modalWindow() -> typing.Optional['QWindow']: ...
    @staticmethod
    def applicationDisplayName() -> str: ...
    @staticmethod
    def setApplicationDisplayName(name: typing.Optional[str]) -> None: ...
    applicationDisplayNameChanged: typing.ClassVar[QtCore.pyqtSignal]
    applicationStateChanged: typing.ClassVar[QtCore.pyqtSignal]
    focusWindowChanged: typing.ClassVar[QtCore.pyqtSignal]
    saveStateRequest: typing.ClassVar[QtCore.pyqtSignal]
    commitDataRequest: typing.ClassVar[QtCore.pyqtSignal]
    focusObjectChanged: typing.ClassVar[QtCore.pyqtSignal]
    lastWindowClosed: typing.ClassVar[QtCore.pyqtSignal]
    screenAdded: typing.ClassVar[QtCore.pyqtSignal]
    fontDatabaseChanged: typing.ClassVar[QtCore.pyqtSignal]
    def notify(self, a0: typing.Optional[QtCore.QObject], a1: typing.Optional[QtCore.QEvent]) -> bool: ...
    @staticmethod
    def exec() -> int: ...
    @staticmethod
    def quitOnLastWindowClosed() -> bool: ...
    @staticmethod
    def setQuitOnLastWindowClosed(quit: bool) -> None: ...
    @staticmethod
    def desktopSettingsAware() -> bool: ...
    @staticmethod
    def setDesktopSettingsAware(on: bool) -> None: ...
    @staticmethod
    def isLeftToRight() -> bool: ...
    @staticmethod
    def isRightToLeft() -> bool: ...
    @staticmethod
    def layoutDirection() -> QtCore.Qt.LayoutDirection: ...
    @staticmethod
    def setLayoutDirection(direction: QtCore.Qt.LayoutDirection) -> None: ...
    @staticmethod
    def mouseButtons() -> QtCore.Qt.MouseButton: ...
    @staticmethod
    def queryKeyboardModifiers() -> QtCore.Qt.KeyboardModifier: ...
    @staticmethod
    def keyboardModifiers() -> QtCore.Qt.KeyboardModifier: ...
    @staticmethod
    def setPalette(pal: 'QPalette') -> None: ...
    @staticmethod
    def palette() -> 'QPalette': ...
    @staticmethod
    def clipboard() -> typing.Optional[QClipboard]: ...
    @staticmethod
    def setFont(a0: QFont) -> None: ...
    @staticmethod
    def font() -> QFont: ...
    @staticmethod
    def restoreOverrideCursor() -> None: ...
    @staticmethod
    def changeOverrideCursor(a0: typing.Union[QCursor, QtCore.Qt.CursorShape]) -> None: ...
    @staticmethod
    def setOverrideCursor(a0: typing.Union[QCursor, QtCore.Qt.CursorShape]) -> None: ...
    @staticmethod
    def overrideCursor() -> typing.Optional[QCursor]: ...
    @staticmethod
    def screens() -> list['QScreen']: ...
    @staticmethod
    def primaryScreen() -> typing.Optional['QScreen']: ...
    @staticmethod
    def focusObject() -> typing.Optional[QtCore.QObject]: ...
    @staticmethod
    def focusWindow() -> typing.Optional['QWindow']: ...
    @staticmethod
    def platformName() -> str: ...
    @staticmethod
    def topLevelAt(pos: QtCore.QPoint) -> typing.Optional['QWindow']: ...
    @staticmethod
    def topLevelWindows() -> list['QWindow']: ...
    @staticmethod
    def allWindows() -> list['QWindow']: ...


class QIcon(PyQt6.sip.wrapper):

    class ThemeIcon(enum.Enum):
        AddressBookNew = ... # type: QIcon.ThemeIcon
        ApplicationExit = ... # type: QIcon.ThemeIcon
        AppointmentNew = ... # type: QIcon.ThemeIcon
        CallStart = ... # type: QIcon.ThemeIcon
        CallStop = ... # type: QIcon.ThemeIcon
        ContactNew = ... # type: QIcon.ThemeIcon
        DocumentNew = ... # type: QIcon.ThemeIcon
        DocumentOpen = ... # type: QIcon.ThemeIcon
        DocumentOpenRecent = ... # type: QIcon.ThemeIcon
        DocumentPageSetup = ... # type: QIcon.ThemeIcon
        DocumentPrint = ... # type: QIcon.ThemeIcon
        DocumentPrintPreview = ... # type: QIcon.ThemeIcon
        DocumentProperties = ... # type: QIcon.ThemeIcon
        DocumentRevert = ... # type: QIcon.ThemeIcon
        DocumentSave = ... # type: QIcon.ThemeIcon
        DocumentSaveAs = ... # type: QIcon.ThemeIcon
        DocumentSend = ... # type: QIcon.ThemeIcon
        EditClear = ... # type: QIcon.ThemeIcon
        EditCopy = ... # type: QIcon.ThemeIcon
        EditCut = ... # type: QIcon.ThemeIcon
        EditDelete = ... # type: QIcon.ThemeIcon
        EditFind = ... # type: QIcon.ThemeIcon
        EditPaste = ... # type: QIcon.ThemeIcon
        EditRedo = ... # type: QIcon.ThemeIcon
        EditSelectAll = ... # type: QIcon.ThemeIcon
        EditUndo = ... # type: QIcon.ThemeIcon
        FolderNew = ... # type: QIcon.ThemeIcon
        FormatIndentLess = ... # type: QIcon.ThemeIcon
        FormatIndentMore = ... # type: QIcon.ThemeIcon
        FormatJustifyCenter = ... # type: QIcon.ThemeIcon
        FormatJustifyFill = ... # type: QIcon.ThemeIcon
        FormatJustifyLeft = ... # type: QIcon.ThemeIcon
        FormatJustifyRight = ... # type: QIcon.ThemeIcon
        FormatTextDirectionLtr = ... # type: QIcon.ThemeIcon
        FormatTextDirectionRtl = ... # type: QIcon.ThemeIcon
        FormatTextBold = ... # type: QIcon.ThemeIcon
        FormatTextItalic = ... # type: QIcon.ThemeIcon
        FormatTextUnderline = ... # type: QIcon.ThemeIcon
        FormatTextStrikethrough = ... # type: QIcon.ThemeIcon
        GoDown = ... # type: QIcon.ThemeIcon
        GoHome = ... # type: QIcon.ThemeIcon
        GoNext = ... # type: QIcon.ThemeIcon
        GoPrevious = ... # type: QIcon.ThemeIcon
        GoUp = ... # type: QIcon.ThemeIcon
        HelpAbout = ... # type: QIcon.ThemeIcon
        HelpFaq = ... # type: QIcon.ThemeIcon
        InsertImage = ... # type: QIcon.ThemeIcon
        InsertLink = ... # type: QIcon.ThemeIcon
        InsertText = ... # type: QIcon.ThemeIcon
        ListAdd = ... # type: QIcon.ThemeIcon
        ListRemove = ... # type: QIcon.ThemeIcon
        MailForward = ... # type: QIcon.ThemeIcon
        MailMarkImportant = ... # type: QIcon.ThemeIcon
        MailMarkRead = ... # type: QIcon.ThemeIcon
        MailMarkUnread = ... # type: QIcon.ThemeIcon
        MailMessageNew = ... # type: QIcon.ThemeIcon
        MailReplyAll = ... # type: QIcon.ThemeIcon
        MailReplySender = ... # type: QIcon.ThemeIcon
        MailSend = ... # type: QIcon.ThemeIcon
        MediaEject = ... # type: QIcon.ThemeIcon
        MediaPlaybackPause = ... # type: QIcon.ThemeIcon
        MediaPlaybackStart = ... # type: QIcon.ThemeIcon
        MediaPlaybackStop = ... # type: QIcon.ThemeIcon
        MediaRecord = ... # type: QIcon.ThemeIcon
        MediaSeekBackward = ... # type: QIcon.ThemeIcon
        MediaSeekForward = ... # type: QIcon.ThemeIcon
        MediaSkipBackward = ... # type: QIcon.ThemeIcon
        MediaSkipForward = ... # type: QIcon.ThemeIcon
        ObjectRotateLeft = ... # type: QIcon.ThemeIcon
        ObjectRotateRight = ... # type: QIcon.ThemeIcon
        ProcessStop = ... # type: QIcon.ThemeIcon
        SystemLockScreen = ... # type: QIcon.ThemeIcon
        SystemLogOut = ... # type: QIcon.ThemeIcon
        SystemSearch = ... # type: QIcon.ThemeIcon
        SystemReboot = ... # type: QIcon.ThemeIcon
        SystemShutdown = ... # type: QIcon.ThemeIcon
        ToolsCheckSpelling = ... # type: QIcon.ThemeIcon
        ViewFullscreen = ... # type: QIcon.ThemeIcon
        ViewRefresh = ... # type: QIcon.ThemeIcon
        ViewRestore = ... # type: QIcon.ThemeIcon
        WindowClose = ... # type: QIcon.ThemeIcon
        WindowNew = ... # type: QIcon.ThemeIcon
        ZoomFitBest = ... # type: QIcon.ThemeIcon
        ZoomIn = ... # type: QIcon.ThemeIcon
        ZoomOut = ... # type: QIcon.ThemeIcon
        AudioCard = ... # type: QIcon.ThemeIcon
        AudioInputMicrophone = ... # type: QIcon.ThemeIcon
        Battery = ... # type: QIcon.ThemeIcon
        CameraPhoto = ... # type: QIcon.ThemeIcon
        CameraVideo = ... # type: QIcon.ThemeIcon
        CameraWeb = ... # type: QIcon.ThemeIcon
        Computer = ... # type: QIcon.ThemeIcon
        DriveHarddisk = ... # type: QIcon.ThemeIcon
        DriveOptical = ... # type: QIcon.ThemeIcon
        InputGaming = ... # type: QIcon.ThemeIcon
        InputKeyboard = ... # type: QIcon.ThemeIcon
        InputMouse = ... # type: QIcon.ThemeIcon
        InputTablet = ... # type: QIcon.ThemeIcon
        MediaFlash = ... # type: QIcon.ThemeIcon
        MediaOptical = ... # type: QIcon.ThemeIcon
        MediaTape = ... # type: QIcon.ThemeIcon
        MultimediaPlayer = ... # type: QIcon.ThemeIcon
        NetworkWired = ... # type: QIcon.ThemeIcon
        NetworkWireless = ... # type: QIcon.ThemeIcon
        Phone = ... # type: QIcon.ThemeIcon
        Printer = ... # type: QIcon.ThemeIcon
        Scanner = ... # type: QIcon.ThemeIcon
        VideoDisplay = ... # type: QIcon.ThemeIcon
        AppointmentMissed = ... # type: QIcon.ThemeIcon
        AppointmentSoon = ... # type: QIcon.ThemeIcon
        AudioVolumeHigh = ... # type: QIcon.ThemeIcon
        AudioVolumeLow = ... # type: QIcon.ThemeIcon
        AudioVolumeMedium = ... # type: QIcon.ThemeIcon
        AudioVolumeMuted = ... # type: QIcon.ThemeIcon
        BatteryCaution = ... # type: QIcon.ThemeIcon
        BatteryLow = ... # type: QIcon.ThemeIcon
        DialogError = ... # type: QIcon.ThemeIcon
        DialogInformation = ... # type: QIcon.ThemeIcon
        DialogPassword = ... # type: QIcon.ThemeIcon
        DialogQuestion = ... # type: QIcon.ThemeIcon
        DialogWarning = ... # type: QIcon.ThemeIcon
        FolderDragAccept = ... # type: QIcon.ThemeIcon
        FolderOpen = ... # type: QIcon.ThemeIcon
        FolderVisiting = ... # type: QIcon.ThemeIcon
        ImageLoading = ... # type: QIcon.ThemeIcon
        ImageMissing = ... # type: QIcon.ThemeIcon
        MailAttachment = ... # type: QIcon.ThemeIcon
        MailUnread = ... # type: QIcon.ThemeIcon
        MailRead = ... # type: QIcon.ThemeIcon
        MailReplied = ... # type: QIcon.ThemeIcon
        MediaPlaylistRepeat = ... # type: QIcon.ThemeIcon
        MediaPlaylistShuffle = ... # type: QIcon.ThemeIcon
        NetworkOffline = ... # type: QIcon.ThemeIcon
        PrinterPrinting = ... # type: QIcon.ThemeIcon
        SecurityHigh = ... # type: QIcon.ThemeIcon
        SecurityLow = ... # type: QIcon.ThemeIcon
        SoftwareUpdateAvailable = ... # type: QIcon.ThemeIcon
        SoftwareUpdateUrgent = ... # type: QIcon.ThemeIcon
        SyncError = ... # type: QIcon.ThemeIcon
        SyncSynchronizing = ... # type: QIcon.ThemeIcon
        UserAvailable = ... # type: QIcon.ThemeIcon
        UserOffline = ... # type: QIcon.ThemeIcon
        WeatherClear = ... # type: QIcon.ThemeIcon
        WeatherClearNight = ... # type: QIcon.ThemeIcon
        WeatherFewClouds = ... # type: QIcon.ThemeIcon
        WeatherFewCloudsNight = ... # type: QIcon.ThemeIcon
        WeatherFog = ... # type: QIcon.ThemeIcon
        WeatherShowers = ... # type: QIcon.ThemeIcon
        WeatherSnow = ... # type: QIcon.ThemeIcon
        WeatherStorm = ... # type: QIcon.ThemeIcon

    class State(enum.Enum):
        On = ... # type: QIcon.State
        Off = ... # type: QIcon.State

    class Mode(enum.Enum):
        Normal = ... # type: QIcon.Mode
        Disabled = ... # type: QIcon.Mode
        Active = ... # type: QIcon.Mode
        Selected = ... # type: QIcon.Mode

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, pixmap: QPixmap) -> None: ...
    @typing.overload
    def __init__(self, other: 'QIcon') -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, engine: typing.Optional['QIconEngine']) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    @staticmethod
    def setFallbackThemeName(name: typing.Optional[str]) -> None: ...
    @staticmethod
    def fallbackThemeName() -> str: ...
    @staticmethod
    def setFallbackSearchPaths(paths: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    @staticmethod
    def fallbackSearchPaths() -> list[str]: ...
    def isMask(self) -> bool: ...
    def setIsMask(self, isMask: bool) -> None: ...
    def swap(self, other: 'QIcon') -> None: ...
    def name(self) -> str: ...
    @staticmethod
    def setThemeName(path: typing.Optional[str]) -> None: ...
    @staticmethod
    def themeName() -> str: ...
    @staticmethod
    def setThemeSearchPaths(searchpath: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    @staticmethod
    def themeSearchPaths() -> list[str]: ...
    @typing.overload
    @staticmethod
    def hasThemeIcon(name: typing.Optional[str]) -> bool: ...
    @typing.overload
    @staticmethod
    def hasThemeIcon(icon: 'QIcon.ThemeIcon') -> bool: ...
    @typing.overload
    @staticmethod
    def fromTheme(name: typing.Optional[str]) -> 'QIcon': ...
    @typing.overload
    @staticmethod
    def fromTheme(name: typing.Optional[str], fallback: 'QIcon') -> 'QIcon': ...
    @typing.overload
    @staticmethod
    def fromTheme(icon: 'QIcon.ThemeIcon') -> 'QIcon': ...
    @typing.overload
    @staticmethod
    def fromTheme(icon: 'QIcon.ThemeIcon', fallback: 'QIcon') -> 'QIcon': ...
    def cacheKey(self) -> int: ...
    def addFile(self, fileName: typing.Optional[str], size: QtCore.QSize = ..., mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> None: ...
    def addPixmap(self, pixmap: QPixmap, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> None: ...
    def isDetached(self) -> bool: ...
    def isNull(self) -> bool: ...
    @typing.overload
    def paint(self, painter: typing.Optional['QPainter'], rect: QtCore.QRect, alignment: QtCore.Qt.AlignmentFlag = ..., mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> None: ...
    @typing.overload
    def paint(self, painter: typing.Optional['QPainter'], x: int, y: int, w: int, h: int, alignment: QtCore.Qt.AlignmentFlag = ..., mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> None: ...
    def availableSizes(self, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> list[QtCore.QSize]: ...
    def actualSize(self, size: QtCore.QSize, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> QtCore.QSize: ...
    @typing.overload
    def pixmap(self, size: QtCore.QSize, devicePixelRatio: float, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> QPixmap: ...
    @typing.overload
    def pixmap(self, size: QtCore.QSize, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> QPixmap: ...
    @typing.overload
    def pixmap(self, w: int, h: int, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> QPixmap: ...
    @typing.overload
    def pixmap(self, extent: int, mode: 'QIcon.Mode' = ..., state: 'QIcon.State' = ...) -> QPixmap: ...


class QIconEngine(PyQt6.sip.wrapper):

    class IconEngineHook(enum.Enum):
        IsNullHook = ... # type: QIconEngine.IconEngineHook
        ScaledPixmapHook = ... # type: QIconEngine.IconEngineHook

    class ScaledPixmapArgument(PyQt6.sip.simplewrapper):

        mode = ... # type: QIcon.Mode
        pixmap = ... # type: QPixmap
        scale = ... # type: float
        size = ... # type: QtCore.QSize
        state = ... # type: QIcon.State

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QIconEngine.ScaledPixmapArgument') -> None: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QIconEngine') -> None: ...

    def scaledPixmap(self, size: QtCore.QSize, mode: QIcon.Mode, state: QIcon.State, scale: float) -> QPixmap: ...
    def isNull(self) -> bool: ...
    def iconName(self) -> str: ...
    def availableSizes(self, mode: QIcon.Mode = ..., state: QIcon.State = ...) -> list[QtCore.QSize]: ...
    def write(self, out: QtCore.QDataStream) -> bool: ...
    def read(self, in_: QtCore.QDataStream) -> bool: ...
    def clone(self) -> typing.Optional['QIconEngine']: ...
    def key(self) -> str: ...
    def addFile(self, fileName: typing.Optional[str], size: QtCore.QSize, mode: QIcon.Mode, state: QIcon.State) -> None: ...
    def addPixmap(self, pixmap: QPixmap, mode: QIcon.Mode, state: QIcon.State) -> None: ...
    def pixmap(self, size: QtCore.QSize, mode: QIcon.Mode, state: QIcon.State) -> QPixmap: ...
    def actualSize(self, size: QtCore.QSize, mode: QIcon.Mode, state: QIcon.State) -> QtCore.QSize: ...
    def paint(self, painter: typing.Optional['QPainter'], rect: QtCore.QRect, mode: QIcon.Mode, state: QIcon.State) -> None: ...


class QImage(QPaintDevice):

    class Format(enum.Enum):
        Format_Invalid = ... # type: QImage.Format
        Format_Mono = ... # type: QImage.Format
        Format_MonoLSB = ... # type: QImage.Format
        Format_Indexed8 = ... # type: QImage.Format
        Format_RGB32 = ... # type: QImage.Format
        Format_ARGB32 = ... # type: QImage.Format
        Format_ARGB32_Premultiplied = ... # type: QImage.Format
        Format_RGB16 = ... # type: QImage.Format
        Format_ARGB8565_Premultiplied = ... # type: QImage.Format
        Format_RGB666 = ... # type: QImage.Format
        Format_ARGB6666_Premultiplied = ... # type: QImage.Format
        Format_RGB555 = ... # type: QImage.Format
        Format_ARGB8555_Premultiplied = ... # type: QImage.Format
        Format_RGB888 = ... # type: QImage.Format
        Format_RGB444 = ... # type: QImage.Format
        Format_ARGB4444_Premultiplied = ... # type: QImage.Format
        Format_RGBX8888 = ... # type: QImage.Format
        Format_RGBA8888 = ... # type: QImage.Format
        Format_RGBA8888_Premultiplied = ... # type: QImage.Format
        Format_BGR30 = ... # type: QImage.Format
        Format_A2BGR30_Premultiplied = ... # type: QImage.Format
        Format_RGB30 = ... # type: QImage.Format
        Format_A2RGB30_Premultiplied = ... # type: QImage.Format
        Format_Alpha8 = ... # type: QImage.Format
        Format_Grayscale8 = ... # type: QImage.Format
        Format_RGBX64 = ... # type: QImage.Format
        Format_RGBA64 = ... # type: QImage.Format
        Format_RGBA64_Premultiplied = ... # type: QImage.Format
        Format_Grayscale16 = ... # type: QImage.Format
        Format_BGR888 = ... # type: QImage.Format
        Format_RGBX16FPx4 = ... # type: QImage.Format
        Format_RGBA16FPx4 = ... # type: QImage.Format
        Format_RGBA16FPx4_Premultiplied = ... # type: QImage.Format
        Format_RGBX32FPx4 = ... # type: QImage.Format
        Format_RGBA32FPx4 = ... # type: QImage.Format
        Format_RGBA32FPx4_Premultiplied = ... # type: QImage.Format
        Format_CMYK8888 = ... # type: QImage.Format

    class InvertMode(enum.Enum):
        InvertRgb = ... # type: QImage.InvertMode
        InvertRgba = ... # type: QImage.InvertMode

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, size: QtCore.QSize, format: 'QImage.Format') -> None: ...
    @typing.overload
    def __init__(self, width: int, height: int, format: 'QImage.Format') -> None: ...
    @typing.overload
    def __init__(self, data: typing.Optional[bytes], width: int, height: int, format: 'QImage.Format', cleanupFunction: typing.Optional[typing.Callable[..., None]] = ..., cleanupInfo: typing.Optional[typing.Any] = ...) -> None: ...
    @typing.overload
    def __init__(self, data: typing.Optional[bytes], width: int, height: int, bytesPerLine: int, format: 'QImage.Format', cleanupFunction: typing.Optional[typing.Callable[..., None]] = ..., cleanupInfo: typing.Optional[typing.Any] = ...) -> None: ...
    @typing.overload
    def __init__(self, xpm: list[str]) -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str], format: typing.Optional[str] = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QImage') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def flip(self, orient: QtCore.Qt.Orientation = ...) -> None: ...
    def flipped(self, orient: QtCore.Qt.Orientation = ...) -> 'QImage': ...
    @typing.overload
    def colorTransformed(self, transform: QColorTransform) -> 'QImage': ...
    @typing.overload
    def colorTransformed(self, transform: QColorTransform, format: 'QImage.Format', flags: QtCore.Qt.ImageConversionFlag = ...) -> 'QImage': ...
    def deviceIndependentSize(self) -> QtCore.QSizeF: ...
    @typing.overload
    def applyColorTransform(self, transform: QColorTransform) -> None: ...
    @typing.overload
    def applyColorTransform(self, transform: QColorTransform, format: 'QImage.Format', flags: QtCore.Qt.ImageConversionFlag = ...) -> None: ...
    @typing.overload
    def convertToColorSpace(self, a0: QColorSpace) -> None: ...
    @typing.overload
    def convertToColorSpace(self, colorSpace: QColorSpace, format: 'QImage.Format', flags: QtCore.Qt.ImageConversionFlag = ...) -> None: ...
    @typing.overload
    def convertedToColorSpace(self, a0: QColorSpace) -> 'QImage': ...
    @typing.overload
    def convertedToColorSpace(self, colorSpace: QColorSpace, format: 'QImage.Format', flags: QtCore.Qt.ImageConversionFlag = ...) -> 'QImage': ...
    def setColorSpace(self, a0: QColorSpace) -> None: ...
    def colorSpace(self) -> QColorSpace: ...
    def convertTo(self, f: 'QImage.Format', flags: QtCore.Qt.ImageConversionFlag = ...) -> None: ...
    def convertedTo(self, f: 'QImage.Format', flags: QtCore.Qt.ImageConversionFlag = ...) -> 'QImage': ...
    def sizeInBytes(self) -> int: ...
    def reinterpretAsFormat(self, f: 'QImage.Format') -> bool: ...
    @typing.overload
    def setPixelColor(self, x: int, y: int, c: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def setPixelColor(self, pt: QtCore.QPoint, c: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def pixelColor(self, x: int, y: int) -> QColor: ...
    @typing.overload
    def pixelColor(self, pt: QtCore.QPoint) -> QColor: ...
    @staticmethod
    def toImageFormat(format: 'QPixelFormat') -> 'QImage.Format': ...
    @staticmethod
    def toPixelFormat(format: 'QImage.Format') -> 'QPixelFormat': ...
    def pixelFormat(self) -> 'QPixelFormat': ...
    def setDevicePixelRatio(self, scaleFactor: float) -> None: ...
    def devicePixelRatio(self) -> float: ...
    def swap(self, other: 'QImage') -> None: ...
    def bitPlaneCount(self) -> int: ...
    def setColorCount(self, a0: int) -> None: ...
    def colorCount(self) -> int: ...
    def cacheKey(self) -> int: ...
    @staticmethod
    def trueMatrix(a0: 'QTransform', w: int, h: int) -> 'QTransform': ...
    def transformed(self, matrix: 'QTransform', mode: QtCore.Qt.TransformationMode = ...) -> 'QImage': ...
    def createMaskFromColor(self, color: int, mode: QtCore.Qt.MaskMode = ...) -> 'QImage': ...
    def setText(self, key: typing.Optional[str], value: typing.Optional[str]) -> None: ...
    def text(self, key: typing.Optional[str] = ...) -> str: ...
    def textKeys(self) -> list[str]: ...
    def setOffset(self, a0: QtCore.QPoint) -> None: ...
    def offset(self) -> QtCore.QPoint: ...
    def setDotsPerMeterY(self, a0: int) -> None: ...
    def setDotsPerMeterX(self, a0: int) -> None: ...
    def dotsPerMeterY(self) -> int: ...
    def dotsPerMeterX(self) -> int: ...
    def paintEngine(self) -> typing.Optional['QPaintEngine']: ...
    @typing.overload
    @staticmethod
    def fromData(data: typing.Optional[PyQt6.sip.array[bytes]], format: typing.Optional[str] = ...) -> 'QImage': ...
    @typing.overload
    @staticmethod
    def fromData(data: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], format: typing.Optional[str] = ...) -> 'QImage': ...
    @typing.overload
    def save(self, fileName: typing.Optional[str], format: typing.Optional[str] = ..., quality: int = ...) -> bool: ...
    @typing.overload
    def save(self, device: typing.Optional[QtCore.QIODevice], format: typing.Optional[str] = ..., quality: int = ...) -> bool: ...
    @typing.overload
    def loadFromData(self, data: typing.Optional[PyQt6.sip.array[bytes]], format: typing.Optional[str] = ...) -> bool: ...
    @typing.overload
    def loadFromData(self, data: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], format: typing.Optional[str] = ...) -> bool: ...
    @typing.overload
    def load(self, device: typing.Optional[QtCore.QIODevice], format: typing.Optional[str]) -> bool: ...
    @typing.overload
    def load(self, fileName: typing.Optional[str], format: typing.Optional[str] = ...) -> bool: ...
    def invertPixels(self, mode: 'QImage.InvertMode' = ...) -> None: ...
    def rgbSwap(self) -> None: ...
    def rgbSwapped(self) -> 'QImage': ...
    def mirror(self, horizontal: bool = ..., vertical: bool = ...) -> None: ...
    def mirrored(self, horizontal: bool = ..., vertical: bool = ...) -> 'QImage': ...
    def scaledToHeight(self, height: int, mode: QtCore.Qt.TransformationMode = ...) -> 'QImage': ...
    def scaledToWidth(self, width: int, mode: QtCore.Qt.TransformationMode = ...) -> 'QImage': ...
    @typing.overload
    def scaled(self, width: int, height: int, aspectRatioMode: QtCore.Qt.AspectRatioMode = ..., transformMode: QtCore.Qt.TransformationMode = ...) -> 'QImage': ...
    @typing.overload
    def scaled(self, size: QtCore.QSize, aspectRatioMode: QtCore.Qt.AspectRatioMode = ..., transformMode: QtCore.Qt.TransformationMode = ...) -> 'QImage': ...
    def createHeuristicMask(self, clipTight: bool = ...) -> 'QImage': ...
    def createAlphaMask(self, flags: QtCore.Qt.ImageConversionFlag = ...) -> 'QImage': ...
    def setAlphaChannel(self, alphaChannel: 'QImage') -> None: ...
    def hasAlphaChannel(self) -> bool: ...
    @typing.overload
    def fill(self, pixel: int) -> None: ...
    @typing.overload
    def fill(self, color: QtCore.Qt.GlobalColor) -> None: ...
    @typing.overload
    def fill(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def setColorTable(self, colors: collections.abc.Iterable[int]) -> None: ...
    def colorTable(self) -> list[int]: ...
    @typing.overload
    def setPixel(self, pt: QtCore.QPoint, index_or_rgb: int) -> None: ...
    @typing.overload
    def setPixel(self, x: int, y: int, index_or_rgb: int) -> None: ...
    @typing.overload
    def pixel(self, pt: QtCore.QPoint) -> int: ...
    @typing.overload
    def pixel(self, x: int, y: int) -> int: ...
    @typing.overload
    def pixelIndex(self, pt: QtCore.QPoint) -> int: ...
    @typing.overload
    def pixelIndex(self, x: int, y: int) -> int: ...
    @typing.overload
    def valid(self, pt: QtCore.QPoint) -> bool: ...
    @typing.overload
    def valid(self, x: int, y: int) -> bool: ...
    def bytesPerLine(self) -> int: ...
    def constScanLine(self, a0: int) -> typing.Optional[PyQt6.sip.voidptr]: ...
    def scanLine(self, a0: int) -> typing.Optional[PyQt6.sip.voidptr]: ...
    def constBits(self) -> typing.Optional[PyQt6.sip.voidptr]: ...
    def bits(self) -> typing.Optional[PyQt6.sip.voidptr]: ...
    def isGrayscale(self) -> bool: ...
    def allGray(self) -> bool: ...
    def setColor(self, i: int, c: int) -> None: ...
    def color(self, i: int) -> int: ...
    def depth(self) -> int: ...
    def rect(self) -> QtCore.QRect: ...
    def size(self) -> QtCore.QSize: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    @staticmethod
    def fromHICON(hicon: typing.Optional[PyQt6.sip.voidptr]) -> 'QImage': ...
    def toHICON(self, mask: 'QImage' = ...) -> typing.Optional[PyQt6.sip.voidptr]: ...
    @staticmethod
    def fromHBITMAP(hbitmap: typing.Optional[PyQt6.sip.voidptr]) -> 'QImage': ...
    def toHBITMAP(self) -> typing.Optional[PyQt6.sip.voidptr]: ...
    @typing.overload
    def convertToFormat(self, f: 'QImage.Format', flags: QtCore.Qt.ImageConversionFlag = ...) -> 'QImage': ...
    @typing.overload
    def convertToFormat(self, f: 'QImage.Format', colorTable: collections.abc.Iterable[int], flags: QtCore.Qt.ImageConversionFlag = ...) -> 'QImage': ...
    def format(self) -> 'QImage.Format': ...
    @typing.overload
    def copy(self, rect: QtCore.QRect = ...) -> 'QImage': ...
    @typing.overload
    def copy(self, x: int, y: int, w: int, h: int) -> 'QImage': ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isNull(self) -> bool: ...


class QImageIOHandler(PyQt6.sip.simplewrapper):

    class Transformation(enum.Flag):
        TransformationNone = ... # type: QImageIOHandler.Transformation
        TransformationMirror = ... # type: QImageIOHandler.Transformation
        TransformationFlip = ... # type: QImageIOHandler.Transformation
        TransformationRotate180 = ... # type: QImageIOHandler.Transformation
        TransformationRotate90 = ... # type: QImageIOHandler.Transformation
        TransformationMirrorAndRotate90 = ... # type: QImageIOHandler.Transformation
        TransformationFlipAndRotate90 = ... # type: QImageIOHandler.Transformation
        TransformationRotate270 = ... # type: QImageIOHandler.Transformation

    class ImageOption(enum.Enum):
        Size = ... # type: QImageIOHandler.ImageOption
        ClipRect = ... # type: QImageIOHandler.ImageOption
        Description = ... # type: QImageIOHandler.ImageOption
        ScaledClipRect = ... # type: QImageIOHandler.ImageOption
        ScaledSize = ... # type: QImageIOHandler.ImageOption
        CompressionRatio = ... # type: QImageIOHandler.ImageOption
        Gamma = ... # type: QImageIOHandler.ImageOption
        Quality = ... # type: QImageIOHandler.ImageOption
        Name = ... # type: QImageIOHandler.ImageOption
        SubType = ... # type: QImageIOHandler.ImageOption
        IncrementalReading = ... # type: QImageIOHandler.ImageOption
        Endianness = ... # type: QImageIOHandler.ImageOption
        Animation = ... # type: QImageIOHandler.ImageOption
        BackgroundColor = ... # type: QImageIOHandler.ImageOption
        SupportedSubTypes = ... # type: QImageIOHandler.ImageOption
        OptimizedWrite = ... # type: QImageIOHandler.ImageOption
        ProgressiveScanWrite = ... # type: QImageIOHandler.ImageOption
        ImageTransformation = ... # type: QImageIOHandler.ImageOption

    def __init__(self) -> None: ...

    def currentImageRect(self) -> QtCore.QRect: ...
    def currentImageNumber(self) -> int: ...
    def nextImageDelay(self) -> int: ...
    def imageCount(self) -> int: ...
    def loopCount(self) -> int: ...
    def jumpToImage(self, imageNumber: int) -> bool: ...
    def jumpToNextImage(self) -> bool: ...
    def supportsOption(self, option: 'QImageIOHandler.ImageOption') -> bool: ...
    def setOption(self, option: 'QImageIOHandler.ImageOption', value: typing.Any) -> None: ...
    def option(self, option: 'QImageIOHandler.ImageOption') -> typing.Any: ...
    def write(self, image: QImage) -> bool: ...
    def read(self, image: typing.Optional[QImage]) -> bool: ...
    def canRead(self) -> bool: ...
    def format(self) -> QtCore.QByteArray: ...
    def setFormat(self, format: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def device(self) -> typing.Optional[QtCore.QIODevice]: ...
    def setDevice(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...


class QImageReader(PyQt6.sip.simplewrapper):

    class ImageReaderError(enum.Enum):
        UnknownError = ... # type: QImageReader.ImageReaderError
        FileNotFoundError = ... # type: QImageReader.ImageReaderError
        DeviceError = ... # type: QImageReader.ImageReaderError
        UnsupportedFormatError = ... # type: QImageReader.ImageReaderError
        InvalidDataError = ... # type: QImageReader.ImageReaderError

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, device: typing.Optional[QtCore.QIODevice], format: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ...) -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str], format: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ...) -> None: ...

    @staticmethod
    def setAllocationLimit(mbLimit: int) -> None: ...
    @staticmethod
    def allocationLimit() -> int: ...
    @staticmethod
    def imageFormatsForMimeType(mimeType: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> list[QtCore.QByteArray]: ...
    def autoTransform(self) -> bool: ...
    def setAutoTransform(self, enabled: bool) -> None: ...
    def transformation(self) -> QImageIOHandler.Transformation: ...
    def supportedSubTypes(self) -> list[QtCore.QByteArray]: ...
    def subType(self) -> QtCore.QByteArray: ...
    @staticmethod
    def supportedMimeTypes() -> list[QtCore.QByteArray]: ...
    def decideFormatFromContent(self) -> bool: ...
    def setDecideFormatFromContent(self, ignored: bool) -> None: ...
    def autoDetectImageFormat(self) -> bool: ...
    def setAutoDetectImageFormat(self, enabled: bool) -> None: ...
    def supportsOption(self, option: QImageIOHandler.ImageOption) -> bool: ...
    def quality(self) -> int: ...
    def setQuality(self, quality: int) -> None: ...
    def supportsAnimation(self) -> bool: ...
    def backgroundColor(self) -> QColor: ...
    def setBackgroundColor(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def text(self, key: typing.Optional[str]) -> str: ...
    def textKeys(self) -> list[str]: ...
    @staticmethod
    def supportedImageFormats() -> list[QtCore.QByteArray]: ...
    @typing.overload
    @staticmethod
    def imageFormat(fileName: typing.Optional[str]) -> QtCore.QByteArray: ...
    @typing.overload
    @staticmethod
    def imageFormat(device: typing.Optional[QtCore.QIODevice]) -> QtCore.QByteArray: ...
    @typing.overload
    def imageFormat(self) -> QImage.Format: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QImageReader.ImageReaderError': ...
    def currentImageRect(self) -> QtCore.QRect: ...
    def currentImageNumber(self) -> int: ...
    def nextImageDelay(self) -> int: ...
    def imageCount(self) -> int: ...
    def loopCount(self) -> int: ...
    def jumpToImage(self, imageNumber: int) -> bool: ...
    def jumpToNextImage(self) -> bool: ...
    @typing.overload
    def read(self) -> QImage: ...
    @typing.overload
    def read(self, image: typing.Optional[QImage]) -> bool: ...
    def canRead(self) -> bool: ...
    def scaledClipRect(self) -> QtCore.QRect: ...
    def setScaledClipRect(self, rect: QtCore.QRect) -> None: ...
    def scaledSize(self) -> QtCore.QSize: ...
    def setScaledSize(self, size: QtCore.QSize) -> None: ...
    def clipRect(self) -> QtCore.QRect: ...
    def setClipRect(self, rect: QtCore.QRect) -> None: ...
    def size(self) -> QtCore.QSize: ...
    def fileName(self) -> str: ...
    def setFileName(self, fileName: typing.Optional[str]) -> None: ...
    def device(self) -> typing.Optional[QtCore.QIODevice]: ...
    def setDevice(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...
    def format(self) -> QtCore.QByteArray: ...
    def setFormat(self, format: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...


class QImageWriter(PyQt6.sip.simplewrapper):

    class ImageWriterError(enum.Enum):
        UnknownError = ... # type: QImageWriter.ImageWriterError
        DeviceError = ... # type: QImageWriter.ImageWriterError
        UnsupportedFormatError = ... # type: QImageWriter.ImageWriterError
        InvalidImageError = ... # type: QImageWriter.ImageWriterError

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, device: typing.Optional[QtCore.QIODevice], format: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str], format: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ...) -> None: ...

    @staticmethod
    def imageFormatsForMimeType(mimeType: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> list[QtCore.QByteArray]: ...
    def setTransformation(self, orientation: QImageIOHandler.Transformation) -> None: ...
    def transformation(self) -> QImageIOHandler.Transformation: ...
    def progressiveScanWrite(self) -> bool: ...
    def setProgressiveScanWrite(self, progressive: bool) -> None: ...
    def optimizedWrite(self) -> bool: ...
    def setOptimizedWrite(self, optimize: bool) -> None: ...
    def supportedSubTypes(self) -> list[QtCore.QByteArray]: ...
    def subType(self) -> QtCore.QByteArray: ...
    def setSubType(self, type: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    @staticmethod
    def supportedMimeTypes() -> list[QtCore.QByteArray]: ...
    def compression(self) -> int: ...
    def setCompression(self, compression: int) -> None: ...
    def supportsOption(self, option: QImageIOHandler.ImageOption) -> bool: ...
    def setText(self, key: typing.Optional[str], text: typing.Optional[str]) -> None: ...
    @staticmethod
    def supportedImageFormats() -> list[QtCore.QByteArray]: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QImageWriter.ImageWriterError': ...
    def write(self, image: QImage) -> bool: ...
    def canWrite(self) -> bool: ...
    def quality(self) -> int: ...
    def setQuality(self, quality: int) -> None: ...
    def fileName(self) -> str: ...
    def setFileName(self, fileName: typing.Optional[str]) -> None: ...
    def device(self) -> typing.Optional[QtCore.QIODevice]: ...
    def setDevice(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...
    def format(self) -> QtCore.QByteArray: ...
    def setFormat(self, format: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...


class QInputDevice(QtCore.QObject):

    class Capability(enum.Flag):
        None_ = ... # type: QInputDevice.Capability
        Position = ... # type: QInputDevice.Capability
        Area = ... # type: QInputDevice.Capability
        Pressure = ... # type: QInputDevice.Capability
        Velocity = ... # type: QInputDevice.Capability
        NormalizedPosition = ... # type: QInputDevice.Capability
        MouseEmulation = ... # type: QInputDevice.Capability
        PixelScroll = ... # type: QInputDevice.Capability
        Scroll = ... # type: QInputDevice.Capability
        Hover = ... # type: QInputDevice.Capability
        Rotation = ... # type: QInputDevice.Capability
        XTilt = ... # type: QInputDevice.Capability
        YTilt = ... # type: QInputDevice.Capability
        TangentialPressure = ... # type: QInputDevice.Capability
        ZPosition = ... # type: QInputDevice.Capability
        All = ... # type: QInputDevice.Capability

    class DeviceType(enum.Flag):
        Unknown = ... # type: QInputDevice.DeviceType
        Mouse = ... # type: QInputDevice.DeviceType
        TouchScreen = ... # type: QInputDevice.DeviceType
        TouchPad = ... # type: QInputDevice.DeviceType
        Puck = ... # type: QInputDevice.DeviceType
        Stylus = ... # type: QInputDevice.DeviceType
        Airbrush = ... # type: QInputDevice.DeviceType
        Keyboard = ... # type: QInputDevice.DeviceType
        AllDevices = ... # type: QInputDevice.DeviceType

    @typing.overload
    def __init__(self, name: typing.Optional[str], systemId: int, type: 'QInputDevice.DeviceType', seatName: typing.Optional[str] = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def __ne__(self, other: object): ...
    @staticmethod
    def seatNames() -> list[str]: ...
    capabilitiesChanged: typing.ClassVar[QtCore.pyqtSignal]
    availableVirtualGeometryChanged: typing.ClassVar[QtCore.pyqtSignal]
    def __eq__(self, other: object): ...
    @staticmethod
    def primaryKeyboard(seatName: typing.Optional[str] = ...) -> typing.Optional['QInputDevice']: ...
    @staticmethod
    def devices() -> list['QInputDevice']: ...
    def availableVirtualGeometry(self) -> QtCore.QRect: ...
    def seatName(self) -> str: ...
    def systemId(self) -> int: ...
    def hasCapability(self, cap: 'QInputDevice.Capability') -> bool: ...
    def capabilities(self) -> 'QInputDevice.Capability': ...
    def type(self) -> 'QInputDevice.DeviceType': ...
    def name(self) -> str: ...


class QInputMethod(QtCore.QObject):

    class Action(enum.Enum):
        Click = ... # type: QInputMethod.Action
        ContextMenu = ... # type: QInputMethod.Action

    inputItemClipRectangleChanged: typing.ClassVar[QtCore.pyqtSignal]
    anchorRectangleChanged: typing.ClassVar[QtCore.pyqtSignal]
    def inputItemClipRectangle(self) -> QtCore.QRectF: ...
    def anchorRectangle(self) -> QtCore.QRectF: ...
    inputDirectionChanged: typing.ClassVar[QtCore.pyqtSignal]
    localeChanged: typing.ClassVar[QtCore.pyqtSignal]
    animatingChanged: typing.ClassVar[QtCore.pyqtSignal]
    visibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    keyboardRectangleChanged: typing.ClassVar[QtCore.pyqtSignal]
    cursorRectangleChanged: typing.ClassVar[QtCore.pyqtSignal]
    def invokeAction(self, a: 'QInputMethod.Action', cursorPosition: int) -> None: ...
    def commit(self) -> None: ...
    def reset(self) -> None: ...
    def update(self, queries: QtCore.Qt.InputMethodQuery) -> None: ...
    def hide(self) -> None: ...
    def show(self) -> None: ...
    @staticmethod
    def queryFocusObject(query: QtCore.Qt.InputMethodQuery, argument: typing.Any) -> typing.Any: ...
    def setInputItemRectangle(self, rect: QtCore.QRectF) -> None: ...
    def inputItemRectangle(self) -> QtCore.QRectF: ...
    def inputDirection(self) -> QtCore.Qt.LayoutDirection: ...
    def locale(self) -> QtCore.QLocale: ...
    def isAnimating(self) -> bool: ...
    def setVisible(self, visible: bool) -> None: ...
    def isVisible(self) -> bool: ...
    def keyboardRectangle(self) -> QtCore.QRectF: ...
    def cursorRectangle(self) -> QtCore.QRectF: ...
    def setInputItemTransform(self, transform: 'QTransform') -> None: ...
    def inputItemTransform(self) -> 'QTransform': ...


class QKeySequence(PyQt6.sip.simplewrapper):

    class StandardKey(enum.Enum):
        UnknownKey = ... # type: QKeySequence.StandardKey
        HelpContents = ... # type: QKeySequence.StandardKey
        WhatsThis = ... # type: QKeySequence.StandardKey
        Open = ... # type: QKeySequence.StandardKey
        Close = ... # type: QKeySequence.StandardKey
        Save = ... # type: QKeySequence.StandardKey
        New = ... # type: QKeySequence.StandardKey
        Delete = ... # type: QKeySequence.StandardKey
        Cut = ... # type: QKeySequence.StandardKey
        Copy = ... # type: QKeySequence.StandardKey
        Paste = ... # type: QKeySequence.StandardKey
        Undo = ... # type: QKeySequence.StandardKey
        Redo = ... # type: QKeySequence.StandardKey
        Back = ... # type: QKeySequence.StandardKey
        Forward = ... # type: QKeySequence.StandardKey
        Refresh = ... # type: QKeySequence.StandardKey
        ZoomIn = ... # type: QKeySequence.StandardKey
        ZoomOut = ... # type: QKeySequence.StandardKey
        Print = ... # type: QKeySequence.StandardKey
        AddTab = ... # type: QKeySequence.StandardKey
        NextChild = ... # type: QKeySequence.StandardKey
        PreviousChild = ... # type: QKeySequence.StandardKey
        Find = ... # type: QKeySequence.StandardKey
        FindNext = ... # type: QKeySequence.StandardKey
        FindPrevious = ... # type: QKeySequence.StandardKey
        Replace = ... # type: QKeySequence.StandardKey
        SelectAll = ... # type: QKeySequence.StandardKey
        Bold = ... # type: QKeySequence.StandardKey
        Italic = ... # type: QKeySequence.StandardKey
        Underline = ... # type: QKeySequence.StandardKey
        MoveToNextChar = ... # type: QKeySequence.StandardKey
        MoveToPreviousChar = ... # type: QKeySequence.StandardKey
        MoveToNextWord = ... # type: QKeySequence.StandardKey
        MoveToPreviousWord = ... # type: QKeySequence.StandardKey
        MoveToNextLine = ... # type: QKeySequence.StandardKey
        MoveToPreviousLine = ... # type: QKeySequence.StandardKey
        MoveToNextPage = ... # type: QKeySequence.StandardKey
        MoveToPreviousPage = ... # type: QKeySequence.StandardKey
        MoveToStartOfLine = ... # type: QKeySequence.StandardKey
        MoveToEndOfLine = ... # type: QKeySequence.StandardKey
        MoveToStartOfBlock = ... # type: QKeySequence.StandardKey
        MoveToEndOfBlock = ... # type: QKeySequence.StandardKey
        MoveToStartOfDocument = ... # type: QKeySequence.StandardKey
        MoveToEndOfDocument = ... # type: QKeySequence.StandardKey
        SelectNextChar = ... # type: QKeySequence.StandardKey
        SelectPreviousChar = ... # type: QKeySequence.StandardKey
        SelectNextWord = ... # type: QKeySequence.StandardKey
        SelectPreviousWord = ... # type: QKeySequence.StandardKey
        SelectNextLine = ... # type: QKeySequence.StandardKey
        SelectPreviousLine = ... # type: QKeySequence.StandardKey
        SelectNextPage = ... # type: QKeySequence.StandardKey
        SelectPreviousPage = ... # type: QKeySequence.StandardKey
        SelectStartOfLine = ... # type: QKeySequence.StandardKey
        SelectEndOfLine = ... # type: QKeySequence.StandardKey
        SelectStartOfBlock = ... # type: QKeySequence.StandardKey
        SelectEndOfBlock = ... # type: QKeySequence.StandardKey
        SelectStartOfDocument = ... # type: QKeySequence.StandardKey
        SelectEndOfDocument = ... # type: QKeySequence.StandardKey
        DeleteStartOfWord = ... # type: QKeySequence.StandardKey
        DeleteEndOfWord = ... # type: QKeySequence.StandardKey
        DeleteEndOfLine = ... # type: QKeySequence.StandardKey
        InsertParagraphSeparator = ... # type: QKeySequence.StandardKey
        InsertLineSeparator = ... # type: QKeySequence.StandardKey
        SaveAs = ... # type: QKeySequence.StandardKey
        Preferences = ... # type: QKeySequence.StandardKey
        Quit = ... # type: QKeySequence.StandardKey
        FullScreen = ... # type: QKeySequence.StandardKey
        Deselect = ... # type: QKeySequence.StandardKey
        DeleteCompleteLine = ... # type: QKeySequence.StandardKey
        Backspace = ... # type: QKeySequence.StandardKey
        Cancel = ... # type: QKeySequence.StandardKey

    class SequenceMatch(enum.Enum):
        NoMatch = ... # type: QKeySequence.SequenceMatch
        PartialMatch = ... # type: QKeySequence.SequenceMatch
        ExactMatch = ... # type: QKeySequence.SequenceMatch

    class SequenceFormat(enum.Enum):
        NativeText = ... # type: QKeySequence.SequenceFormat
        PortableText = ... # type: QKeySequence.SequenceFormat

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, ks: 'QKeySequence') -> None: ...
    @typing.overload
    def __init__(self, key: 'QKeySequence.StandardKey') -> None: ...
    @typing.overload
    def __init__(self, key: typing.Optional[str], format: 'QKeySequence.SequenceFormat' = ...) -> None: ...
    @typing.overload
    def __init__(self, k1: int, key2: int = ..., key3: int = ..., key4: int = ...) -> None: ...
    @typing.overload
    def __init__(self, k1: QtCore.QKeyCombination, key2: QtCore.QKeyCombination = ..., key3: QtCore.QKeyCombination = ..., key4: QtCore.QKeyCombination = ...) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def __hash__(self) -> int: ...
    @staticmethod
    def listToString(list: collections.abc.Iterable[typing.Union['QKeySequence', 'QKeySequence.StandardKey', typing.Optional[str], int]], format: 'QKeySequence.SequenceFormat' = ...) -> str: ...
    @staticmethod
    def listFromString(str: typing.Optional[str], format: 'QKeySequence.SequenceFormat' = ...) -> list['QKeySequence']: ...
    @staticmethod
    def keyBindings(key: 'QKeySequence.StandardKey') -> list['QKeySequence']: ...
    @staticmethod
    def fromString(str: typing.Optional[str], format: 'QKeySequence.SequenceFormat' = ...) -> 'QKeySequence': ...
    def toString(self, format: 'QKeySequence.SequenceFormat' = ...) -> str: ...
    def swap(self, other: 'QKeySequence') -> None: ...
    def isDetached(self) -> bool: ...
    def __ge__(self, other: typing.Union['QKeySequence', 'QKeySequence.StandardKey', typing.Optional[str], int]) -> bool: ...
    def __le__(self, other: typing.Union['QKeySequence', 'QKeySequence.StandardKey', typing.Optional[str], int]) -> bool: ...
    def __gt__(self, other: typing.Union['QKeySequence', 'QKeySequence.StandardKey', typing.Optional[str], int]) -> bool: ...
    def __lt__(self, ks: typing.Union['QKeySequence', 'QKeySequence.StandardKey', typing.Optional[str], int]) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __getitem__(self, i: int) -> QtCore.QKeyCombination: ...
    @staticmethod
    def mnemonic(text: typing.Optional[str]) -> 'QKeySequence': ...
    def matches(self, seq: typing.Union['QKeySequence', 'QKeySequence.StandardKey', typing.Optional[str], int]) -> 'QKeySequence.SequenceMatch': ...
    def isEmpty(self) -> bool: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...


class QMatrix4x4(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, values: collections.abc.Sequence[float]) -> None: ...
    @typing.overload
    def __init__(self, m11: float, m12: float, m13: float, m14: float, m21: float, m22: float, m23: float, m24: float, m31: float, m32: float, m33: float, m34: float, m41: float, m42: float, m43: float, m44: float) -> None: ...
    @typing.overload
    def __init__(self, transform: 'QTransform') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMatrix4x4') -> None: ...

    def __truediv__(self, divisor: float) -> 'QMatrix4x4': ...
    def __add__(self, m2: 'QMatrix4x4') -> 'QMatrix4x4': ...
    def __sub__(self, m2: 'QMatrix4x4') -> 'QMatrix4x4': ...
    @typing.overload
    def __mul__(self, m2: 'QMatrix4x4') -> 'QMatrix4x4': ...
    @typing.overload
    def __mul__(self, point: QtCore.QPoint) -> QtCore.QPoint: ...
    @typing.overload
    def __mul__(self, point: QtCore.QPointF) -> QtCore.QPointF: ...
    @typing.overload
    def __mul__(self, factor: float) -> 'QMatrix4x4': ...
    @typing.overload
    def __mul__(self, vector: 'QVector4D') -> 'QVector4D': ...
    @typing.overload
    def __mul__(self, vector: 'QVector3D') -> 'QVector3D': ...
    def __rmul__(self, factor: float) -> 'QMatrix4x4': ...
    def __matmul__(self, m2: 'QMatrix4x4') -> 'QMatrix4x4': ...
    def __neg__(self) -> 'QMatrix4x4': ...
    def isAffine(self) -> bool: ...
    @typing.overload
    def viewport(self, left: float, bottom: float, width: float, height: float, nearPlane: float = ..., farPlane: float = ...) -> None: ...
    @typing.overload
    def viewport(self, rect: QtCore.QRectF) -> None: ...
    def mapVector(self, vector: 'QVector3D') -> 'QVector3D': ...
    @typing.overload
    def map(self, point: QtCore.QPoint) -> QtCore.QPoint: ...
    @typing.overload
    def map(self, point: QtCore.QPointF) -> QtCore.QPointF: ...
    @typing.overload
    def map(self, point: 'QVector3D') -> 'QVector3D': ...
    @typing.overload
    def map(self, point: 'QVector4D') -> 'QVector4D': ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __itruediv__(self, divisor: float) -> 'QMatrix4x4': ...
    def __imatmul__(self, other: 'QMatrix4x4') -> 'QMatrix4x4': ...
    @typing.overload
    def __imul__(self, other: 'QMatrix4x4') -> 'QMatrix4x4': ...
    @typing.overload
    def __imul__(self, factor: float) -> 'QMatrix4x4': ...
    def __isub__(self, other: 'QMatrix4x4') -> 'QMatrix4x4': ...
    def __iadd__(self, other: 'QMatrix4x4') -> 'QMatrix4x4': ...
    def fill(self, value: float) -> None: ...
    def setToIdentity(self) -> None: ...
    def isIdentity(self) -> bool: ...
    def setRow(self, index: int, value: 'QVector4D') -> None: ...
    def row(self, index: int) -> 'QVector4D': ...
    def setColumn(self, index: int, value: 'QVector4D') -> None: ...
    def column(self, index: int) -> 'QVector4D': ...
    def __setitem__(self, a0: typing.Any, a1: float) -> None: ...
    def __getitem__(self, a0: typing.Any) -> typing.Any: ...
    def optimize(self) -> None: ...
    def data(self) -> list[float]: ...
    @typing.overload
    def mapRect(self, rect: QtCore.QRect) -> QtCore.QRect: ...
    @typing.overload
    def mapRect(self, rect: QtCore.QRectF) -> QtCore.QRectF: ...
    @typing.overload
    def toTransform(self) -> 'QTransform': ...
    @typing.overload
    def toTransform(self, distanceToPlane: float) -> 'QTransform': ...
    def copyDataTo(self) -> list[float]: ...
    def lookAt(self, eye: 'QVector3D', center: 'QVector3D', up: 'QVector3D') -> None: ...
    def perspective(self, angle: float, aspect: float, nearPlane: float, farPlane: float) -> None: ...
    def frustum(self, left: float, right: float, bottom: float, top: float, nearPlane: float, farPlane: float) -> None: ...
    @typing.overload
    def ortho(self, rect: QtCore.QRect) -> None: ...
    @typing.overload
    def ortho(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def ortho(self, left: float, right: float, bottom: float, top: float, nearPlane: float, farPlane: float) -> None: ...
    @typing.overload
    def rotate(self, angle: float, vector: 'QVector3D') -> None: ...
    @typing.overload
    def rotate(self, angle: float, x: float, y: float, z: float = ...) -> None: ...
    @typing.overload
    def rotate(self, quaternion: 'QQuaternion') -> None: ...
    @typing.overload
    def translate(self, vector: 'QVector3D') -> None: ...
    @typing.overload
    def translate(self, x: float, y: float) -> None: ...
    @typing.overload
    def translate(self, x: float, y: float, z: float) -> None: ...
    @typing.overload
    def scale(self, vector: 'QVector3D') -> None: ...
    @typing.overload
    def scale(self, x: float, y: float) -> None: ...
    @typing.overload
    def scale(self, x: float, y: float, z: float) -> None: ...
    @typing.overload
    def scale(self, factor: float) -> None: ...
    def normalMatrix(self) -> QMatrix3x3: ...
    def transposed(self) -> 'QMatrix4x4': ...
    def inverted(self) -> typing.Tuple['QMatrix4x4', typing.Optional[bool]]: ...
    def determinant(self) -> float: ...
    def __repr__(self) -> str: ...


class QMovie(QtCore.QObject):

    class CacheMode(enum.Enum):
        CacheNone = ... # type: QMovie.CacheMode
        CacheAll = ... # type: QMovie.CacheMode

    class MovieState(enum.Enum):
        NotRunning = ... # type: QMovie.MovieState
        Paused = ... # type: QMovie.MovieState
        Running = ... # type: QMovie.MovieState

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, device: typing.Optional[QtCore.QIODevice], format: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str], format: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def lastErrorString(self) -> str: ...
    def lastError(self) -> QImageReader.ImageReaderError: ...
    def stop(self) -> None: ...
    def setPaused(self, paused: bool) -> None: ...
    def jumpToNextFrame(self) -> bool: ...
    def start(self) -> None: ...
    frameChanged: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    error: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    updated: typing.ClassVar[QtCore.pyqtSignal]
    resized: typing.ClassVar[QtCore.pyqtSignal]
    started: typing.ClassVar[QtCore.pyqtSignal]
    def setCacheMode(self, mode: 'QMovie.CacheMode') -> None: ...
    def cacheMode(self) -> 'QMovie.CacheMode': ...
    def setScaledSize(self, size: QtCore.QSize) -> None: ...
    def scaledSize(self) -> QtCore.QSize: ...
    def speed(self) -> int: ...
    def setSpeed(self, percentSpeed: int) -> None: ...
    def currentFrameNumber(self) -> int: ...
    def nextFrameDelay(self) -> int: ...
    def frameCount(self) -> int: ...
    def loopCount(self) -> int: ...
    def jumpToFrame(self, frameNumber: int) -> bool: ...
    def isValid(self) -> bool: ...
    def currentPixmap(self) -> QPixmap: ...
    def currentImage(self) -> QImage: ...
    def frameRect(self) -> QtCore.QRect: ...
    def state(self) -> 'QMovie.MovieState': ...
    def backgroundColor(self) -> QColor: ...
    def setBackgroundColor(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def format(self) -> QtCore.QByteArray: ...
    def setFormat(self, format: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def fileName(self) -> str: ...
    def setFileName(self, fileName: typing.Optional[str]) -> None: ...
    def device(self) -> typing.Optional[QtCore.QIODevice]: ...
    def setDevice(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...
    @staticmethod
    def supportedFormats() -> list[QtCore.QByteArray]: ...


class QSurface(PyQt6.sip.simplewrapper):

    class SurfaceType(enum.Enum):
        RasterSurface = ... # type: QSurface.SurfaceType
        OpenGLSurface = ... # type: QSurface.SurfaceType
        RasterGLSurface = ... # type: QSurface.SurfaceType
        OpenVGSurface = ... # type: QSurface.SurfaceType
        VulkanSurface = ... # type: QSurface.SurfaceType
        MetalSurface = ... # type: QSurface.SurfaceType
        Direct3DSurface = ... # type: QSurface.SurfaceType

    class SurfaceClass(enum.Enum):
        Window = ... # type: QSurface.SurfaceClass
        Offscreen = ... # type: QSurface.SurfaceClass

    @typing.overload
    def __init__(self, type: 'QSurface.SurfaceClass') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QSurface') -> None: ...

    def supportsOpenGL(self) -> bool: ...
    def size(self) -> QtCore.QSize: ...
    def surfaceType(self) -> 'QSurface.SurfaceType': ...
    def format(self) -> 'QSurfaceFormat': ...
    def surfaceClass(self) -> 'QSurface.SurfaceClass': ...


class QOffscreenSurface(QtCore.QObject, QSurface):

    def __init__(self, screen: typing.Optional['QScreen'] = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    screenChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setScreen(self, screen: typing.Optional['QScreen']) -> None: ...
    def screen(self) -> typing.Optional['QScreen']: ...
    def size(self) -> QtCore.QSize: ...
    def requestedFormat(self) -> 'QSurfaceFormat': ...
    def format(self) -> 'QSurfaceFormat': ...
    def setFormat(self, format: 'QSurfaceFormat') -> None: ...
    def isValid(self) -> bool: ...
    def destroy(self) -> None: ...
    def create(self) -> None: ...
    def surfaceType(self) -> QSurface.SurfaceType: ...


class QOpenGLContextGroup(QtCore.QObject):

    @staticmethod
    def currentContextGroup() -> typing.Optional['QOpenGLContextGroup']: ...
    def shares(self) -> list['QOpenGLContext']: ...


class QOpenGLContext(QtCore.QObject):

    class OpenGLModuleType(enum.Enum):
        LibGL = ... # type: QOpenGLContext.OpenGLModuleType
        LibGLES = ... # type: QOpenGLContext.OpenGLModuleType

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    @staticmethod
    def globalShareContext() -> typing.Optional['QOpenGLContext']: ...
    @staticmethod
    def supportsThreadedOpenGL() -> bool: ...
    def isOpenGLES(self) -> bool: ...
    @staticmethod
    def openGLModuleType() -> 'QOpenGLContext.OpenGLModuleType': ...
    aboutToBeDestroyed: typing.ClassVar[QtCore.pyqtSignal]
    def hasExtension(self, extension: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> bool: ...
    def extensions(self) -> set[QtCore.QByteArray]: ...
    @staticmethod
    def areSharing(first: typing.Optional['QOpenGLContext'], second: typing.Optional['QOpenGLContext']) -> bool: ...
    @staticmethod
    def currentContext() -> typing.Optional['QOpenGLContext']: ...
    def surface(self) -> typing.Optional[QSurface]: ...
    def getProcAddress(self, procName: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> typing.Optional[PyQt6.sip.voidptr]: ...
    def swapBuffers(self, surface: typing.Optional[QSurface]) -> None: ...
    def doneCurrent(self) -> None: ...
    def makeCurrent(self, surface: typing.Optional[QSurface]) -> bool: ...
    def defaultFramebufferObject(self) -> int: ...
    def screen(self) -> typing.Optional['QScreen']: ...
    def shareGroup(self) -> typing.Optional[QOpenGLContextGroup]: ...
    def shareContext(self) -> typing.Optional['QOpenGLContext']: ...
    def format(self) -> 'QSurfaceFormat': ...
    def isValid(self) -> bool: ...
    def create(self) -> bool: ...
    def setScreen(self, screen: typing.Optional['QScreen']) -> None: ...
    def setShareContext(self, shareContext: typing.Optional['QOpenGLContext']) -> None: ...
    def setFormat(self, format: 'QSurfaceFormat') -> None: ...


class QPagedPaintDevice(QPaintDevice):

    class PdfVersion(enum.Enum):
        PdfVersion_1_4 = ... # type: QPagedPaintDevice.PdfVersion
        PdfVersion_A1b = ... # type: QPagedPaintDevice.PdfVersion
        PdfVersion_1_6 = ... # type: QPagedPaintDevice.PdfVersion
        PdfVersion_X4 = ... # type: QPagedPaintDevice.PdfVersion

    def pageRanges(self) -> 'QPageRanges': ...
    def setPageRanges(self, ranges: 'QPageRanges') -> None: ...
    def setPageMargins(self, margins: QtCore.QMarginsF, units: 'QPageLayout.Unit' = ...) -> bool: ...
    def setPageOrientation(self, orientation: 'QPageLayout.Orientation') -> bool: ...
    def pageLayout(self) -> 'QPageLayout': ...
    def setPageLayout(self, pageLayout: 'QPageLayout') -> bool: ...
    def setPageSize(self, pageSize: 'QPageSize') -> bool: ...
    def newPage(self) -> bool: ...


class QPageLayout(PyQt6.sip.simplewrapper):

    class OutOfBoundsPolicy(enum.Enum):
        Reject = ... # type: QPageLayout.OutOfBoundsPolicy
        Clamp = ... # type: QPageLayout.OutOfBoundsPolicy

    class Mode(enum.Enum):
        StandardMode = ... # type: QPageLayout.Mode
        FullPageMode = ... # type: QPageLayout.Mode

    class Orientation(enum.Enum):
        Portrait = ... # type: QPageLayout.Orientation
        Landscape = ... # type: QPageLayout.Orientation

    class Unit(enum.Enum):
        Millimeter = ... # type: QPageLayout.Unit
        Point = ... # type: QPageLayout.Unit
        Inch = ... # type: QPageLayout.Unit
        Pica = ... # type: QPageLayout.Unit
        Didot = ... # type: QPageLayout.Unit
        Cicero = ... # type: QPageLayout.Unit

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, pageSize: 'QPageSize', orientation: 'QPageLayout.Orientation', margins: QtCore.QMarginsF, units: 'QPageLayout.Unit' = ..., minMargins: QtCore.QMarginsF = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPageLayout') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def paintRectPixels(self, resolution: int) -> QtCore.QRect: ...
    def paintRectPoints(self) -> QtCore.QRect: ...
    @typing.overload
    def paintRect(self) -> QtCore.QRectF: ...
    @typing.overload
    def paintRect(self, units: 'QPageLayout.Unit') -> QtCore.QRectF: ...
    def fullRectPixels(self, resolution: int) -> QtCore.QRect: ...
    def fullRectPoints(self) -> QtCore.QRect: ...
    @typing.overload
    def fullRect(self) -> QtCore.QRectF: ...
    @typing.overload
    def fullRect(self, units: 'QPageLayout.Unit') -> QtCore.QRectF: ...
    def maximumMargins(self) -> QtCore.QMarginsF: ...
    def minimumMargins(self) -> QtCore.QMarginsF: ...
    def setMinimumMargins(self, minMargins: QtCore.QMarginsF) -> None: ...
    def marginsPixels(self, resolution: int) -> QtCore.QMargins: ...
    def marginsPoints(self) -> QtCore.QMargins: ...
    @typing.overload
    def margins(self) -> QtCore.QMarginsF: ...
    @typing.overload
    def margins(self, units: 'QPageLayout.Unit') -> QtCore.QMarginsF: ...
    def setBottomMargin(self, bottomMargin: float, outOfBoundsPolicy: 'QPageLayout.OutOfBoundsPolicy' = ...) -> bool: ...
    def setTopMargin(self, topMargin: float, outOfBoundsPolicy: 'QPageLayout.OutOfBoundsPolicy' = ...) -> bool: ...
    def setRightMargin(self, rightMargin: float, outOfBoundsPolicy: 'QPageLayout.OutOfBoundsPolicy' = ...) -> bool: ...
    def setLeftMargin(self, leftMargin: float, outOfBoundsPolicy: 'QPageLayout.OutOfBoundsPolicy' = ...) -> bool: ...
    def setMargins(self, margins: QtCore.QMarginsF, outOfBoundsPolicy: 'QPageLayout.OutOfBoundsPolicy' = ...) -> bool: ...
    def units(self) -> 'QPageLayout.Unit': ...
    def setUnits(self, units: 'QPageLayout.Unit') -> None: ...
    def orientation(self) -> 'QPageLayout.Orientation': ...
    def setOrientation(self, orientation: 'QPageLayout.Orientation') -> None: ...
    def pageSize(self) -> 'QPageSize': ...
    def setPageSize(self, pageSize: 'QPageSize', minMargins: QtCore.QMarginsF = ...) -> None: ...
    def mode(self) -> 'QPageLayout.Mode': ...
    def setMode(self, mode: 'QPageLayout.Mode') -> None: ...
    def isValid(self) -> bool: ...
    def isEquivalentTo(self, other: 'QPageLayout') -> bool: ...
    def swap(self, other: 'QPageLayout') -> None: ...


class QPageRanges(PyQt6.sip.simplewrapper):

    class Range(PyQt6.sip.simplewrapper):

        from_ = ... # type: int
        to = ... # type: int

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QPageRanges.Range') -> None: ...

        def __ge__(self, rhs: 'QPageRanges.Range') -> bool: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def __lt__(self, rhs: 'QPageRanges.Range') -> bool: ...
        def contains(self, pageNumber: int) -> bool: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPageRanges') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def lastPage(self) -> int: ...
    def firstPage(self) -> int: ...
    def isEmpty(self) -> bool: ...
    def contains(self, pageNumber: int) -> bool: ...
    @staticmethod
    def fromString(ranges: typing.Optional[str]) -> 'QPageRanges': ...
    def toString(self) -> str: ...
    def clear(self) -> None: ...
    def toRangeList(self) -> list['QPageRanges.Range']: ...
    def addRange(self, from_: int, to: int) -> None: ...
    def addPage(self, pageNumber: int) -> None: ...
    def swap(self, other: 'QPageRanges') -> None: ...


class QPageSize(PyQt6.sip.simplewrapper):

    class SizeMatchPolicy(enum.Enum):
        FuzzyMatch = ... # type: QPageSize.SizeMatchPolicy
        FuzzyOrientationMatch = ... # type: QPageSize.SizeMatchPolicy
        ExactMatch = ... # type: QPageSize.SizeMatchPolicy

    class Unit(enum.Enum):
        Millimeter = ... # type: QPageSize.Unit
        Point = ... # type: QPageSize.Unit
        Inch = ... # type: QPageSize.Unit
        Pica = ... # type: QPageSize.Unit
        Didot = ... # type: QPageSize.Unit
        Cicero = ... # type: QPageSize.Unit

    class PageSizeId(enum.Enum):
        A4 = ... # type: QPageSize.PageSizeId
        B5 = ... # type: QPageSize.PageSizeId
        Letter = ... # type: QPageSize.PageSizeId
        Legal = ... # type: QPageSize.PageSizeId
        Executive = ... # type: QPageSize.PageSizeId
        A0 = ... # type: QPageSize.PageSizeId
        A1 = ... # type: QPageSize.PageSizeId
        A2 = ... # type: QPageSize.PageSizeId
        A3 = ... # type: QPageSize.PageSizeId
        A5 = ... # type: QPageSize.PageSizeId
        A6 = ... # type: QPageSize.PageSizeId
        A7 = ... # type: QPageSize.PageSizeId
        A8 = ... # type: QPageSize.PageSizeId
        A9 = ... # type: QPageSize.PageSizeId
        B0 = ... # type: QPageSize.PageSizeId
        B1 = ... # type: QPageSize.PageSizeId
        B10 = ... # type: QPageSize.PageSizeId
        B2 = ... # type: QPageSize.PageSizeId
        B3 = ... # type: QPageSize.PageSizeId
        B4 = ... # type: QPageSize.PageSizeId
        B6 = ... # type: QPageSize.PageSizeId
        B7 = ... # type: QPageSize.PageSizeId
        B8 = ... # type: QPageSize.PageSizeId
        B9 = ... # type: QPageSize.PageSizeId
        C5E = ... # type: QPageSize.PageSizeId
        Comm10E = ... # type: QPageSize.PageSizeId
        DLE = ... # type: QPageSize.PageSizeId
        Folio = ... # type: QPageSize.PageSizeId
        Ledger = ... # type: QPageSize.PageSizeId
        Tabloid = ... # type: QPageSize.PageSizeId
        Custom = ... # type: QPageSize.PageSizeId
        A10 = ... # type: QPageSize.PageSizeId
        A3Extra = ... # type: QPageSize.PageSizeId
        A4Extra = ... # type: QPageSize.PageSizeId
        A4Plus = ... # type: QPageSize.PageSizeId
        A4Small = ... # type: QPageSize.PageSizeId
        A5Extra = ... # type: QPageSize.PageSizeId
        B5Extra = ... # type: QPageSize.PageSizeId
        JisB0 = ... # type: QPageSize.PageSizeId
        JisB1 = ... # type: QPageSize.PageSizeId
        JisB2 = ... # type: QPageSize.PageSizeId
        JisB3 = ... # type: QPageSize.PageSizeId
        JisB4 = ... # type: QPageSize.PageSizeId
        JisB5 = ... # type: QPageSize.PageSizeId
        JisB6 = ... # type: QPageSize.PageSizeId
        JisB7 = ... # type: QPageSize.PageSizeId
        JisB8 = ... # type: QPageSize.PageSizeId
        JisB9 = ... # type: QPageSize.PageSizeId
        JisB10 = ... # type: QPageSize.PageSizeId
        AnsiC = ... # type: QPageSize.PageSizeId
        AnsiD = ... # type: QPageSize.PageSizeId
        AnsiE = ... # type: QPageSize.PageSizeId
        LegalExtra = ... # type: QPageSize.PageSizeId
        LetterExtra = ... # type: QPageSize.PageSizeId
        LetterPlus = ... # type: QPageSize.PageSizeId
        LetterSmall = ... # type: QPageSize.PageSizeId
        TabloidExtra = ... # type: QPageSize.PageSizeId
        ArchA = ... # type: QPageSize.PageSizeId
        ArchB = ... # type: QPageSize.PageSizeId
        ArchC = ... # type: QPageSize.PageSizeId
        ArchD = ... # type: QPageSize.PageSizeId
        ArchE = ... # type: QPageSize.PageSizeId
        Imperial7x9 = ... # type: QPageSize.PageSizeId
        Imperial8x10 = ... # type: QPageSize.PageSizeId
        Imperial9x11 = ... # type: QPageSize.PageSizeId
        Imperial9x12 = ... # type: QPageSize.PageSizeId
        Imperial10x11 = ... # type: QPageSize.PageSizeId
        Imperial10x13 = ... # type: QPageSize.PageSizeId
        Imperial10x14 = ... # type: QPageSize.PageSizeId
        Imperial12x11 = ... # type: QPageSize.PageSizeId
        Imperial15x11 = ... # type: QPageSize.PageSizeId
        ExecutiveStandard = ... # type: QPageSize.PageSizeId
        Note = ... # type: QPageSize.PageSizeId
        Quarto = ... # type: QPageSize.PageSizeId
        Statement = ... # type: QPageSize.PageSizeId
        SuperA = ... # type: QPageSize.PageSizeId
        SuperB = ... # type: QPageSize.PageSizeId
        Postcard = ... # type: QPageSize.PageSizeId
        DoublePostcard = ... # type: QPageSize.PageSizeId
        Prc16K = ... # type: QPageSize.PageSizeId
        Prc32K = ... # type: QPageSize.PageSizeId
        Prc32KBig = ... # type: QPageSize.PageSizeId
        FanFoldUS = ... # type: QPageSize.PageSizeId
        FanFoldGerman = ... # type: QPageSize.PageSizeId
        FanFoldGermanLegal = ... # type: QPageSize.PageSizeId
        EnvelopeB4 = ... # type: QPageSize.PageSizeId
        EnvelopeB5 = ... # type: QPageSize.PageSizeId
        EnvelopeB6 = ... # type: QPageSize.PageSizeId
        EnvelopeC0 = ... # type: QPageSize.PageSizeId
        EnvelopeC1 = ... # type: QPageSize.PageSizeId
        EnvelopeC2 = ... # type: QPageSize.PageSizeId
        EnvelopeC3 = ... # type: QPageSize.PageSizeId
        EnvelopeC4 = ... # type: QPageSize.PageSizeId
        EnvelopeC6 = ... # type: QPageSize.PageSizeId
        EnvelopeC65 = ... # type: QPageSize.PageSizeId
        EnvelopeC7 = ... # type: QPageSize.PageSizeId
        Envelope9 = ... # type: QPageSize.PageSizeId
        Envelope11 = ... # type: QPageSize.PageSizeId
        Envelope12 = ... # type: QPageSize.PageSizeId
        Envelope14 = ... # type: QPageSize.PageSizeId
        EnvelopeMonarch = ... # type: QPageSize.PageSizeId
        EnvelopePersonal = ... # type: QPageSize.PageSizeId
        EnvelopeChou3 = ... # type: QPageSize.PageSizeId
        EnvelopeChou4 = ... # type: QPageSize.PageSizeId
        EnvelopeInvite = ... # type: QPageSize.PageSizeId
        EnvelopeItalian = ... # type: QPageSize.PageSizeId
        EnvelopeKaku2 = ... # type: QPageSize.PageSizeId
        EnvelopeKaku3 = ... # type: QPageSize.PageSizeId
        EnvelopePrc1 = ... # type: QPageSize.PageSizeId
        EnvelopePrc2 = ... # type: QPageSize.PageSizeId
        EnvelopePrc3 = ... # type: QPageSize.PageSizeId
        EnvelopePrc4 = ... # type: QPageSize.PageSizeId
        EnvelopePrc5 = ... # type: QPageSize.PageSizeId
        EnvelopePrc6 = ... # type: QPageSize.PageSizeId
        EnvelopePrc7 = ... # type: QPageSize.PageSizeId
        EnvelopePrc8 = ... # type: QPageSize.PageSizeId
        EnvelopePrc9 = ... # type: QPageSize.PageSizeId
        EnvelopePrc10 = ... # type: QPageSize.PageSizeId
        EnvelopeYou4 = ... # type: QPageSize.PageSizeId
        AnsiA = ... # type: QPageSize.PageSizeId
        AnsiB = ... # type: QPageSize.PageSizeId
        EnvelopeC5 = ... # type: QPageSize.PageSizeId
        EnvelopeDL = ... # type: QPageSize.PageSizeId
        Envelope10 = ... # type: QPageSize.PageSizeId
        LastPageSize = ... # type: QPageSize.PageSizeId

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, pageSizeId: 'QPageSize.PageSizeId') -> None: ...
    @typing.overload
    def __init__(self, pointSize: QtCore.QSize, name: typing.Optional[str] = ..., matchPolicy: 'QPageSize.SizeMatchPolicy' = ...) -> None: ...
    @typing.overload
    def __init__(self, size: QtCore.QSizeF, units: 'QPageSize.Unit', name: typing.Optional[str] = ..., matchPolicy: 'QPageSize.SizeMatchPolicy' = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPageSize') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def rectPixels(self, resolution: int) -> QtCore.QRect: ...
    def rectPoints(self) -> QtCore.QRect: ...
    def rect(self, units: 'QPageSize.Unit') -> QtCore.QRectF: ...
    @typing.overload
    def sizePixels(self, resolution: int) -> QtCore.QSize: ...
    @typing.overload
    @staticmethod
    def sizePixels(pageSizeId: 'QPageSize.PageSizeId', resolution: int) -> QtCore.QSize: ...
    @typing.overload
    def sizePoints(self) -> QtCore.QSize: ...
    @typing.overload
    @staticmethod
    def sizePoints(pageSizeId: 'QPageSize.PageSizeId') -> QtCore.QSize: ...
    @typing.overload
    def size(self, units: 'QPageSize.Unit') -> QtCore.QSizeF: ...
    @typing.overload
    @staticmethod
    def size(pageSizeId: 'QPageSize.PageSizeId', units: 'QPageSize.Unit') -> QtCore.QSizeF: ...
    @typing.overload
    def definitionUnits(self) -> 'QPageSize.Unit': ...
    @typing.overload
    @staticmethod
    def definitionUnits(pageSizeId: 'QPageSize.PageSizeId') -> 'QPageSize.Unit': ...
    @typing.overload
    def definitionSize(self) -> QtCore.QSizeF: ...
    @typing.overload
    @staticmethod
    def definitionSize(pageSizeId: 'QPageSize.PageSizeId') -> QtCore.QSizeF: ...
    @typing.overload
    def windowsId(self) -> int: ...
    @typing.overload
    @staticmethod
    def windowsId(pageSizeId: 'QPageSize.PageSizeId') -> int: ...
    @typing.overload
    def id(self) -> 'QPageSize.PageSizeId': ...
    @typing.overload
    @staticmethod
    def id(pointSize: QtCore.QSize, matchPolicy: 'QPageSize.SizeMatchPolicy' = ...) -> 'QPageSize.PageSizeId': ...
    @typing.overload
    @staticmethod
    def id(size: QtCore.QSizeF, units: 'QPageSize.Unit', matchPolicy: 'QPageSize.SizeMatchPolicy' = ...) -> 'QPageSize.PageSizeId': ...
    @typing.overload
    @staticmethod
    def id(windowsId: int) -> 'QPageSize.PageSizeId': ...
    @typing.overload
    def name(self) -> str: ...
    @typing.overload
    @staticmethod
    def name(pageSizeId: 'QPageSize.PageSizeId') -> str: ...
    @typing.overload
    def key(self) -> str: ...
    @typing.overload
    @staticmethod
    def key(pageSizeId: 'QPageSize.PageSizeId') -> str: ...
    def isValid(self) -> bool: ...
    def isEquivalentTo(self, other: 'QPageSize') -> bool: ...
    def swap(self, other: 'QPageSize') -> None: ...


class QWindow(QtCore.QObject, QSurface):

    class Visibility(enum.Enum):
        Hidden = ... # type: QWindow.Visibility
        AutomaticVisibility = ... # type: QWindow.Visibility
        Windowed = ... # type: QWindow.Visibility
        Minimized = ... # type: QWindow.Visibility
        Maximized = ... # type: QWindow.Visibility
        FullScreen = ... # type: QWindow.Visibility

    class AncestorMode(enum.Enum):
        ExcludeTransients = ... # type: QWindow.AncestorMode
        IncludeTransients = ... # type: QWindow.AncestorMode

    @typing.overload
    def __init__(self, screen: typing.Optional['QScreen'] = ...) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional['QWindow']) -> None: ...

    safeAreaMarginsChanged: typing.ClassVar[QtCore.pyqtSignal]
    def safeAreaMargins(self) -> QtCore.QMargins: ...
    def nativeEvent(self, eventType: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], message: typing.Optional[PyQt6.sip.voidptr]) -> typing.Tuple[bool, typing.Optional[PyQt6.sip.voidptr]]: ...
    def closeEvent(self, a0: typing.Optional[QCloseEvent]) -> None: ...
    def paintEvent(self, a0: typing.Optional[QPaintEvent]) -> None: ...
    def startSystemMove(self) -> bool: ...
    def startSystemResize(self, edges: QtCore.Qt.Edge) -> bool: ...
    def setWindowStates(self, states: QtCore.Qt.WindowState) -> None: ...
    def windowStates(self) -> QtCore.Qt.WindowState: ...
    def setFlag(self, a0: QtCore.Qt.WindowType, on: bool = ...) -> None: ...
    opacityChanged: typing.ClassVar[QtCore.pyqtSignal]
    activeChanged: typing.ClassVar[QtCore.pyqtSignal]
    visibilityChanged: typing.ClassVar[QtCore.pyqtSignal]
    @staticmethod
    def fromWinId(id: PyQt6.sip.voidptr) -> typing.Optional['QWindow']: ...
    def mask(self) -> 'QRegion': ...
    def setMask(self, region: 'QRegion') -> None: ...
    def opacity(self) -> float: ...
    def setVisibility(self, v: 'QWindow.Visibility') -> None: ...
    def visibility(self) -> 'QWindow.Visibility': ...
    def tabletEvent(self, a0: typing.Optional[QTabletEvent]) -> None: ...
    def touchEvent(self, a0: typing.Optional[QTouchEvent]) -> None: ...
    def wheelEvent(self, a0: typing.Optional[QWheelEvent]) -> None: ...
    def mouseMoveEvent(self, a0: typing.Optional[QMouseEvent]) -> None: ...
    def mouseDoubleClickEvent(self, a0: typing.Optional[QMouseEvent]) -> None: ...
    def mouseReleaseEvent(self, a0: typing.Optional[QMouseEvent]) -> None: ...
    def mousePressEvent(self, a0: typing.Optional[QMouseEvent]) -> None: ...
    def keyReleaseEvent(self, a0: typing.Optional[QKeyEvent]) -> None: ...
    def keyPressEvent(self, a0: typing.Optional[QKeyEvent]) -> None: ...
    def event(self, a0: typing.Optional[QtCore.QEvent]) -> bool: ...
    def hideEvent(self, a0: typing.Optional[QHideEvent]) -> None: ...
    def showEvent(self, a0: typing.Optional[QShowEvent]) -> None: ...
    def focusOutEvent(self, a0: typing.Optional[QFocusEvent]) -> None: ...
    def focusInEvent(self, a0: typing.Optional[QFocusEvent]) -> None: ...
    def moveEvent(self, a0: typing.Optional[QMoveEvent]) -> None: ...
    def resizeEvent(self, a0: typing.Optional[QResizeEvent]) -> None: ...
    def exposeEvent(self, a0: typing.Optional[QExposeEvent]) -> None: ...
    windowTitleChanged: typing.ClassVar[QtCore.pyqtSignal]
    focusObjectChanged: typing.ClassVar[QtCore.pyqtSignal]
    contentOrientationChanged: typing.ClassVar[QtCore.pyqtSignal]
    visibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    maximumHeightChanged: typing.ClassVar[QtCore.pyqtSignal]
    maximumWidthChanged: typing.ClassVar[QtCore.pyqtSignal]
    minimumHeightChanged: typing.ClassVar[QtCore.pyqtSignal]
    minimumWidthChanged: typing.ClassVar[QtCore.pyqtSignal]
    heightChanged: typing.ClassVar[QtCore.pyqtSignal]
    widthChanged: typing.ClassVar[QtCore.pyqtSignal]
    yChanged: typing.ClassVar[QtCore.pyqtSignal]
    xChanged: typing.ClassVar[QtCore.pyqtSignal]
    windowStateChanged: typing.ClassVar[QtCore.pyqtSignal]
    modalityChanged: typing.ClassVar[QtCore.pyqtSignal]
    screenChanged: typing.ClassVar[QtCore.pyqtSignal]
    def requestUpdate(self) -> None: ...
    def alert(self, msec: int) -> None: ...
    def setMaximumHeight(self, h: int) -> None: ...
    def setMaximumWidth(self, w: int) -> None: ...
    def setMinimumHeight(self, h: int) -> None: ...
    def setMinimumWidth(self, w: int) -> None: ...
    def setHeight(self, arg: int) -> None: ...
    def setWidth(self, arg: int) -> None: ...
    def setY(self, arg: int) -> None: ...
    def setX(self, arg: int) -> None: ...
    def setTitle(self, a0: typing.Optional[str]) -> None: ...
    def lower(self) -> None: ...
    def raise_(self) -> None: ...
    def close(self) -> bool: ...
    def showNormal(self) -> None: ...
    def showFullScreen(self) -> None: ...
    def showMaximized(self) -> None: ...
    def showMinimized(self) -> None: ...
    def hide(self) -> None: ...
    def show(self) -> None: ...
    def setVisible(self, visible: bool) -> None: ...
    def unsetCursor(self) -> None: ...
    def setCursor(self, a0: typing.Union[QCursor, QtCore.Qt.CursorShape]) -> None: ...
    def cursor(self) -> QCursor: ...
    @typing.overload
    def mapFromGlobal(self, pos: QtCore.QPoint) -> QtCore.QPoint: ...
    @typing.overload
    def mapFromGlobal(self, pos: QtCore.QPointF) -> QtCore.QPointF: ...
    @typing.overload
    def mapToGlobal(self, pos: QtCore.QPoint) -> QtCore.QPoint: ...
    @typing.overload
    def mapToGlobal(self, pos: QtCore.QPointF) -> QtCore.QPointF: ...
    def focusObject(self) -> typing.Optional[QtCore.QObject]: ...
    def setScreen(self, screen: typing.Optional['QScreen']) -> None: ...
    def screen(self) -> typing.Optional['QScreen']: ...
    def setMouseGrabEnabled(self, grab: bool) -> bool: ...
    def setKeyboardGrabEnabled(self, grab: bool) -> bool: ...
    def destroy(self) -> None: ...
    def icon(self) -> QIcon: ...
    def setIcon(self, icon: QIcon) -> None: ...
    def filePath(self) -> str: ...
    def setFilePath(self, filePath: typing.Optional[str]) -> None: ...
    @typing.overload
    def resize(self, newSize: QtCore.QSize) -> None: ...
    @typing.overload
    def resize(self, w: int, h: int) -> None: ...
    @typing.overload
    def setPosition(self, pt: QtCore.QPoint) -> None: ...
    @typing.overload
    def setPosition(self, posx: int, posy: int) -> None: ...
    def position(self) -> QtCore.QPoint: ...
    def size(self) -> QtCore.QSize: ...
    def y(self) -> int: ...
    def x(self) -> int: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def setFramePosition(self, point: QtCore.QPoint) -> None: ...
    def framePosition(self) -> QtCore.QPoint: ...
    def frameGeometry(self) -> QtCore.QRect: ...
    def frameMargins(self) -> QtCore.QMargins: ...
    def geometry(self) -> QtCore.QRect: ...
    @typing.overload
    def setGeometry(self, posx: int, posy: int, w: int, h: int) -> None: ...
    @typing.overload
    def setGeometry(self, rect: QtCore.QRect) -> None: ...
    def setSizeIncrement(self, size: QtCore.QSize) -> None: ...
    def setBaseSize(self, size: QtCore.QSize) -> None: ...
    def setMaximumSize(self, size: QtCore.QSize) -> None: ...
    def setMinimumSize(self, size: QtCore.QSize) -> None: ...
    def sizeIncrement(self) -> QtCore.QSize: ...
    def baseSize(self) -> QtCore.QSize: ...
    def maximumSize(self) -> QtCore.QSize: ...
    def minimumSize(self) -> QtCore.QSize: ...
    def maximumHeight(self) -> int: ...
    def maximumWidth(self) -> int: ...
    def minimumHeight(self) -> int: ...
    def minimumWidth(self) -> int: ...
    def isExposed(self) -> bool: ...
    def isAncestorOf(self, child: typing.Optional['QWindow'], mode: 'QWindow.AncestorMode' = ...) -> bool: ...
    def transientParent(self) -> typing.Optional['QWindow']: ...
    def setTransientParent(self, parent: typing.Optional['QWindow']) -> None: ...
    def setWindowState(self, state: QtCore.Qt.WindowState) -> None: ...
    def windowState(self) -> QtCore.Qt.WindowState: ...
    def devicePixelRatio(self) -> float: ...
    def contentOrientation(self) -> QtCore.Qt.ScreenOrientation: ...
    def reportContentOrientationChange(self, orientation: QtCore.Qt.ScreenOrientation) -> None: ...
    def isActive(self) -> bool: ...
    def requestActivate(self) -> None: ...
    def setOpacity(self, level: float) -> None: ...
    def title(self) -> str: ...
    def type(self) -> QtCore.Qt.WindowType: ...
    def flags(self) -> QtCore.Qt.WindowType: ...
    def setFlags(self, flags: QtCore.Qt.WindowType) -> None: ...
    def requestedFormat(self) -> 'QSurfaceFormat': ...
    def format(self) -> 'QSurfaceFormat': ...
    def setFormat(self, format: 'QSurfaceFormat') -> None: ...
    def setModality(self, modality: QtCore.Qt.WindowModality) -> None: ...
    def modality(self) -> QtCore.Qt.WindowModality: ...
    def isModal(self) -> bool: ...
    def isTopLevel(self) -> bool: ...
    def setParent(self, parent: typing.Optional['QWindow']) -> None: ...
    def parent(self, mode: 'QWindow.AncestorMode' = ...) -> typing.Optional['QWindow']: ...
    def winId(self) -> PyQt6.sip.voidptr: ...
    def create(self) -> None: ...
    def isVisible(self) -> bool: ...
    def surfaceType(self) -> QSurface.SurfaceType: ...
    def setSurfaceType(self, surfaceType: QSurface.SurfaceType) -> None: ...


class QPaintDeviceWindow(QWindow, QPaintDevice):

    def event(self, event: typing.Optional[QtCore.QEvent]) -> bool: ...
    def exposeEvent(self, a0: typing.Optional[QExposeEvent]) -> None: ...
    def metric(self, metric: QPaintDevice.PaintDeviceMetric) -> int: ...
    def paintEvent(self, event: typing.Optional[QPaintEvent]) -> None: ...
    @typing.overload
    def update(self, rect: QtCore.QRect) -> None: ...
    @typing.overload
    def update(self, region: 'QRegion') -> None: ...
    @typing.overload
    def update(self) -> None: ...


class QTextItem(PyQt6.sip.simplewrapper):

    class RenderFlag(enum.Flag):
        RightToLeft = ... # type: QTextItem.RenderFlag
        Overline = ... # type: QTextItem.RenderFlag
        Underline = ... # type: QTextItem.RenderFlag
        StrikeOut = ... # type: QTextItem.RenderFlag

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextItem') -> None: ...

    def font(self) -> QFont: ...
    def text(self) -> str: ...
    def renderFlags(self) -> 'QTextItem.RenderFlag': ...
    def width(self) -> float: ...
    def ascent(self) -> float: ...
    def descent(self) -> float: ...


class QPaintEngine(PyQt6.sip.simplewrapper):

    class Type(enum.Enum):
        X11 = ... # type: QPaintEngine.Type
        Windows = ... # type: QPaintEngine.Type
        QuickDraw = ... # type: QPaintEngine.Type
        CoreGraphics = ... # type: QPaintEngine.Type
        MacPrinter = ... # type: QPaintEngine.Type
        QWindowSystem = ... # type: QPaintEngine.Type
        OpenGL = ... # type: QPaintEngine.Type
        Picture = ... # type: QPaintEngine.Type
        SVG = ... # type: QPaintEngine.Type
        Raster = ... # type: QPaintEngine.Type
        Direct3D = ... # type: QPaintEngine.Type
        Pdf = ... # type: QPaintEngine.Type
        OpenVG = ... # type: QPaintEngine.Type
        OpenGL2 = ... # type: QPaintEngine.Type
        PaintBuffer = ... # type: QPaintEngine.Type
        Blitter = ... # type: QPaintEngine.Type
        Direct2D = ... # type: QPaintEngine.Type
        User = ... # type: QPaintEngine.Type
        MaxUser = ... # type: QPaintEngine.Type

    class PolygonDrawMode(enum.Enum):
        OddEvenMode = ... # type: QPaintEngine.PolygonDrawMode
        WindingMode = ... # type: QPaintEngine.PolygonDrawMode
        ConvexMode = ... # type: QPaintEngine.PolygonDrawMode
        PolylineMode = ... # type: QPaintEngine.PolygonDrawMode

    class DirtyFlag(enum.Flag):
        DirtyPen = ... # type: QPaintEngine.DirtyFlag
        DirtyBrush = ... # type: QPaintEngine.DirtyFlag
        DirtyBrushOrigin = ... # type: QPaintEngine.DirtyFlag
        DirtyFont = ... # type: QPaintEngine.DirtyFlag
        DirtyBackground = ... # type: QPaintEngine.DirtyFlag
        DirtyBackgroundMode = ... # type: QPaintEngine.DirtyFlag
        DirtyTransform = ... # type: QPaintEngine.DirtyFlag
        DirtyClipRegion = ... # type: QPaintEngine.DirtyFlag
        DirtyClipPath = ... # type: QPaintEngine.DirtyFlag
        DirtyHints = ... # type: QPaintEngine.DirtyFlag
        DirtyCompositionMode = ... # type: QPaintEngine.DirtyFlag
        DirtyClipEnabled = ... # type: QPaintEngine.DirtyFlag
        DirtyOpacity = ... # type: QPaintEngine.DirtyFlag
        AllDirty = ... # type: QPaintEngine.DirtyFlag

    class PaintEngineFeature(enum.Flag):
        PrimitiveTransform = ... # type: QPaintEngine.PaintEngineFeature
        PatternTransform = ... # type: QPaintEngine.PaintEngineFeature
        PixmapTransform = ... # type: QPaintEngine.PaintEngineFeature
        PatternBrush = ... # type: QPaintEngine.PaintEngineFeature
        LinearGradientFill = ... # type: QPaintEngine.PaintEngineFeature
        RadialGradientFill = ... # type: QPaintEngine.PaintEngineFeature
        ConicalGradientFill = ... # type: QPaintEngine.PaintEngineFeature
        AlphaBlend = ... # type: QPaintEngine.PaintEngineFeature
        PorterDuff = ... # type: QPaintEngine.PaintEngineFeature
        PainterPaths = ... # type: QPaintEngine.PaintEngineFeature
        Antialiasing = ... # type: QPaintEngine.PaintEngineFeature
        BrushStroke = ... # type: QPaintEngine.PaintEngineFeature
        ConstantOpacity = ... # type: QPaintEngine.PaintEngineFeature
        MaskedBrush = ... # type: QPaintEngine.PaintEngineFeature
        PaintOutsidePaintEvent = ... # type: QPaintEngine.PaintEngineFeature
        PerspectiveTransform = ... # type: QPaintEngine.PaintEngineFeature
        BlendModes = ... # type: QPaintEngine.PaintEngineFeature
        ObjectBoundingModeGradients = ... # type: QPaintEngine.PaintEngineFeature
        RasterOpModes = ... # type: QPaintEngine.PaintEngineFeature
        AllFeatures = ... # type: QPaintEngine.PaintEngineFeature

    def __init__(self, features: 'QPaintEngine.PaintEngineFeature' = ...) -> None: ...

    def hasFeature(self, feature: 'QPaintEngine.PaintEngineFeature') -> bool: ...
    def painter(self) -> typing.Optional['QPainter']: ...
    def type(self) -> 'QPaintEngine.Type': ...
    def paintDevice(self) -> typing.Optional[QPaintDevice]: ...
    def setPaintDevice(self, device: typing.Optional[QPaintDevice]) -> None: ...
    def drawImage(self, r: QtCore.QRectF, pm: QImage, sr: QtCore.QRectF, flags: QtCore.Qt.ImageConversionFlag = ...) -> None: ...
    def drawTiledPixmap(self, r: QtCore.QRectF, pixmap: QPixmap, s: QtCore.QPointF) -> None: ...
    def drawTextItem(self, p: QtCore.QPointF, textItem: QTextItem) -> None: ...
    def drawPixmap(self, r: QtCore.QRectF, pm: QPixmap, sr: QtCore.QRectF) -> None: ...
    @typing.overload
    def drawPolygon(self, points: typing.Optional[PyQt6.sip.array[QtCore.QPointF]], mode: 'QPaintEngine.PolygonDrawMode') -> None: ...
    @typing.overload
    def drawPolygon(self, points: typing.Optional[PyQt6.sip.array[QtCore.QPoint]], mode: 'QPaintEngine.PolygonDrawMode') -> None: ...
    @typing.overload
    def drawPoints(self, points: typing.Optional[PyQt6.sip.array[QtCore.QPointF]]) -> None: ...
    @typing.overload
    def drawPoints(self, points: typing.Optional[PyQt6.sip.array[QtCore.QPoint]]) -> None: ...
    def drawPath(self, path: 'QPainterPath') -> None: ...
    @typing.overload
    def drawEllipse(self, r: QtCore.QRectF) -> None: ...
    @typing.overload
    def drawEllipse(self, r: QtCore.QRect) -> None: ...
    @typing.overload
    def drawLines(self, lines: typing.Optional[PyQt6.sip.array[QtCore.QLine]]) -> None: ...
    @typing.overload
    def drawLines(self, lines: typing.Optional[PyQt6.sip.array[QtCore.QLineF]]) -> None: ...
    @typing.overload
    def drawRects(self, rects: typing.Optional[PyQt6.sip.array[QtCore.QRect]]) -> None: ...
    @typing.overload
    def drawRects(self, rects: typing.Optional[PyQt6.sip.array[QtCore.QRectF]]) -> None: ...
    def updateState(self, state: 'QPaintEngineState') -> None: ...
    def end(self) -> bool: ...
    def begin(self, pdev: typing.Optional[QPaintDevice]) -> bool: ...
    def setActive(self, newState: bool) -> None: ...
    def isActive(self) -> bool: ...


class QPaintEngineState(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPaintEngineState') -> None: ...

    def penNeedsResolving(self) -> bool: ...
    def brushNeedsResolving(self) -> bool: ...
    def transform(self) -> 'QTransform': ...
    def painter(self) -> typing.Optional['QPainter']: ...
    def compositionMode(self) -> 'QPainter.CompositionMode': ...
    def renderHints(self) -> 'QPainter.RenderHint': ...
    def isClipEnabled(self) -> bool: ...
    def clipPath(self) -> 'QPainterPath': ...
    def clipRegion(self) -> 'QRegion': ...
    def clipOperation(self) -> QtCore.Qt.ClipOperation: ...
    def opacity(self) -> float: ...
    def font(self) -> QFont: ...
    def backgroundMode(self) -> QtCore.Qt.BGMode: ...
    def backgroundBrush(self) -> QBrush: ...
    def brushOrigin(self) -> QtCore.QPointF: ...
    def brush(self) -> QBrush: ...
    def pen(self) -> 'QPen': ...
    def state(self) -> QPaintEngine.DirtyFlag: ...


class QPainter(PyQt6.sip.simplewrapper):

    class PixmapFragmentHint(enum.Flag):
        OpaqueHint = ... # type: QPainter.PixmapFragmentHint

    class CompositionMode(enum.Enum):
        CompositionMode_SourceOver = ... # type: QPainter.CompositionMode
        CompositionMode_DestinationOver = ... # type: QPainter.CompositionMode
        CompositionMode_Clear = ... # type: QPainter.CompositionMode
        CompositionMode_Source = ... # type: QPainter.CompositionMode
        CompositionMode_Destination = ... # type: QPainter.CompositionMode
        CompositionMode_SourceIn = ... # type: QPainter.CompositionMode
        CompositionMode_DestinationIn = ... # type: QPainter.CompositionMode
        CompositionMode_SourceOut = ... # type: QPainter.CompositionMode
        CompositionMode_DestinationOut = ... # type: QPainter.CompositionMode
        CompositionMode_SourceAtop = ... # type: QPainter.CompositionMode
        CompositionMode_DestinationAtop = ... # type: QPainter.CompositionMode
        CompositionMode_Xor = ... # type: QPainter.CompositionMode
        CompositionMode_Plus = ... # type: QPainter.CompositionMode
        CompositionMode_Multiply = ... # type: QPainter.CompositionMode
        CompositionMode_Screen = ... # type: QPainter.CompositionMode
        CompositionMode_Overlay = ... # type: QPainter.CompositionMode
        CompositionMode_Darken = ... # type: QPainter.CompositionMode
        CompositionMode_Lighten = ... # type: QPainter.CompositionMode
        CompositionMode_ColorDodge = ... # type: QPainter.CompositionMode
        CompositionMode_ColorBurn = ... # type: QPainter.CompositionMode
        CompositionMode_HardLight = ... # type: QPainter.CompositionMode
        CompositionMode_SoftLight = ... # type: QPainter.CompositionMode
        CompositionMode_Difference = ... # type: QPainter.CompositionMode
        CompositionMode_Exclusion = ... # type: QPainter.CompositionMode
        RasterOp_SourceOrDestination = ... # type: QPainter.CompositionMode
        RasterOp_SourceAndDestination = ... # type: QPainter.CompositionMode
        RasterOp_SourceXorDestination = ... # type: QPainter.CompositionMode
        RasterOp_NotSourceAndNotDestination = ... # type: QPainter.CompositionMode
        RasterOp_NotSourceOrNotDestination = ... # type: QPainter.CompositionMode
        RasterOp_NotSourceXorDestination = ... # type: QPainter.CompositionMode
        RasterOp_NotSource = ... # type: QPainter.CompositionMode
        RasterOp_NotSourceAndDestination = ... # type: QPainter.CompositionMode
        RasterOp_SourceAndNotDestination = ... # type: QPainter.CompositionMode
        RasterOp_NotSourceOrDestination = ... # type: QPainter.CompositionMode
        RasterOp_SourceOrNotDestination = ... # type: QPainter.CompositionMode
        RasterOp_ClearDestination = ... # type: QPainter.CompositionMode
        RasterOp_SetDestination = ... # type: QPainter.CompositionMode
        RasterOp_NotDestination = ... # type: QPainter.CompositionMode

    class RenderHint(enum.Flag):
        Antialiasing = ... # type: QPainter.RenderHint
        TextAntialiasing = ... # type: QPainter.RenderHint
        SmoothPixmapTransform = ... # type: QPainter.RenderHint
        LosslessImageRendering = ... # type: QPainter.RenderHint
        VerticalSubpixelPositioning = ... # type: QPainter.RenderHint
        NonCosmeticBrushPatterns = ... # type: QPainter.RenderHint

    class PixmapFragment(PyQt6.sip.simplewrapper):

        height = ... # type: float
        opacity = ... # type: float
        rotation = ... # type: float
        scaleX = ... # type: float
        scaleY = ... # type: float
        sourceLeft = ... # type: float
        sourceTop = ... # type: float
        width = ... # type: float
        x = ... # type: float
        y = ... # type: float

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QPainter.PixmapFragment') -> None: ...

        @staticmethod
        def create(pos: QtCore.QPointF, sourceRect: QtCore.QRectF, scaleX: float = ..., scaleY: float = ..., rotation: float = ..., opacity: float = ...) -> 'QPainter.PixmapFragment': ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: typing.Optional[QPaintDevice]) -> None: ...

    def drawGlyphRun(self, position: QtCore.QPointF, glyphRun: QGlyphRun) -> None: ...
    def clipBoundingRect(self) -> QtCore.QRectF: ...
    @typing.overload
    def drawStaticText(self, topLeftPosition: QtCore.QPointF, staticText: 'QStaticText') -> None: ...
    @typing.overload
    def drawStaticText(self, p: QtCore.QPoint, staticText: 'QStaticText') -> None: ...
    @typing.overload
    def drawStaticText(self, x: int, y: int, staticText: 'QStaticText') -> None: ...
    def drawPixmapFragments(self, fragments: typing.Optional[PyQt6.sip.array['QPainter.PixmapFragment']], pixmap: QPixmap, hints: 'QPainter.PixmapFragmentHint' = ...) -> None: ...
    def endNativePainting(self) -> None: ...
    def beginNativePainting(self) -> None: ...
    @typing.overload
    def drawRoundedRect(self, rect: QtCore.QRectF, xRadius: float, yRadius: float, mode: QtCore.Qt.SizeMode = ...) -> None: ...
    @typing.overload
    def drawRoundedRect(self, x: int, y: int, w: int, h: int, xRadius: float, yRadius: float, mode: QtCore.Qt.SizeMode = ...) -> None: ...
    @typing.overload
    def drawRoundedRect(self, rect: QtCore.QRect, xRadius: float, yRadius: float, mode: QtCore.Qt.SizeMode = ...) -> None: ...
    def testRenderHint(self, hint: 'QPainter.RenderHint') -> bool: ...
    def combinedTransform(self) -> 'QTransform': ...
    def worldTransform(self) -> 'QTransform': ...
    def setWorldTransform(self, matrix: 'QTransform', combine: bool = ...) -> None: ...
    def resetTransform(self) -> None: ...
    def deviceTransform(self) -> 'QTransform': ...
    def transform(self) -> 'QTransform': ...
    def setTransform(self, transform: 'QTransform', combine: bool = ...) -> None: ...
    def setWorldMatrixEnabled(self, enabled: bool) -> None: ...
    def worldMatrixEnabled(self) -> bool: ...
    def setOpacity(self, opacity: float) -> None: ...
    def opacity(self) -> float: ...
    @typing.overload
    def drawImage(self, r: QtCore.QRectF, image: QImage) -> None: ...
    @typing.overload
    def drawImage(self, targetRect: QtCore.QRectF, image: QImage, sourceRect: QtCore.QRectF, flags: QtCore.Qt.ImageConversionFlag = ...) -> None: ...
    @typing.overload
    def drawImage(self, r: QtCore.QRect, image: QImage) -> None: ...
    @typing.overload
    def drawImage(self, targetRect: QtCore.QRect, image: QImage, sourceRect: QtCore.QRect, flags: QtCore.Qt.ImageConversionFlag = ...) -> None: ...
    @typing.overload
    def drawImage(self, p: QtCore.QPointF, image: QImage) -> None: ...
    @typing.overload
    def drawImage(self, p: QtCore.QPointF, image: QImage, sr: QtCore.QRectF, flags: QtCore.Qt.ImageConversionFlag = ...) -> None: ...
    @typing.overload
    def drawImage(self, p: QtCore.QPoint, image: QImage) -> None: ...
    @typing.overload
    def drawImage(self, p: QtCore.QPoint, image: QImage, sr: QtCore.QRect, flags: QtCore.Qt.ImageConversionFlag = ...) -> None: ...
    @typing.overload
    def drawImage(self, x: int, y: int, image: QImage, sx: int = ..., sy: int = ..., sw: int = ..., sh: int = ..., flags: QtCore.Qt.ImageConversionFlag = ...) -> None: ...
    @typing.overload
    def drawPoint(self, p: QtCore.QPointF) -> None: ...
    @typing.overload
    def drawPoint(self, x: int, y: int) -> None: ...
    @typing.overload
    def drawPoint(self, p: QtCore.QPoint) -> None: ...
    @typing.overload
    def drawRect(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def drawRect(self, x: int, y: int, w: int, h: int) -> None: ...
    @typing.overload
    def drawRect(self, r: QtCore.QRect) -> None: ...
    @typing.overload
    def drawLine(self, l: QtCore.QLineF) -> None: ...
    @typing.overload
    def drawLine(self, line: QtCore.QLine) -> None: ...
    @typing.overload
    def drawLine(self, x1: int, y1: int, x2: int, y2: int) -> None: ...
    @typing.overload
    def drawLine(self, p1: QtCore.QPoint, p2: QtCore.QPoint) -> None: ...
    @typing.overload
    def drawLine(self, p1: QtCore.QPointF, p2: QtCore.QPointF) -> None: ...
    def paintEngine(self) -> typing.Optional[QPaintEngine]: ...
    def setRenderHints(self, hints: 'QPainter.RenderHint', on: bool = ...) -> None: ...
    def renderHints(self) -> 'QPainter.RenderHint': ...
    def setRenderHint(self, hint: 'QPainter.RenderHint', on: bool = ...) -> None: ...
    @typing.overload
    def eraseRect(self, a0: QtCore.QRectF) -> None: ...
    @typing.overload
    def eraseRect(self, rect: QtCore.QRect) -> None: ...
    @typing.overload
    def eraseRect(self, x: int, y: int, w: int, h: int) -> None: ...
    @typing.overload
    def fillRect(self, a0: QtCore.QRectF, a1: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    @typing.overload
    def fillRect(self, a0: QtCore.QRect, a1: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    @typing.overload
    def fillRect(self, x: int, y: int, w: int, h: int, b: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    @typing.overload
    def fillRect(self, a0: QtCore.QRectF, color: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def fillRect(self, a0: QtCore.QRect, color: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def fillRect(self, x: int, y: int, w: int, h: int, b: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def fillRect(self, x: int, y: int, w: int, h: int, c: QtCore.Qt.GlobalColor) -> None: ...
    @typing.overload
    def fillRect(self, r: QtCore.QRect, c: QtCore.Qt.GlobalColor) -> None: ...
    @typing.overload
    def fillRect(self, r: QtCore.QRectF, c: QtCore.Qt.GlobalColor) -> None: ...
    @typing.overload
    def fillRect(self, x: int, y: int, w: int, h: int, style: QtCore.Qt.BrushStyle) -> None: ...
    @typing.overload
    def fillRect(self, r: QtCore.QRect, style: QtCore.Qt.BrushStyle) -> None: ...
    @typing.overload
    def fillRect(self, r: QtCore.QRectF, style: QtCore.Qt.BrushStyle) -> None: ...
    @typing.overload
    def fillRect(self, x: int, y: int, w: int, h: int, preset: QGradient.Preset) -> None: ...
    @typing.overload
    def fillRect(self, r: QtCore.QRect, preset: QGradient.Preset) -> None: ...
    @typing.overload
    def fillRect(self, r: QtCore.QRectF, preset: QGradient.Preset) -> None: ...
    @typing.overload
    def boundingRect(self, rect: QtCore.QRectF, flags: int, text: typing.Optional[str]) -> QtCore.QRectF: ...
    @typing.overload
    def boundingRect(self, rect: QtCore.QRect, flags: int, text: typing.Optional[str]) -> QtCore.QRect: ...
    @typing.overload
    def boundingRect(self, rectangle: QtCore.QRectF, text: typing.Optional[str], option: 'QTextOption' = ...) -> QtCore.QRectF: ...
    @typing.overload
    def boundingRect(self, x: int, y: int, w: int, h: int, flags: int, text: typing.Optional[str]) -> QtCore.QRect: ...
    @typing.overload
    def drawText(self, p: QtCore.QPointF, s: typing.Optional[str]) -> None: ...
    @typing.overload
    def drawText(self, rectangle: QtCore.QRectF, flags: int, text: typing.Optional[str]) -> typing.Optional[QtCore.QRectF]: ...
    @typing.overload
    def drawText(self, rectangle: QtCore.QRect, flags: int, text: typing.Optional[str]) -> typing.Optional[QtCore.QRect]: ...
    @typing.overload
    def drawText(self, rectangle: QtCore.QRectF, text: typing.Optional[str], option: 'QTextOption' = ...) -> None: ...
    @typing.overload
    def drawText(self, p: QtCore.QPoint, s: typing.Optional[str]) -> None: ...
    @typing.overload
    def drawText(self, x: int, y: int, width: int, height: int, flags: int, text: typing.Optional[str]) -> typing.Optional[QtCore.QRect]: ...
    @typing.overload
    def drawText(self, x: int, y: int, s: typing.Optional[str]) -> None: ...
    def layoutDirection(self) -> QtCore.Qt.LayoutDirection: ...
    def setLayoutDirection(self, direction: QtCore.Qt.LayoutDirection) -> None: ...
    @typing.overload
    def drawPixmap(self, targetRect: QtCore.QRectF, pixmap: QPixmap, sourceRect: QtCore.QRectF) -> None: ...
    @typing.overload
    def drawPixmap(self, targetRect: QtCore.QRect, pixmap: QPixmap, sourceRect: QtCore.QRect) -> None: ...
    @typing.overload
    def drawPixmap(self, p: QtCore.QPointF, pm: QPixmap) -> None: ...
    @typing.overload
    def drawPixmap(self, p: QtCore.QPoint, pm: QPixmap) -> None: ...
    @typing.overload
    def drawPixmap(self, r: QtCore.QRect, pm: QPixmap) -> None: ...
    @typing.overload
    def drawPixmap(self, x: int, y: int, pm: QPixmap) -> None: ...
    @typing.overload
    def drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap) -> None: ...
    @typing.overload
    def drawPixmap(self, x: int, y: int, w: int, h: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int) -> None: ...
    @typing.overload
    def drawPixmap(self, x: int, y: int, pm: QPixmap, sx: int, sy: int, sw: int, sh: int) -> None: ...
    @typing.overload
    def drawPixmap(self, p: QtCore.QPointF, pm: QPixmap, sr: QtCore.QRectF) -> None: ...
    @typing.overload
    def drawPixmap(self, p: QtCore.QPoint, pm: QPixmap, sr: QtCore.QRect) -> None: ...
    @typing.overload
    def drawPicture(self, p: QtCore.QPointF, picture: 'QPicture') -> None: ...
    @typing.overload
    def drawPicture(self, x: int, y: int, p: 'QPicture') -> None: ...
    @typing.overload
    def drawPicture(self, pt: QtCore.QPoint, p: 'QPicture') -> None: ...
    @typing.overload
    def drawTiledPixmap(self, rectangle: QtCore.QRectF, pixmap: QPixmap, pos: QtCore.QPointF = ...) -> None: ...
    @typing.overload
    def drawTiledPixmap(self, rectangle: QtCore.QRect, pixmap: QPixmap, pos: QtCore.QPoint = ...) -> None: ...
    @typing.overload
    def drawTiledPixmap(self, x: int, y: int, width: int, height: int, pixmap: QPixmap, sx: int = ..., sy: int = ...) -> None: ...
    @typing.overload
    def drawChord(self, rect: QtCore.QRectF, a: int, alen: int) -> None: ...
    @typing.overload
    def drawChord(self, rect: QtCore.QRect, a: int, alen: int) -> None: ...
    @typing.overload
    def drawChord(self, x: int, y: int, w: int, h: int, a: int, alen: int) -> None: ...
    @typing.overload
    def drawPie(self, rect: QtCore.QRectF, a: int, alen: int) -> None: ...
    @typing.overload
    def drawPie(self, rect: QtCore.QRect, a: int, alen: int) -> None: ...
    @typing.overload
    def drawPie(self, x: int, y: int, w: int, h: int, a: int, alen: int) -> None: ...
    @typing.overload
    def drawArc(self, rect: QtCore.QRectF, a: int, alen: int) -> None: ...
    @typing.overload
    def drawArc(self, r: QtCore.QRect, a: int, alen: int) -> None: ...
    @typing.overload
    def drawArc(self, x: int, y: int, w: int, h: int, a: int, alen: int) -> None: ...
    @typing.overload
    def drawConvexPolygon(self, poly: 'QPolygonF') -> None: ...
    @typing.overload
    def drawConvexPolygon(self, poly: 'QPolygon') -> None: ...
    @typing.overload
    def drawConvexPolygon(self, points: typing.Optional[PyQt6.sip.array[QtCore.QPointF]]) -> None: ...
    @typing.overload
    def drawConvexPolygon(self, point: typing.Optional[QtCore.QPointF], *args: QtCore.QPointF) -> None: ...
    @typing.overload
    def drawConvexPolygon(self, points: typing.Optional[PyQt6.sip.array[QtCore.QPoint]]) -> None: ...
    @typing.overload
    def drawConvexPolygon(self, point: typing.Optional[QtCore.QPoint], *args: QtCore.QPoint) -> None: ...
    @typing.overload
    def drawPolygon(self, points: 'QPolygonF', fillRule: QtCore.Qt.FillRule = ...) -> None: ...
    @typing.overload
    def drawPolygon(self, points: 'QPolygon', fillRule: QtCore.Qt.FillRule = ...) -> None: ...
    @typing.overload
    def drawPolygon(self, points: typing.Optional[PyQt6.sip.array[QtCore.QPointF]], fillRule: QtCore.Qt.FillRule = ...) -> None: ...
    @typing.overload
    def drawPolygon(self, point: typing.Optional[QtCore.QPointF], *args: QtCore.QPointF) -> None: ...
    @typing.overload
    def drawPolygon(self, points: typing.Optional[PyQt6.sip.array[QtCore.QPoint]], fillRule: QtCore.Qt.FillRule = ...) -> None: ...
    @typing.overload
    def drawPolygon(self, point: typing.Optional[QtCore.QPoint], *args: QtCore.QPoint) -> None: ...
    @typing.overload
    def drawPolyline(self, polyline: 'QPolygonF') -> None: ...
    @typing.overload
    def drawPolyline(self, polyline: 'QPolygon') -> None: ...
    @typing.overload
    def drawPolyline(self, points: typing.Optional[PyQt6.sip.array[QtCore.QPointF]]) -> None: ...
    @typing.overload
    def drawPolyline(self, point: typing.Optional[QtCore.QPointF], *args: QtCore.QPointF) -> None: ...
    @typing.overload
    def drawPolyline(self, points: typing.Optional[PyQt6.sip.array[QtCore.QPoint]]) -> None: ...
    @typing.overload
    def drawPolyline(self, point: typing.Optional[QtCore.QPoint], *args: QtCore.QPoint) -> None: ...
    @typing.overload
    def drawEllipse(self, r: QtCore.QRectF) -> None: ...
    @typing.overload
    def drawEllipse(self, r: QtCore.QRect) -> None: ...
    @typing.overload
    def drawEllipse(self, x: int, y: int, w: int, h: int) -> None: ...
    @typing.overload
    def drawEllipse(self, center: QtCore.QPointF, rx: float, ry: float) -> None: ...
    @typing.overload
    def drawEllipse(self, center: QtCore.QPoint, rx: int, ry: int) -> None: ...
    @typing.overload
    def drawRects(self, rects: typing.Optional[PyQt6.sip.array[QtCore.QRectF]]) -> None: ...
    @typing.overload
    def drawRects(self, rect: typing.Optional[QtCore.QRectF], *args: QtCore.QRectF) -> None: ...
    @typing.overload
    def drawRects(self, rects: typing.Optional[PyQt6.sip.array[QtCore.QRect]]) -> None: ...
    @typing.overload
    def drawRects(self, rect: typing.Optional[QtCore.QRect], *args: QtCore.QRect) -> None: ...
    @typing.overload
    def drawLines(self, lines: typing.Optional[PyQt6.sip.array[QtCore.QLineF]]) -> None: ...
    @typing.overload
    def drawLines(self, line: typing.Optional[QtCore.QLineF], *args: QtCore.QLineF) -> None: ...
    @typing.overload
    def drawLines(self, pointPairs: typing.Optional[PyQt6.sip.array[QtCore.QPointF]]) -> None: ...
    @typing.overload
    def drawLines(self, pointPair: typing.Optional[QtCore.QPointF], *args: QtCore.QPointF) -> None: ...
    @typing.overload
    def drawLines(self, lines: typing.Optional[PyQt6.sip.array[QtCore.QLine]]) -> None: ...
    @typing.overload
    def drawLines(self, line: typing.Optional[QtCore.QLine], *args: QtCore.QLine) -> None: ...
    @typing.overload
    def drawLines(self, pointPairs: typing.Optional[PyQt6.sip.array[QtCore.QPoint]]) -> None: ...
    @typing.overload
    def drawLines(self, pointPair: typing.Optional[QtCore.QPoint], *args: QtCore.QPoint) -> None: ...
    @typing.overload
    def drawPoints(self, points: 'QPolygonF') -> None: ...
    @typing.overload
    def drawPoints(self, points: 'QPolygon') -> None: ...
    @typing.overload
    def drawPoints(self, points: typing.Optional[PyQt6.sip.array[QtCore.QPointF]]) -> None: ...
    @typing.overload
    def drawPoints(self, point: typing.Optional[QtCore.QPointF], *args: QtCore.QPointF) -> None: ...
    @typing.overload
    def drawPoints(self, points: typing.Optional[PyQt6.sip.array[QtCore.QPoint]]) -> None: ...
    @typing.overload
    def drawPoints(self, point: typing.Optional[QtCore.QPoint], *args: QtCore.QPoint) -> None: ...
    def drawPath(self, path: 'QPainterPath') -> None: ...
    def fillPath(self, path: 'QPainterPath', brush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    def strokePath(self, path: 'QPainterPath', pen: typing.Union['QPen', typing.Union[QColor, QtCore.Qt.GlobalColor, int]]) -> None: ...
    def viewTransformEnabled(self) -> bool: ...
    def setViewTransformEnabled(self, enable: bool) -> None: ...
    @typing.overload
    def setViewport(self, viewport: QtCore.QRect) -> None: ...
    @typing.overload
    def setViewport(self, x: int, y: int, w: int, h: int) -> None: ...
    def viewport(self) -> QtCore.QRect: ...
    @typing.overload
    def setWindow(self, window: QtCore.QRect) -> None: ...
    @typing.overload
    def setWindow(self, x: int, y: int, w: int, h: int) -> None: ...
    def window(self) -> QtCore.QRect: ...
    @typing.overload
    def translate(self, offset: QtCore.QPointF) -> None: ...
    @typing.overload
    def translate(self, dx: float, dy: float) -> None: ...
    @typing.overload
    def translate(self, offset: QtCore.QPoint) -> None: ...
    def rotate(self, a: float) -> None: ...
    def shear(self, sh: float, sv: float) -> None: ...
    def scale(self, sx: float, sy: float) -> None: ...
    def restore(self) -> None: ...
    def save(self) -> None: ...
    def hasClipping(self) -> bool: ...
    def setClipping(self, enable: bool) -> None: ...
    def setClipPath(self, path: 'QPainterPath', operation: QtCore.Qt.ClipOperation = ...) -> None: ...
    def setClipRegion(self, region: 'QRegion', operation: QtCore.Qt.ClipOperation = ...) -> None: ...
    @typing.overload
    def setClipRect(self, rectangle: QtCore.QRectF, operation: QtCore.Qt.ClipOperation = ...) -> None: ...
    @typing.overload
    def setClipRect(self, x: int, y: int, width: int, height: int, operation: QtCore.Qt.ClipOperation = ...) -> None: ...
    @typing.overload
    def setClipRect(self, rectangle: QtCore.QRect, operation: QtCore.Qt.ClipOperation = ...) -> None: ...
    def clipPath(self) -> 'QPainterPath': ...
    def clipRegion(self) -> 'QRegion': ...
    def background(self) -> QBrush: ...
    def setBackground(self, bg: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    @typing.overload
    def setBrushOrigin(self, a0: QtCore.QPointF) -> None: ...
    @typing.overload
    def setBrushOrigin(self, x: int, y: int) -> None: ...
    @typing.overload
    def setBrushOrigin(self, p: QtCore.QPoint) -> None: ...
    def brushOrigin(self) -> QtCore.QPoint: ...
    def backgroundMode(self) -> QtCore.Qt.BGMode: ...
    def setBackgroundMode(self, mode: QtCore.Qt.BGMode) -> None: ...
    def brush(self) -> QBrush: ...
    @typing.overload
    def setBrush(self, brush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    @typing.overload
    def setBrush(self, style: QtCore.Qt.BrushStyle) -> None: ...
    def pen(self) -> 'QPen': ...
    @typing.overload
    def setPen(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def setPen(self, pen: typing.Union['QPen', typing.Union[QColor, QtCore.Qt.GlobalColor, int]]) -> None: ...
    @typing.overload
    def setPen(self, style: QtCore.Qt.PenStyle) -> None: ...
    def fontInfo(self) -> QFontInfo: ...
    def fontMetrics(self) -> QFontMetrics: ...
    def setFont(self, f: QFont) -> None: ...
    def font(self) -> QFont: ...
    def compositionMode(self) -> 'QPainter.CompositionMode': ...
    def setCompositionMode(self, mode: 'QPainter.CompositionMode') -> None: ...
    def isActive(self) -> bool: ...
    def end(self) -> bool: ...
    def begin(self, a0: typing.Optional[QPaintDevice]) -> bool: ...
    def device(self) -> typing.Optional[QPaintDevice]: ...
    def __exit__(self, type: typing.Any, value: typing.Any, traceback: typing.Any) -> None: ...
    def __enter__(self) -> typing.Any: ...


class QPainterPath(PyQt6.sip.simplewrapper):

    class ElementType(enum.Enum):
        MoveToElement = ... # type: QPainterPath.ElementType
        LineToElement = ... # type: QPainterPath.ElementType
        CurveToElement = ... # type: QPainterPath.ElementType
        CurveToDataElement = ... # type: QPainterPath.ElementType

    class Element(PyQt6.sip.simplewrapper):

        type = ... # type: 'QPainterPath.ElementType'
        x = ... # type: float
        y = ... # type: float

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QPainterPath.Element') -> None: ...

        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def isCurveTo(self) -> bool: ...
        def isLineTo(self) -> bool: ...
        def isMoveTo(self) -> bool: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, startPoint: QtCore.QPointF) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPainterPath') -> None: ...

    def capacity(self) -> int: ...
    def reserve(self, size: int) -> None: ...
    def clear(self) -> None: ...
    def swap(self, other: 'QPainterPath') -> None: ...
    @typing.overload
    def translated(self, dx: float, dy: float) -> 'QPainterPath': ...
    @typing.overload
    def translated(self, offset: QtCore.QPointF) -> 'QPainterPath': ...
    @typing.overload
    def translate(self, dx: float, dy: float) -> None: ...
    @typing.overload
    def translate(self, offset: QtCore.QPointF) -> None: ...
    def __isub__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def __iadd__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def __ior__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def __iand__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def __sub__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def __add__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def __or__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def __and__(self, other: 'QPainterPath') -> 'QPainterPath': ...
    def simplified(self) -> 'QPainterPath': ...
    @typing.overload
    def addRoundedRect(self, rect: QtCore.QRectF, xRadius: float, yRadius: float, mode: QtCore.Qt.SizeMode = ...) -> None: ...
    @typing.overload
    def addRoundedRect(self, x: float, y: float, w: float, h: float, xRadius: float, yRadius: float, mode: QtCore.Qt.SizeMode = ...) -> None: ...
    def subtracted(self, r: 'QPainterPath') -> 'QPainterPath': ...
    def intersected(self, r: 'QPainterPath') -> 'QPainterPath': ...
    def united(self, r: 'QPainterPath') -> 'QPainterPath': ...
    def slopeAtPercent(self, t: float) -> float: ...
    def angleAtPercent(self, t: float) -> float: ...
    def pointAtPercent(self, t: float) -> QtCore.QPointF: ...
    def percentAtLength(self, t: float) -> float: ...
    def length(self) -> float: ...
    def setElementPositionAt(self, i: int, x: float, y: float) -> None: ...
    def elementAt(self, i: int) -> 'QPainterPath.Element': ...
    def elementCount(self) -> int: ...
    def isEmpty(self) -> bool: ...
    @typing.overload
    def arcMoveTo(self, rect: QtCore.QRectF, angle: float) -> None: ...
    @typing.overload
    def arcMoveTo(self, x: float, y: float, w: float, h: float, angle: float) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def toFillPolygon(self, matrix: 'QTransform' = ...) -> 'QPolygonF': ...
    def toFillPolygons(self, matrix: 'QTransform' = ...) -> list['QPolygonF']: ...
    def toSubpathPolygons(self, matrix: 'QTransform' = ...) -> list['QPolygonF']: ...
    def toReversed(self) -> 'QPainterPath': ...
    def setFillRule(self, fillRule: QtCore.Qt.FillRule) -> None: ...
    def fillRule(self) -> QtCore.Qt.FillRule: ...
    def controlPointRect(self) -> QtCore.QRectF: ...
    def boundingRect(self) -> QtCore.QRectF: ...
    @typing.overload
    def intersects(self, rect: QtCore.QRectF) -> bool: ...
    @typing.overload
    def intersects(self, p: 'QPainterPath') -> bool: ...
    @typing.overload
    def contains(self, pt: QtCore.QPointF) -> bool: ...
    @typing.overload
    def contains(self, rect: QtCore.QRectF) -> bool: ...
    @typing.overload
    def contains(self, p: 'QPainterPath') -> bool: ...
    def connectPath(self, path: 'QPainterPath') -> None: ...
    def addRegion(self, region: 'QRegion') -> None: ...
    def addPath(self, path: 'QPainterPath') -> None: ...
    @typing.overload
    def addText(self, point: QtCore.QPointF, f: QFont, text: typing.Optional[str]) -> None: ...
    @typing.overload
    def addText(self, x: float, y: float, f: QFont, text: typing.Optional[str]) -> None: ...
    def addPolygon(self, polygon: 'QPolygonF') -> None: ...
    @typing.overload
    def addEllipse(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def addEllipse(self, x: float, y: float, w: float, h: float) -> None: ...
    @typing.overload
    def addEllipse(self, center: QtCore.QPointF, rx: float, ry: float) -> None: ...
    @typing.overload
    def addRect(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def addRect(self, x: float, y: float, w: float, h: float) -> None: ...
    def currentPosition(self) -> QtCore.QPointF: ...
    @typing.overload
    def quadTo(self, ctrlPt: QtCore.QPointF, endPt: QtCore.QPointF) -> None: ...
    @typing.overload
    def quadTo(self, ctrlPtx: float, ctrlPty: float, endPtx: float, endPty: float) -> None: ...
    @typing.overload
    def cubicTo(self, ctrlPt1: QtCore.QPointF, ctrlPt2: QtCore.QPointF, endPt: QtCore.QPointF) -> None: ...
    @typing.overload
    def cubicTo(self, ctrlPt1x: float, ctrlPt1y: float, ctrlPt2x: float, ctrlPt2y: float, endPtx: float, endPty: float) -> None: ...
    @typing.overload
    def arcTo(self, rect: QtCore.QRectF, startAngle: float, arcLength: float) -> None: ...
    @typing.overload
    def arcTo(self, x: float, y: float, w: float, h: float, startAngle: float, arcLenght: float) -> None: ...
    @typing.overload
    def lineTo(self, p: QtCore.QPointF) -> None: ...
    @typing.overload
    def lineTo(self, x: float, y: float) -> None: ...
    @typing.overload
    def moveTo(self, p: QtCore.QPointF) -> None: ...
    @typing.overload
    def moveTo(self, x: float, y: float) -> None: ...
    def closeSubpath(self) -> None: ...


class QPainterPathStroker(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, pen: typing.Union['QPen', typing.Union[QColor, QtCore.Qt.GlobalColor, int]]) -> None: ...

    def dashOffset(self) -> float: ...
    def setDashOffset(self, offset: float) -> None: ...
    def createStroke(self, path: QPainterPath) -> QPainterPath: ...
    def dashPattern(self) -> list[float]: ...
    @typing.overload
    def setDashPattern(self, a0: QtCore.Qt.PenStyle) -> None: ...
    @typing.overload
    def setDashPattern(self, dashPattern: collections.abc.Iterable[float]) -> None: ...
    def curveThreshold(self) -> float: ...
    def setCurveThreshold(self, threshold: float) -> None: ...
    def miterLimit(self) -> float: ...
    def setMiterLimit(self, length: float) -> None: ...
    def joinStyle(self) -> QtCore.Qt.PenJoinStyle: ...
    def setJoinStyle(self, style: QtCore.Qt.PenJoinStyle) -> None: ...
    def capStyle(self) -> QtCore.Qt.PenCapStyle: ...
    def setCapStyle(self, style: QtCore.Qt.PenCapStyle) -> None: ...
    def width(self) -> float: ...
    def setWidth(self, width: float) -> None: ...


class QPainterStateGuard(PyQt6.sip.simplewrapper):

    class InitialState(enum.Enum):
        Save = ... # type: QPainterStateGuard.InitialState
        NoSave = ... # type: QPainterStateGuard.InitialState

    def __init__(self, painter: typing.Optional[QPainter], state: 'QPainterStateGuard.InitialState' = ...) -> None: ...

    def restore(self) -> None: ...
    def save(self) -> None: ...
    def swap(self, other: 'QPainterStateGuard') -> None: ...


class QPalette(PyQt6.sip.simplewrapper):

    class ColorRole(enum.Enum):
        WindowText = ... # type: QPalette.ColorRole
        Button = ... # type: QPalette.ColorRole
        Light = ... # type: QPalette.ColorRole
        Midlight = ... # type: QPalette.ColorRole
        Dark = ... # type: QPalette.ColorRole
        Mid = ... # type: QPalette.ColorRole
        Text = ... # type: QPalette.ColorRole
        BrightText = ... # type: QPalette.ColorRole
        ButtonText = ... # type: QPalette.ColorRole
        Base = ... # type: QPalette.ColorRole
        Window = ... # type: QPalette.ColorRole
        Shadow = ... # type: QPalette.ColorRole
        Highlight = ... # type: QPalette.ColorRole
        HighlightedText = ... # type: QPalette.ColorRole
        Link = ... # type: QPalette.ColorRole
        LinkVisited = ... # type: QPalette.ColorRole
        AlternateBase = ... # type: QPalette.ColorRole
        ToolTipBase = ... # type: QPalette.ColorRole
        ToolTipText = ... # type: QPalette.ColorRole
        PlaceholderText = ... # type: QPalette.ColorRole
        Accent = ... # type: QPalette.ColorRole
        NoRole = ... # type: QPalette.ColorRole
        NColorRoles = ... # type: QPalette.ColorRole

    class ColorGroup(enum.Enum):
        Active = ... # type: QPalette.ColorGroup
        Disabled = ... # type: QPalette.ColorGroup
        Inactive = ... # type: QPalette.ColorGroup
        NColorGroups = ... # type: QPalette.ColorGroup
        Current = ... # type: QPalette.ColorGroup
        All = ... # type: QPalette.ColorGroup
        Normal = ... # type: QPalette.ColorGroup

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, button: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def __init__(self, button: QtCore.Qt.GlobalColor) -> None: ...
    @typing.overload
    def __init__(self, button: typing.Union[QColor, QtCore.Qt.GlobalColor, int], background: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def __init__(self, foreground: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], button: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], light: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], dark: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], mid: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], text: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], bright_text: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], base: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], background: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    @typing.overload
    def __init__(self, palette: 'QPalette') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def accent(self) -> QBrush: ...
    def swap(self, other: 'QPalette') -> None: ...
    def cacheKey(self) -> int: ...
    def isBrushSet(self, cg: 'QPalette.ColorGroup', cr: 'QPalette.ColorRole') -> bool: ...
    @typing.overload
    def setColor(self, acg: 'QPalette.ColorGroup', acr: 'QPalette.ColorRole', acolor: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def setColor(self, acr: 'QPalette.ColorRole', acolor: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def resolve(self, a0: 'QPalette') -> 'QPalette': ...
    def isCopyOf(self, p: 'QPalette') -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def placeholderText(self) -> QBrush: ...
    def toolTipText(self) -> QBrush: ...
    def toolTipBase(self) -> QBrush: ...
    def linkVisited(self) -> QBrush: ...
    def link(self) -> QBrush: ...
    def highlightedText(self) -> QBrush: ...
    def highlight(self) -> QBrush: ...
    def shadow(self) -> QBrush: ...
    def buttonText(self) -> QBrush: ...
    def brightText(self) -> QBrush: ...
    def midlight(self) -> QBrush: ...
    def window(self) -> QBrush: ...
    def alternateBase(self) -> QBrush: ...
    def base(self) -> QBrush: ...
    def text(self) -> QBrush: ...
    def mid(self) -> QBrush: ...
    def dark(self) -> QBrush: ...
    def light(self) -> QBrush: ...
    def button(self) -> QBrush: ...
    def windowText(self) -> QBrush: ...
    def isEqual(self, cr1: 'QPalette.ColorGroup', cr2: 'QPalette.ColorGroup') -> bool: ...
    def setColorGroup(self, cr: 'QPalette.ColorGroup', foreground: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], button: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], light: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], dark: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], mid: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], text: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], bright_text: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], base: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], background: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    @typing.overload
    def setBrush(self, cg: 'QPalette.ColorGroup', cr: 'QPalette.ColorRole', brush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    @typing.overload
    def setBrush(self, acr: 'QPalette.ColorRole', abrush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    @typing.overload
    def brush(self, cg: 'QPalette.ColorGroup', cr: 'QPalette.ColorRole') -> QBrush: ...
    @typing.overload
    def brush(self, cr: 'QPalette.ColorRole') -> QBrush: ...
    @typing.overload
    def color(self, cg: 'QPalette.ColorGroup', cr: 'QPalette.ColorRole') -> QColor: ...
    @typing.overload
    def color(self, cr: 'QPalette.ColorRole') -> QColor: ...
    def setCurrentColorGroup(self, cg: 'QPalette.ColorGroup') -> None: ...
    def currentColorGroup(self) -> 'QPalette.ColorGroup': ...


class QPdfOutputIntent(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QPdfOutputIntent') -> None: ...

    def setOutputProfile(self, profile: QColorSpace) -> None: ...
    def outputProfile(self) -> QColorSpace: ...
    def setRegistryName(self, name: QtCore.QUrl) -> None: ...
    def registryName(self) -> QtCore.QUrl: ...
    def setOutputCondition(self, condition: typing.Optional[str]) -> None: ...
    def outputCondition(self) -> str: ...
    def setOutputConditionIdentifier(self, identifier: typing.Optional[str]) -> None: ...
    def outputConditionIdentifier(self) -> str: ...
    def swap(self, other: 'QPdfOutputIntent') -> None: ...


class QPdfWriter(QtCore.QObject, QPagedPaintDevice):

    class ColorModel(enum.Enum):
        RGB = ... # type: QPdfWriter.ColorModel
        Grayscale = ... # type: QPdfWriter.ColorModel
        CMYK = ... # type: QPdfWriter.ColorModel
        Auto = ... # type: QPdfWriter.ColorModel

    @typing.overload
    def __init__(self, filename: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...

    def setAuthor(self, author: typing.Optional[str]) -> None: ...
    def author(self) -> str: ...
    def setOutputIntent(self, intent: QPdfOutputIntent) -> None: ...
    def outputIntent(self) -> QPdfOutputIntent: ...
    def setColorModel(self, model: 'QPdfWriter.ColorModel') -> None: ...
    def colorModel(self) -> 'QPdfWriter.ColorModel': ...
    def setDocumentId(self, documentId: QtCore.QUuid) -> None: ...
    def documentId(self) -> QtCore.QUuid: ...
    def addFileAttachment(self, fileName: typing.Optional[str], data: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], mimeType: typing.Optional[str] = ...) -> None: ...
    def documentXmpMetadata(self) -> QtCore.QByteArray: ...
    def setDocumentXmpMetadata(self, xmpMetadata: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def pdfVersion(self) -> QPagedPaintDevice.PdfVersion: ...
    def setPdfVersion(self, version: QPagedPaintDevice.PdfVersion) -> None: ...
    def resolution(self) -> int: ...
    def setResolution(self, resolution: int) -> None: ...
    def metric(self, id: QPaintDevice.PaintDeviceMetric) -> int: ...
    def paintEngine(self) -> typing.Optional[QPaintEngine]: ...
    def newPage(self) -> bool: ...
    def setCreator(self, creator: typing.Optional[str]) -> None: ...
    def creator(self) -> str: ...
    def setTitle(self, title: typing.Optional[str]) -> None: ...
    def title(self) -> str: ...


class QPen(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: QtCore.Qt.PenStyle) -> None: ...
    @typing.overload
    def __init__(self, brush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient], width: float, style: QtCore.Qt.PenStyle = ..., cap: QtCore.Qt.PenCapStyle = ..., join: QtCore.Qt.PenJoinStyle = ...) -> None: ...
    @typing.overload
    def __init__(self, pen: typing.Union['QPen', typing.Union[QColor, QtCore.Qt.GlobalColor, int]]) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def swap(self, other: 'QPen') -> None: ...
    def setCosmetic(self, cosmetic: bool) -> None: ...
    def isCosmetic(self) -> bool: ...
    def setDashOffset(self, doffset: float) -> None: ...
    def dashOffset(self) -> float: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def setMiterLimit(self, limit: float) -> None: ...
    def miterLimit(self) -> float: ...
    def setDashPattern(self, pattern: collections.abc.Iterable[float]) -> None: ...
    def dashPattern(self) -> list[float]: ...
    def setJoinStyle(self, pcs: QtCore.Qt.PenJoinStyle) -> None: ...
    def joinStyle(self) -> QtCore.Qt.PenJoinStyle: ...
    def setCapStyle(self, pcs: QtCore.Qt.PenCapStyle) -> None: ...
    def capStyle(self) -> QtCore.Qt.PenCapStyle: ...
    def isSolid(self) -> bool: ...
    def setBrush(self, brush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    def brush(self) -> QBrush: ...
    def setColor(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def color(self) -> QColor: ...
    def setWidth(self, width: int) -> None: ...
    def width(self) -> int: ...
    def setWidthF(self, width: float) -> None: ...
    def widthF(self) -> float: ...
    def setStyle(self, a0: QtCore.Qt.PenStyle) -> None: ...
    def style(self) -> QtCore.Qt.PenStyle: ...


class QPicture(QPaintDevice):

    @typing.overload
    def __init__(self, formatVersion: int = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPicture') -> None: ...

    def swap(self, other: 'QPicture') -> None: ...
    def metric(self, m: QPaintDevice.PaintDeviceMetric) -> int: ...
    def paintEngine(self) -> typing.Optional[QPaintEngine]: ...
    def isDetached(self) -> bool: ...
    def detach(self) -> None: ...
    def setBoundingRect(self, r: QtCore.QRect) -> None: ...
    def boundingRect(self) -> QtCore.QRect: ...
    @typing.overload
    def save(self, fileName: typing.Optional[str]) -> bool: ...
    @typing.overload
    def save(self, dev: typing.Optional[QtCore.QIODevice]) -> bool: ...
    @typing.overload
    def load(self, fileName: typing.Optional[str]) -> bool: ...
    @typing.overload
    def load(self, dev: typing.Optional[QtCore.QIODevice]) -> bool: ...
    def play(self, p: typing.Optional[QPainter]) -> bool: ...
    def setData(self, data: typing.Optional[PyQt6.sip.array[bytes]]) -> None: ...
    def data(self) -> typing.Optional[bytes]: ...
    def size(self) -> int: ...
    def devType(self) -> int: ...
    def isNull(self) -> bool: ...


class QPixelFormat(PyQt6.sip.simplewrapper):

    class ByteOrder(enum.Enum):
        LittleEndian = ... # type: QPixelFormat.ByteOrder
        BigEndian = ... # type: QPixelFormat.ByteOrder
        CurrentSystemEndian = ... # type: QPixelFormat.ByteOrder

    class YUVLayout(enum.Enum):
        YUV444 = ... # type: QPixelFormat.YUVLayout
        YUV422 = ... # type: QPixelFormat.YUVLayout
        YUV411 = ... # type: QPixelFormat.YUVLayout
        YUV420P = ... # type: QPixelFormat.YUVLayout
        YUV420SP = ... # type: QPixelFormat.YUVLayout
        YV12 = ... # type: QPixelFormat.YUVLayout
        UYVY = ... # type: QPixelFormat.YUVLayout
        YUYV = ... # type: QPixelFormat.YUVLayout
        NV12 = ... # type: QPixelFormat.YUVLayout
        NV21 = ... # type: QPixelFormat.YUVLayout
        IMC1 = ... # type: QPixelFormat.YUVLayout
        IMC2 = ... # type: QPixelFormat.YUVLayout
        IMC3 = ... # type: QPixelFormat.YUVLayout
        IMC4 = ... # type: QPixelFormat.YUVLayout
        Y8 = ... # type: QPixelFormat.YUVLayout
        Y16 = ... # type: QPixelFormat.YUVLayout

    class TypeInterpretation(enum.Enum):
        UnsignedInteger = ... # type: QPixelFormat.TypeInterpretation
        UnsignedShort = ... # type: QPixelFormat.TypeInterpretation
        UnsignedByte = ... # type: QPixelFormat.TypeInterpretation
        FloatingPoint = ... # type: QPixelFormat.TypeInterpretation

    class AlphaPremultiplied(enum.Enum):
        NotPremultiplied = ... # type: QPixelFormat.AlphaPremultiplied
        Premultiplied = ... # type: QPixelFormat.AlphaPremultiplied

    class AlphaPosition(enum.Enum):
        AtBeginning = ... # type: QPixelFormat.AlphaPosition
        AtEnd = ... # type: QPixelFormat.AlphaPosition

    class AlphaUsage(enum.Enum):
        UsesAlpha = ... # type: QPixelFormat.AlphaUsage
        IgnoresAlpha = ... # type: QPixelFormat.AlphaUsage

    class ColorModel(enum.Enum):
        RGB = ... # type: QPixelFormat.ColorModel
        BGR = ... # type: QPixelFormat.ColorModel
        Indexed = ... # type: QPixelFormat.ColorModel
        Grayscale = ... # type: QPixelFormat.ColorModel
        CMYK = ... # type: QPixelFormat.ColorModel
        HSL = ... # type: QPixelFormat.ColorModel
        HSV = ... # type: QPixelFormat.ColorModel
        YUV = ... # type: QPixelFormat.ColorModel
        Alpha = ... # type: QPixelFormat.ColorModel

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, mdl: 'QPixelFormat.ColorModel', firstSize: int, secondSize: int, thirdSize: int, fourthSize: int, fifthSize: int, alfa: int, usage: 'QPixelFormat.AlphaUsage', position: 'QPixelFormat.AlphaPosition', premult: 'QPixelFormat.AlphaPremultiplied', typeInterp: 'QPixelFormat.TypeInterpretation', byteOrder: 'QPixelFormat.ByteOrder' = ..., subEnum: int = ...) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPixelFormat') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def subEnum(self) -> int: ...
    def yuvLayout(self) -> 'QPixelFormat.YUVLayout': ...
    def byteOrder(self) -> 'QPixelFormat.ByteOrder': ...
    def typeInterpretation(self) -> 'QPixelFormat.TypeInterpretation': ...
    def premultiplied(self) -> 'QPixelFormat.AlphaPremultiplied': ...
    def alphaPosition(self) -> 'QPixelFormat.AlphaPosition': ...
    def alphaUsage(self) -> 'QPixelFormat.AlphaUsage': ...
    def bitsPerPixel(self) -> int: ...
    def alphaSize(self) -> int: ...
    def brightnessSize(self) -> int: ...
    def lightnessSize(self) -> int: ...
    def saturationSize(self) -> int: ...
    def hueSize(self) -> int: ...
    def blackSize(self) -> int: ...
    def yellowSize(self) -> int: ...
    def magentaSize(self) -> int: ...
    def cyanSize(self) -> int: ...
    def blueSize(self) -> int: ...
    def greenSize(self) -> int: ...
    def redSize(self) -> int: ...
    def channelCount(self) -> int: ...
    def colorModel(self) -> 'QPixelFormat.ColorModel': ...


class QPixmapCache(PyQt6.sip.simplewrapper):

    class Key(PyQt6.sip.simplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, other: 'QPixmapCache.Key') -> None: ...

        def __hash__(self) -> int: ...
        def isValid(self) -> bool: ...
        def swap(self, other: 'QPixmapCache.Key') -> None: ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPixmapCache') -> None: ...

    @staticmethod
    def setCacheLimit(a0: int) -> None: ...
    @staticmethod
    def replace(key: 'QPixmapCache.Key', pixmap: QPixmap) -> bool: ...
    @typing.overload
    @staticmethod
    def remove(key: typing.Optional[str]) -> None: ...
    @typing.overload
    @staticmethod
    def remove(key: 'QPixmapCache.Key') -> None: ...
    @typing.overload
    @staticmethod
    def insert(key: typing.Optional[str], a1: QPixmap) -> bool: ...
    @typing.overload
    @staticmethod
    def insert(pixmap: QPixmap) -> 'QPixmapCache.Key': ...
    @typing.overload
    @staticmethod
    def find(key: typing.Optional[str]) -> QPixmap: ...
    @typing.overload
    @staticmethod
    def find(key: 'QPixmapCache.Key') -> QPixmap: ...
    @staticmethod
    def clear() -> None: ...
    @staticmethod
    def cacheLimit() -> int: ...


class QPointingDeviceUniqueId(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPointingDeviceUniqueId') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __hash__(self) -> int: ...
    def numericId(self) -> int: ...
    def isValid(self) -> bool: ...
    @staticmethod
    def fromNumericId(id: int) -> 'QPointingDeviceUniqueId': ...


class QPointingDevice(QInputDevice):

    class PointerType(enum.Flag):
        Unknown = ... # type: QPointingDevice.PointerType
        Generic = ... # type: QPointingDevice.PointerType
        Finger = ... # type: QPointingDevice.PointerType
        Pen = ... # type: QPointingDevice.PointerType
        Eraser = ... # type: QPointingDevice.PointerType
        Cursor = ... # type: QPointingDevice.PointerType
        AllPointerTypes = ... # type: QPointingDevice.PointerType

    @typing.overload
    def __init__(self, name: typing.Optional[str], systemId: int, devType: QInputDevice.DeviceType, pType: 'QPointingDevice.PointerType', caps: QInputDevice.Capability, maxPoints: int, buttonCount: int, seatName: typing.Optional[str] = ..., uniqueId: QPointingDeviceUniqueId = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @staticmethod
    def primaryPointingDevice(seatName: typing.Optional[str] = ...) -> typing.Optional['QPointingDevice']: ...
    def uniqueId(self) -> QPointingDeviceUniqueId: ...
    def buttonCount(self) -> int: ...
    def maximumPoints(self) -> int: ...
    def pointerType(self) -> 'QPointingDevice.PointerType': ...


class QPolygon(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, r: QtCore.QRect, closed: bool = ...) -> None: ...
    @typing.overload
    def __init__(self, v: collections.abc.Iterable[QtCore.QPoint]) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPolygon') -> None: ...

    def __mul__(self, m: 'QTransform') -> 'QPolygon': ...
    def toPolygonF(self) -> 'QPolygonF': ...
    def __contains__(self, value: QtCore.QPoint) -> int: ...
    @typing.overload
    def __delitem__(self, i: int) -> None: ...
    @typing.overload
    def __delitem__(self, slice: slice) -> None: ...
    @typing.overload
    def __setitem__(self, i: int, value: QtCore.QPoint) -> None: ...
    @typing.overload
    def __setitem__(self, slice: slice, list: 'QPolygon') -> None: ...
    @typing.overload
    def __getitem__(self, i: int) -> QtCore.QPoint: ...
    @typing.overload
    def __getitem__(self, slice: slice) -> 'QPolygon': ...
    def __lshift__(self, value: QtCore.QPoint) -> typing.Any: ...
    def __eq__(self, other: object): ...
    @typing.overload
    def __iadd__(self, other: 'QPolygon') -> 'QPolygon': ...
    @typing.overload
    def __iadd__(self, value: QtCore.QPoint) -> 'QPolygon': ...
    def __add__(self, other: 'QPolygon') -> 'QPolygon': ...
    def __ne__(self, other: object): ...
    @typing.overload
    def value(self, i: int) -> QtCore.QPoint: ...
    @typing.overload
    def value(self, i: int, defaultValue: QtCore.QPoint) -> QtCore.QPoint: ...
    def size(self) -> int: ...
    def resize(self, size: int) -> None: ...
    def replace(self, i: int, value: QtCore.QPoint) -> None: ...
    @typing.overload
    def remove(self, i: int) -> None: ...
    @typing.overload
    def remove(self, i: int, count: int) -> None: ...
    def prepend(self, value: QtCore.QPoint) -> None: ...
    def mid(self, pos: int, length: int = ...) -> 'QPolygon': ...
    def lastIndexOf(self, value: QtCore.QPoint, from_: int = ...) -> int: ...
    def last(self) -> QtCore.QPoint: ...
    def isEmpty(self) -> bool: ...
    def insert(self, i: int, value: QtCore.QPoint) -> None: ...
    def indexOf(self, value: QtCore.QPoint, from_: int = ...) -> int: ...
    def first(self) -> QtCore.QPoint: ...
    def fill(self, value: QtCore.QPoint, size: int = ...) -> None: ...
    def data(self) -> typing.Optional[PyQt6.sip.voidptr]: ...
    def __len__(self) -> int: ...
    @typing.overload
    def count(self, value: QtCore.QPoint) -> int: ...
    @typing.overload
    def count(self) -> int: ...
    def contains(self, value: QtCore.QPoint) -> bool: ...
    def clear(self) -> None: ...
    def at(self, i: int) -> QtCore.QPoint: ...
    def append(self, value: QtCore.QPoint) -> None: ...
    def intersects(self, r: 'QPolygon') -> bool: ...
    def subtracted(self, r: 'QPolygon') -> 'QPolygon': ...
    def intersected(self, r: 'QPolygon') -> 'QPolygon': ...
    def united(self, r: 'QPolygon') -> 'QPolygon': ...
    def containsPoint(self, pt: QtCore.QPoint, fillRule: QtCore.Qt.FillRule) -> bool: ...
    @typing.overload
    def putPoints(self, index: int, firstx: int, firsty: int, *args: int) -> None: ...
    @typing.overload
    def putPoints(self, index: int, nPoints: int, fromPolygon: 'QPolygon', from_: int = ...) -> None: ...
    def setPoints(self, firstx: int, firsty: int, *args: int) -> None: ...
    @typing.overload
    def setPoint(self, index: int, x: int, y: int) -> None: ...
    @typing.overload
    def setPoint(self, index: int, p: QtCore.QPoint) -> None: ...
    def point(self, i: int) -> QtCore.QPoint: ...
    def boundingRect(self) -> QtCore.QRect: ...
    @typing.overload
    def translated(self, dx: int, dy: int) -> 'QPolygon': ...
    @typing.overload
    def translated(self, offset: QtCore.QPoint) -> 'QPolygon': ...
    @typing.overload
    def translate(self, dx: int, dy: int) -> None: ...
    @typing.overload
    def translate(self, offset: QtCore.QPoint) -> None: ...
    def swap(self, other: 'QPolygon') -> None: ...


class QPolygonF(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, v: collections.abc.Iterable[QtCore.QPointF]) -> None: ...
    @typing.overload
    def __init__(self, r: QtCore.QRectF) -> None: ...
    @typing.overload
    def __init__(self, a: QPolygon) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QPolygonF') -> None: ...

    def __mul__(self, m: 'QTransform') -> 'QPolygonF': ...
    def __contains__(self, value: QtCore.QPointF) -> int: ...
    @typing.overload
    def __delitem__(self, i: int) -> None: ...
    @typing.overload
    def __delitem__(self, slice: slice) -> None: ...
    @typing.overload
    def __setitem__(self, i: int, value: QtCore.QPointF) -> None: ...
    @typing.overload
    def __setitem__(self, slice: slice, list: 'QPolygonF') -> None: ...
    @typing.overload
    def __getitem__(self, i: int) -> QtCore.QPointF: ...
    @typing.overload
    def __getitem__(self, slice: slice) -> 'QPolygonF': ...
    def __lshift__(self, value: QtCore.QPointF) -> typing.Any: ...
    def __eq__(self, other: object): ...
    @typing.overload
    def __iadd__(self, other: 'QPolygonF') -> 'QPolygonF': ...
    @typing.overload
    def __iadd__(self, value: QtCore.QPointF) -> 'QPolygonF': ...
    def __add__(self, other: 'QPolygonF') -> 'QPolygonF': ...
    def __ne__(self, other: object): ...
    @typing.overload
    def value(self, i: int) -> QtCore.QPointF: ...
    @typing.overload
    def value(self, i: int, defaultValue: QtCore.QPointF) -> QtCore.QPointF: ...
    def size(self) -> int: ...
    def resize(self, size: int) -> None: ...
    def replace(self, i: int, value: QtCore.QPointF) -> None: ...
    @typing.overload
    def remove(self, i: int) -> None: ...
    @typing.overload
    def remove(self, i: int, count: int) -> None: ...
    def prepend(self, value: QtCore.QPointF) -> None: ...
    def mid(self, pos: int, length: int = ...) -> 'QPolygonF': ...
    def lastIndexOf(self, value: QtCore.QPointF, from_: int = ...) -> int: ...
    def last(self) -> QtCore.QPointF: ...
    def isEmpty(self) -> bool: ...
    def insert(self, i: int, value: QtCore.QPointF) -> None: ...
    def indexOf(self, value: QtCore.QPointF, from_: int = ...) -> int: ...
    def first(self) -> QtCore.QPointF: ...
    def fill(self, value: QtCore.QPointF, size: int = ...) -> None: ...
    def data(self) -> typing.Optional[PyQt6.sip.voidptr]: ...
    def __len__(self) -> int: ...
    @typing.overload
    def count(self, value: QtCore.QPointF) -> int: ...
    @typing.overload
    def count(self) -> int: ...
    def contains(self, value: QtCore.QPointF) -> bool: ...
    def clear(self) -> None: ...
    def at(self, i: int) -> QtCore.QPointF: ...
    def append(self, value: QtCore.QPointF) -> None: ...
    def intersects(self, r: 'QPolygonF') -> bool: ...
    def subtracted(self, r: 'QPolygonF') -> 'QPolygonF': ...
    def intersected(self, r: 'QPolygonF') -> 'QPolygonF': ...
    def united(self, r: 'QPolygonF') -> 'QPolygonF': ...
    def containsPoint(self, pt: QtCore.QPointF, fillRule: QtCore.Qt.FillRule) -> bool: ...
    def boundingRect(self) -> QtCore.QRectF: ...
    def isClosed(self) -> bool: ...
    def toPolygon(self) -> QPolygon: ...
    @typing.overload
    def translated(self, dx: float, dy: float) -> 'QPolygonF': ...
    @typing.overload
    def translated(self, offset: QtCore.QPointF) -> 'QPolygonF': ...
    @typing.overload
    def translate(self, dx: float, dy: float) -> None: ...
    @typing.overload
    def translate(self, offset: QtCore.QPointF) -> None: ...
    def swap(self, other: 'QPolygonF') -> None: ...


class QQuaternion(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, aScalar: float, xpos: float, ypos: float, zpos: float) -> None: ...
    @typing.overload
    def __init__(self, aScalar: float, aVector: 'QVector3D') -> None: ...
    @typing.overload
    def __init__(self, aVector: 'QVector4D') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QQuaternion') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __truediv__(self, divisor: float) -> 'QQuaternion': ...
    def __add__(self, q2: 'QQuaternion') -> 'QQuaternion': ...
    def __sub__(self, q2: 'QQuaternion') -> 'QQuaternion': ...
    @typing.overload
    def __mul__(self, q2: 'QQuaternion') -> 'QQuaternion': ...
    @typing.overload
    def __mul__(self, factor: float) -> 'QQuaternion': ...
    @typing.overload
    def __mul__(self, vec: 'QVector3D') -> 'QVector3D': ...
    def __rmul__(self, factor: float) -> 'QQuaternion': ...
    def __neg__(self) -> 'QQuaternion': ...
    def toEulerAngles(self) -> 'QVector3D': ...
    def conjugated(self) -> 'QQuaternion': ...
    def inverted(self) -> 'QQuaternion': ...
    @staticmethod
    def dotProduct(q1: 'QQuaternion', q2: 'QQuaternion') -> float: ...
    @staticmethod
    def rotationTo(from_: 'QVector3D', to: 'QVector3D') -> 'QQuaternion': ...
    @staticmethod
    def fromDirection(direction: 'QVector3D', up: 'QVector3D') -> 'QQuaternion': ...
    @staticmethod
    def fromAxes(xAxis: 'QVector3D', yAxis: 'QVector3D', zAxis: 'QVector3D') -> 'QQuaternion': ...
    def getAxes(self) -> typing.Tuple[typing.Optional['QVector3D'], typing.Optional['QVector3D'], typing.Optional['QVector3D']]: ...
    @staticmethod
    def fromRotationMatrix(rot3x3: QMatrix3x3) -> 'QQuaternion': ...
    def toRotationMatrix(self) -> QMatrix3x3: ...
    @typing.overload
    @staticmethod
    def fromEulerAngles(pitch: float, yaw: float, roll: float) -> 'QQuaternion': ...
    @typing.overload
    @staticmethod
    def fromEulerAngles(eulerAngles: 'QVector3D') -> 'QQuaternion': ...
    def getEulerAngles(self) -> typing.Tuple[typing.Optional[float], typing.Optional[float], typing.Optional[float]]: ...
    def getAxisAndAngle(self) -> typing.Tuple[typing.Optional['QVector3D'], typing.Optional[float]]: ...
    def toVector4D(self) -> 'QVector4D': ...
    def vector(self) -> 'QVector3D': ...
    @typing.overload
    def setVector(self, aVector: 'QVector3D') -> None: ...
    @typing.overload
    def setVector(self, aX: float, aY: float, aZ: float) -> None: ...
    def __itruediv__(self, divisor: float) -> 'QQuaternion': ...
    @typing.overload
    def __imul__(self, factor: float) -> 'QQuaternion': ...
    @typing.overload
    def __imul__(self, quaternion: 'QQuaternion') -> 'QQuaternion': ...
    def __isub__(self, quaternion: 'QQuaternion') -> 'QQuaternion': ...
    def __iadd__(self, quaternion: 'QQuaternion') -> 'QQuaternion': ...
    def setScalar(self, aScalar: float) -> None: ...
    def setZ(self, aZ: float) -> None: ...
    def setY(self, aY: float) -> None: ...
    def setX(self, aX: float) -> None: ...
    def scalar(self) -> float: ...
    def z(self) -> float: ...
    def y(self) -> float: ...
    def x(self) -> float: ...
    def isIdentity(self) -> bool: ...
    def isNull(self) -> bool: ...
    @staticmethod
    def nlerp(q1: 'QQuaternion', q2: 'QQuaternion', t: float) -> 'QQuaternion': ...
    @staticmethod
    def slerp(q1: 'QQuaternion', q2: 'QQuaternion', t: float) -> 'QQuaternion': ...
    @typing.overload
    @staticmethod
    def fromAxisAndAngle(axis: 'QVector3D', angle: float) -> 'QQuaternion': ...
    @typing.overload
    @staticmethod
    def fromAxisAndAngle(x: float, y: float, z: float, angle: float) -> 'QQuaternion': ...
    def rotatedVector(self, vector: 'QVector3D') -> 'QVector3D': ...
    def normalize(self) -> None: ...
    def normalized(self) -> 'QQuaternion': ...
    def lengthSquared(self) -> float: ...
    def length(self) -> float: ...
    def __repr__(self) -> str: ...


class QRasterWindow(QPaintDeviceWindow):

    def __init__(self, parent: typing.Optional[QWindow] = ...) -> None: ...

    def resizeEvent(self, event: typing.Optional[QResizeEvent]) -> None: ...
    def metric(self, metric: QPaintDevice.PaintDeviceMetric) -> int: ...


class QRawFont(PyQt6.sip.simplewrapper):

    class LayoutFlag(enum.Flag):
        SeparateAdvances = ... # type: QRawFont.LayoutFlag
        KernedAdvances = ... # type: QRawFont.LayoutFlag
        UseDesignMetrics = ... # type: QRawFont.LayoutFlag

    class AntialiasingType(enum.Enum):
        PixelAntialiasing = ... # type: QRawFont.AntialiasingType
        SubPixelAntialiasing = ... # type: QRawFont.AntialiasingType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str], pixelSize: float, hintingPreference: QFont.HintingPreference = ...) -> None: ...
    @typing.overload
    def __init__(self, fontData: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], pixelSize: float, hintingPreference: QFont.HintingPreference = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QRawFont') -> None: ...

    def __hash__(self) -> int: ...
    def capHeight(self) -> float: ...
    def swap(self, other: 'QRawFont') -> None: ...
    def underlinePosition(self) -> float: ...
    def lineThickness(self) -> float: ...
    def boundingRect(self, glyphIndex: int) -> QtCore.QRectF: ...
    @staticmethod
    def fromFont(font: QFont, writingSystem: QFontDatabase.WritingSystem = ...) -> 'QRawFont': ...
    @typing.overload
    def fontTable(self, tagName: typing.Optional[str]) -> QtCore.QByteArray: ...
    @typing.overload
    def fontTable(self, tag: QFont.Tag) -> QtCore.QByteArray: ...
    def supportedWritingSystems(self) -> list[QFontDatabase.WritingSystem]: ...
    @typing.overload
    def supportsCharacter(self, ucs4: int) -> bool: ...
    @typing.overload
    def supportsCharacter(self, character: str) -> bool: ...
    def loadFromData(self, fontData: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], pixelSize: float, hintingPreference: QFont.HintingPreference) -> None: ...
    def loadFromFile(self, fileName: typing.Optional[str], pixelSize: float, hintingPreference: QFont.HintingPreference) -> None: ...
    def unitsPerEm(self) -> float: ...
    def maxCharWidth(self) -> float: ...
    def averageCharWidth(self) -> float: ...
    def xHeight(self) -> float: ...
    def leading(self) -> float: ...
    def descent(self) -> float: ...
    def ascent(self) -> float: ...
    def hintingPreference(self) -> QFont.HintingPreference: ...
    def pixelSize(self) -> float: ...
    def setPixelSize(self, pixelSize: float) -> None: ...
    def pathForGlyph(self, glyphIndex: int) -> QPainterPath: ...
    def alphaMapForGlyph(self, glyphIndex: int, antialiasingType: 'QRawFont.AntialiasingType' = ..., transform: 'QTransform' = ...) -> QImage: ...
    @typing.overload
    def advancesForGlyphIndexes(self, glyphIndexes: collections.abc.Iterable[int], layoutFlags: 'QRawFont.LayoutFlag') -> list[QtCore.QPointF]: ...
    @typing.overload
    def advancesForGlyphIndexes(self, glyphIndexes: collections.abc.Iterable[int]) -> list[QtCore.QPointF]: ...
    def glyphIndexesForString(self, text: typing.Optional[str]) -> list[int]: ...
    def weight(self) -> int: ...
    def style(self) -> QFont.Style: ...
    def styleName(self) -> str: ...
    def familyName(self) -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isValid(self) -> bool: ...


class QRegion(PyQt6.sip.simplewrapper):

    class RegionType(enum.Enum):
        Rectangle = ... # type: QRegion.RegionType
        Ellipse = ... # type: QRegion.RegionType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, x: int, y: int, w: int, h: int, type: 'QRegion.RegionType' = ...) -> None: ...
    @typing.overload
    def __init__(self, r: QtCore.QRect, type: 'QRegion.RegionType' = ...) -> None: ...
    @typing.overload
    def __init__(self, a: QPolygon, fillRule: QtCore.Qt.FillRule = ...) -> None: ...
    @typing.overload
    def __init__(self, bitmap: QBitmap) -> None: ...
    @typing.overload
    def __init__(self, region: 'QRegion') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def __mul__(self, m: 'QTransform') -> 'QRegion': ...
    def isNull(self) -> bool: ...
    def swap(self, other: 'QRegion') -> None: ...
    def rectCount(self) -> int: ...
    @typing.overload
    def intersects(self, r: 'QRegion') -> bool: ...
    @typing.overload
    def intersects(self, r: QtCore.QRect) -> bool: ...
    def xored(self, r: 'QRegion') -> 'QRegion': ...
    def subtracted(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def intersected(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def intersected(self, r: QtCore.QRect) -> 'QRegion': ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def __ixor__(self, r: 'QRegion') -> 'QRegion': ...
    def __isub__(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def __iand__(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def __iand__(self, r: QtCore.QRect) -> 'QRegion': ...
    @typing.overload
    def __iadd__(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def __iadd__(self, r: QtCore.QRect) -> 'QRegion': ...
    def __ior__(self, r: 'QRegion') -> 'QRegion': ...
    def __xor__(self, r: 'QRegion') -> 'QRegion': ...
    def __sub__(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def __and__(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def __and__(self, r: QtCore.QRect) -> 'QRegion': ...
    @typing.overload
    def __add__(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def __add__(self, r: QtCore.QRect) -> 'QRegion': ...
    def setRects(self, a0: collections.abc.Iterable[QtCore.QRect]) -> None: ...
    def __or__(self, r: 'QRegion') -> 'QRegion': ...
    def boundingRect(self) -> QtCore.QRect: ...
    @typing.overload
    def united(self, r: 'QRegion') -> 'QRegion': ...
    @typing.overload
    def united(self, r: QtCore.QRect) -> 'QRegion': ...
    @typing.overload
    def translated(self, dx: int, dy: int) -> 'QRegion': ...
    @typing.overload
    def translated(self, p: QtCore.QPoint) -> 'QRegion': ...
    @typing.overload
    def translate(self, dx: int, dy: int) -> None: ...
    @typing.overload
    def translate(self, p: QtCore.QPoint) -> None: ...
    @typing.overload
    def __contains__(self, p: QtCore.QPoint) -> int: ...
    @typing.overload
    def __contains__(self, r: QtCore.QRect) -> int: ...
    @typing.overload
    def contains(self, p: QtCore.QPoint) -> bool: ...
    @typing.overload
    def contains(self, r: QtCore.QRect) -> bool: ...
    def __bool__(self) -> int: ...
    def isEmpty(self) -> bool: ...


class QRgba64(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QRgba64') -> None: ...

    def __int__(self) -> int: ...
    def unpremultiplied(self) -> 'QRgba64': ...
    def premultiplied(self) -> 'QRgba64': ...
    def toRgb16(self) -> int: ...
    def toArgb32(self) -> int: ...
    def alpha8(self) -> int: ...
    def blue8(self) -> int: ...
    def green8(self) -> int: ...
    def red8(self) -> int: ...
    def setAlpha(self, _alpha: int) -> None: ...
    def setBlue(self, _blue: int) -> None: ...
    def setGreen(self, _green: int) -> None: ...
    def setRed(self, _red: int) -> None: ...
    def alpha(self) -> int: ...
    def blue(self) -> int: ...
    def green(self) -> int: ...
    def red(self) -> int: ...
    def isTransparent(self) -> bool: ...
    def isOpaque(self) -> bool: ...
    @staticmethod
    def fromArgb32(rgb: int) -> 'QRgba64': ...
    @staticmethod
    def fromRgba(red: int, green: int, blue: int, alpha: int) -> 'QRgba64': ...
    @typing.overload
    @staticmethod
    def fromRgba64(c: int) -> 'QRgba64': ...
    @typing.overload
    @staticmethod
    def fromRgba64(red: int, green: int, blue: int, alpha: int) -> 'QRgba64': ...


class QScreen(QtCore.QObject):

    def virtualSiblingAt(self, point: QtCore.QPoint) -> typing.Optional['QScreen']: ...
    def serialNumber(self) -> str: ...
    def model(self) -> str: ...
    def manufacturer(self) -> str: ...
    availableGeometryChanged: typing.ClassVar[QtCore.pyqtSignal]
    virtualGeometryChanged: typing.ClassVar[QtCore.pyqtSignal]
    physicalSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    refreshRateChanged: typing.ClassVar[QtCore.pyqtSignal]
    orientationChanged: typing.ClassVar[QtCore.pyqtSignal]
    primaryOrientationChanged: typing.ClassVar[QtCore.pyqtSignal]
    logicalDotsPerInchChanged: typing.ClassVar[QtCore.pyqtSignal]
    physicalDotsPerInchChanged: typing.ClassVar[QtCore.pyqtSignal]
    geometryChanged: typing.ClassVar[QtCore.pyqtSignal]
    def devicePixelRatio(self) -> float: ...
    def refreshRate(self) -> float: ...
    def grabWindow(self, window: PyQt6.sip.voidptr = ..., x: int = ..., y: int = ..., width: int = ..., height: int = ...) -> QPixmap: ...
    def isLandscape(self, orientation: QtCore.Qt.ScreenOrientation) -> bool: ...
    def isPortrait(self, orientation: QtCore.Qt.ScreenOrientation) -> bool: ...
    def mapBetween(self, a: QtCore.Qt.ScreenOrientation, b: QtCore.Qt.ScreenOrientation, rect: QtCore.QRect) -> QtCore.QRect: ...
    def transformBetween(self, a: QtCore.Qt.ScreenOrientation, b: QtCore.Qt.ScreenOrientation, target: QtCore.QRect) -> 'QTransform': ...
    def angleBetween(self, a: QtCore.Qt.ScreenOrientation, b: QtCore.Qt.ScreenOrientation) -> int: ...
    def orientation(self) -> QtCore.Qt.ScreenOrientation: ...
    def primaryOrientation(self) -> QtCore.Qt.ScreenOrientation: ...
    def nativeOrientation(self) -> QtCore.Qt.ScreenOrientation: ...
    def availableVirtualGeometry(self) -> QtCore.QRect: ...
    def availableVirtualSize(self) -> QtCore.QSize: ...
    def virtualGeometry(self) -> QtCore.QRect: ...
    def virtualSize(self) -> QtCore.QSize: ...
    def virtualSiblings(self) -> list['QScreen']: ...
    def availableGeometry(self) -> QtCore.QRect: ...
    def availableSize(self) -> QtCore.QSize: ...
    def logicalDotsPerInch(self) -> float: ...
    def logicalDotsPerInchY(self) -> float: ...
    def logicalDotsPerInchX(self) -> float: ...
    def physicalDotsPerInch(self) -> float: ...
    def physicalDotsPerInchY(self) -> float: ...
    def physicalDotsPerInchX(self) -> float: ...
    def physicalSize(self) -> QtCore.QSizeF: ...
    def geometry(self) -> QtCore.QRect: ...
    def size(self) -> QtCore.QSize: ...
    def depth(self) -> int: ...
    def name(self) -> str: ...


class QSessionManager(QtCore.QObject):

    class RestartHint(enum.Enum):
        RestartIfRunning = ... # type: QSessionManager.RestartHint
        RestartAnyway = ... # type: QSessionManager.RestartHint
        RestartImmediately = ... # type: QSessionManager.RestartHint
        RestartNever = ... # type: QSessionManager.RestartHint

    def requestPhase2(self) -> None: ...
    def isPhase2(self) -> bool: ...
    @typing.overload
    def setManagerProperty(self, name: typing.Optional[str], value: typing.Optional[str]) -> None: ...
    @typing.overload
    def setManagerProperty(self, name: typing.Optional[str], value: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    def discardCommand(self) -> list[str]: ...
    def setDiscardCommand(self, a0: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    def restartCommand(self) -> list[str]: ...
    def setRestartCommand(self, a0: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    def restartHint(self) -> 'QSessionManager.RestartHint': ...
    def setRestartHint(self, a0: 'QSessionManager.RestartHint') -> None: ...
    def cancel(self) -> None: ...
    def release(self) -> None: ...
    def allowsErrorInteraction(self) -> bool: ...
    def allowsInteraction(self) -> bool: ...
    def sessionKey(self) -> str: ...
    def sessionId(self) -> str: ...


class QShortcut(QtCore.QObject):

    @typing.overload
    def __init__(self, key: QKeySequence.StandardKey, parent: typing.Optional[QtCore.QObject], member: PYQT_SLOT = ..., ambiguousMember: PYQT_SLOT = ..., context: QtCore.Qt.ShortcutContext = ...) -> None: ...
    @typing.overload
    def __init__(self, key: typing.Union[QKeySequence, QKeySequence.StandardKey, typing.Optional[str], int], parent: typing.Optional[QtCore.QObject], member: PYQT_SLOT = ..., ambiguousMember: PYQT_SLOT = ..., context: QtCore.Qt.ShortcutContext = ...) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject]) -> None: ...

    def event(self, e: typing.Optional[QtCore.QEvent]) -> bool: ...
    def keys(self) -> list[QKeySequence]: ...
    @typing.overload
    def setKeys(self, keys: collections.abc.Iterable[typing.Union[QKeySequence, QKeySequence.StandardKey, typing.Optional[str], int]]) -> None: ...
    @typing.overload
    def setKeys(self, key: QKeySequence.StandardKey) -> None: ...
    activatedAmbiguously: typing.ClassVar[QtCore.pyqtSignal]
    activated: typing.ClassVar[QtCore.pyqtSignal]
    def autoRepeat(self) -> bool: ...
    def setAutoRepeat(self, on: bool) -> None: ...
    def whatsThis(self) -> str: ...
    def setWhatsThis(self, text: typing.Optional[str]) -> None: ...
    def context(self) -> QtCore.Qt.ShortcutContext: ...
    def setContext(self, context: QtCore.Qt.ShortcutContext) -> None: ...
    def isEnabled(self) -> bool: ...
    def setEnabled(self, enable: bool) -> None: ...
    def key(self) -> QKeySequence: ...
    def setKey(self, key: typing.Union[QKeySequence, QKeySequence.StandardKey, typing.Optional[str], int]) -> None: ...


class QStandardItemModel(QtCore.QAbstractItemModel):

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, rows: int, columns: int, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    itemChanged: typing.ClassVar[QtCore.pyqtSignal]
    def roleNames(self) -> dict[int, QtCore.QByteArray]: ...
    def clearItemData(self, index: QtCore.QModelIndex) -> bool: ...
    def setItemRoleNames(self, roleNames: dict[int, typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]]) -> None: ...
    def dropMimeData(self, data: typing.Optional[QtCore.QMimeData], action: QtCore.Qt.DropAction, row: int, column: int, parent: QtCore.QModelIndex) -> bool: ...
    def mimeData(self, indexes: collections.abc.Iterable[QtCore.QModelIndex]) -> typing.Optional[QtCore.QMimeData]: ...
    def mimeTypes(self) -> list[str]: ...
    def setSortRole(self, role: int) -> None: ...
    def sortRole(self) -> int: ...
    def findItems(self, text: typing.Optional[str], flags: QtCore.Qt.MatchFlag = ..., column: int = ...) -> list['QStandardItem']: ...
    def setItemPrototype(self, item: typing.Optional['QStandardItem']) -> None: ...
    def itemPrototype(self) -> typing.Optional['QStandardItem']: ...
    def takeVerticalHeaderItem(self, row: int) -> typing.Optional['QStandardItem']: ...
    def takeHorizontalHeaderItem(self, column: int) -> typing.Optional['QStandardItem']: ...
    def takeColumn(self, column: int) -> list['QStandardItem']: ...
    def takeRow(self, row: int) -> list['QStandardItem']: ...
    def takeItem(self, row: int, column: int = ...) -> typing.Optional['QStandardItem']: ...
    @typing.overload
    def insertColumn(self, column: int, items: collections.abc.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def insertColumn(self, column: int, parent: QtCore.QModelIndex = ...) -> bool: ...
    @typing.overload
    def insertRow(self, row: int, items: collections.abc.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def insertRow(self, arow: int, aitem: typing.Optional['QStandardItem']) -> None: ...
    @typing.overload
    def insertRow(self, row: int, parent: QtCore.QModelIndex = ...) -> bool: ...
    def appendColumn(self, items: collections.abc.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def appendRow(self, items: collections.abc.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def appendRow(self, aitem: typing.Optional['QStandardItem']) -> None: ...
    def setColumnCount(self, columns: int) -> None: ...
    def setRowCount(self, rows: int) -> None: ...
    def setVerticalHeaderLabels(self, labels: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    def setHorizontalHeaderLabels(self, labels: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    def setVerticalHeaderItem(self, row: int, item: typing.Optional['QStandardItem']) -> None: ...
    def verticalHeaderItem(self, row: int) -> typing.Optional['QStandardItem']: ...
    def setHorizontalHeaderItem(self, column: int, item: typing.Optional['QStandardItem']) -> None: ...
    def horizontalHeaderItem(self, column: int) -> typing.Optional['QStandardItem']: ...
    def invisibleRootItem(self) -> typing.Optional['QStandardItem']: ...
    @typing.overload
    def setItem(self, row: int, column: int, item: typing.Optional['QStandardItem']) -> None: ...
    @typing.overload
    def setItem(self, arow: int, aitem: typing.Optional['QStandardItem']) -> None: ...
    def item(self, row: int, column: int = ...) -> typing.Optional['QStandardItem']: ...
    def indexFromItem(self, item: typing.Optional['QStandardItem']) -> QtCore.QModelIndex: ...
    def itemFromIndex(self, index: QtCore.QModelIndex) -> typing.Optional['QStandardItem']: ...
    def sort(self, column: int, order: QtCore.Qt.SortOrder = ...) -> None: ...
    def setItemData(self, index: QtCore.QModelIndex, roles: dict[int, typing.Any]) -> bool: ...
    def itemData(self, index: QtCore.QModelIndex) -> dict[int, typing.Any]: ...
    def supportedDropActions(self) -> QtCore.Qt.DropAction: ...
    def clear(self) -> None: ...
    def flags(self, index: QtCore.QModelIndex) -> QtCore.Qt.ItemFlag: ...
    def removeColumns(self, column: int, count: int, parent: QtCore.QModelIndex = ...) -> bool: ...
    def removeRows(self, row: int, count: int, parent: QtCore.QModelIndex = ...) -> bool: ...
    def insertColumns(self, column: int, count: int, parent: QtCore.QModelIndex = ...) -> bool: ...
    def insertRows(self, row: int, count: int, parent: QtCore.QModelIndex = ...) -> bool: ...
    def setHeaderData(self, section: int, orientation: QtCore.Qt.Orientation, value: typing.Any, role: int = ...) -> bool: ...
    def headerData(self, section: int, orientation: QtCore.Qt.Orientation, role: int = ...) -> typing.Any: ...
    def setData(self, index: QtCore.QModelIndex, value: typing.Any, role: int = ...) -> bool: ...
    def data(self, index: QtCore.QModelIndex, role: int = ...) -> typing.Any: ...
    def hasChildren(self, parent: QtCore.QModelIndex = ...) -> bool: ...
    def columnCount(self, parent: QtCore.QModelIndex = ...) -> int: ...
    def rowCount(self, parent: QtCore.QModelIndex = ...) -> int: ...
    @typing.overload
    def parent(self, child: QtCore.QModelIndex) -> QtCore.QModelIndex: ...
    @typing.overload
    def parent(self) -> typing.Optional[QtCore.QObject]: ...
    def index(self, row: int, column: int, parent: QtCore.QModelIndex = ...) -> QtCore.QModelIndex: ...


class QStandardItem(PyQt6.sip.wrapper):

    class ItemType(enum.Enum):
        Type = ... # type: QStandardItem.ItemType
        UserType = ... # type: QStandardItem.ItemType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, text: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, icon: QIcon, text: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, rows: int, columns: int = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QStandardItem') -> None: ...

    def __ge__(self, other: 'QStandardItem') -> bool: ...
    def clearData(self) -> None: ...
    def setUserTristate(self, tristate: bool) -> None: ...
    def isUserTristate(self) -> bool: ...
    def setAutoTristate(self, tristate: bool) -> None: ...
    def isAutoTristate(self) -> bool: ...
    def emitDataChanged(self) -> None: ...
    def appendRows(self, items: collections.abc.Iterable['QStandardItem']) -> None: ...
    def appendColumn(self, items: collections.abc.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def appendRow(self, items: collections.abc.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def appendRow(self, aitem: typing.Optional['QStandardItem']) -> None: ...
    def setAccessibleDescription(self, aaccessibleDescription: typing.Optional[str]) -> None: ...
    def setAccessibleText(self, aaccessibleText: typing.Optional[str]) -> None: ...
    def setCheckState(self, acheckState: QtCore.Qt.CheckState) -> None: ...
    def setForeground(self, abrush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    def setBackground(self, abrush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    def setTextAlignment(self, atextAlignment: QtCore.Qt.AlignmentFlag) -> None: ...
    def setFont(self, afont: QFont) -> None: ...
    def setSizeHint(self, asizeHint: QtCore.QSize) -> None: ...
    def setWhatsThis(self, awhatsThis: typing.Optional[str]) -> None: ...
    def setStatusTip(self, astatusTip: typing.Optional[str]) -> None: ...
    def setToolTip(self, atoolTip: typing.Optional[str]) -> None: ...
    def setIcon(self, aicon: QIcon) -> None: ...
    def setText(self, atext: typing.Optional[str]) -> None: ...
    def __lt__(self, other: 'QStandardItem') -> bool: ...
    def write(self, out: QtCore.QDataStream) -> None: ...
    def read(self, in_: QtCore.QDataStream) -> None: ...
    def type(self) -> int: ...
    def clone(self) -> typing.Optional['QStandardItem']: ...
    def sortChildren(self, column: int, order: QtCore.Qt.SortOrder = ...) -> None: ...
    def takeColumn(self, column: int) -> list['QStandardItem']: ...
    def takeRow(self, row: int) -> list['QStandardItem']: ...
    def takeChild(self, row: int, column: int = ...) -> typing.Optional['QStandardItem']: ...
    def removeColumns(self, column: int, count: int) -> None: ...
    def removeRows(self, row: int, count: int) -> None: ...
    def removeColumn(self, column: int) -> None: ...
    def removeRow(self, row: int) -> None: ...
    def insertColumns(self, column: int, count: int) -> None: ...
    def insertColumn(self, column: int, items: collections.abc.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def insertRows(self, row: int, count: int) -> None: ...
    @typing.overload
    def insertRows(self, row: int, items: collections.abc.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def insertRow(self, row: int, items: collections.abc.Iterable['QStandardItem']) -> None: ...
    @typing.overload
    def insertRow(self, arow: int, aitem: typing.Optional['QStandardItem']) -> None: ...
    @typing.overload
    def setChild(self, row: int, column: int, item: typing.Optional['QStandardItem']) -> None: ...
    @typing.overload
    def setChild(self, arow: int, aitem: typing.Optional['QStandardItem']) -> None: ...
    def child(self, row: int, column: int = ...) -> typing.Optional['QStandardItem']: ...
    def hasChildren(self) -> bool: ...
    def setColumnCount(self, columns: int) -> None: ...
    def columnCount(self) -> int: ...
    def setRowCount(self, rows: int) -> None: ...
    def rowCount(self) -> int: ...
    def model(self) -> typing.Optional[QStandardItemModel]: ...
    def index(self) -> QtCore.QModelIndex: ...
    def column(self) -> int: ...
    def row(self) -> int: ...
    def parent(self) -> typing.Optional['QStandardItem']: ...
    def setDropEnabled(self, dropEnabled: bool) -> None: ...
    def isDropEnabled(self) -> bool: ...
    def setDragEnabled(self, dragEnabled: bool) -> None: ...
    def isDragEnabled(self) -> bool: ...
    def setCheckable(self, checkable: bool) -> None: ...
    def isCheckable(self) -> bool: ...
    def setSelectable(self, selectable: bool) -> None: ...
    def isSelectable(self) -> bool: ...
    def setEditable(self, editable: bool) -> None: ...
    def isEditable(self) -> bool: ...
    def setEnabled(self, enabled: bool) -> None: ...
    def isEnabled(self) -> bool: ...
    def setFlags(self, flags: QtCore.Qt.ItemFlag) -> None: ...
    def flags(self) -> QtCore.Qt.ItemFlag: ...
    def accessibleDescription(self) -> str: ...
    def accessibleText(self) -> str: ...
    def checkState(self) -> QtCore.Qt.CheckState: ...
    def foreground(self) -> QBrush: ...
    def background(self) -> QBrush: ...
    def textAlignment(self) -> QtCore.Qt.AlignmentFlag: ...
    def font(self) -> QFont: ...
    def sizeHint(self) -> QtCore.QSize: ...
    def whatsThis(self) -> str: ...
    def statusTip(self) -> str: ...
    def toolTip(self) -> str: ...
    def icon(self) -> QIcon: ...
    def text(self) -> str: ...
    def setData(self, value: typing.Any, role: int = ...) -> None: ...
    def data(self, role: int = ...) -> typing.Any: ...


class QStaticText(PyQt6.sip.simplewrapper):

    class PerformanceHint(enum.Enum):
        ModerateCaching = ... # type: QStaticText.PerformanceHint
        AggressiveCaching = ... # type: QStaticText.PerformanceHint

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, text: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, other: 'QStaticText') -> None: ...

    def swap(self, other: 'QStaticText') -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def performanceHint(self) -> 'QStaticText.PerformanceHint': ...
    def setPerformanceHint(self, performanceHint: 'QStaticText.PerformanceHint') -> None: ...
    def prepare(self, matrix: 'QTransform' = ..., font: QFont = ...) -> None: ...
    def size(self) -> QtCore.QSizeF: ...
    def textOption(self) -> 'QTextOption': ...
    def setTextOption(self, textOption: 'QTextOption') -> None: ...
    def textWidth(self) -> float: ...
    def setTextWidth(self, textWidth: float) -> None: ...
    def textFormat(self) -> QtCore.Qt.TextFormat: ...
    def setTextFormat(self, textFormat: QtCore.Qt.TextFormat) -> None: ...
    def text(self) -> str: ...
    def setText(self, text: typing.Optional[str]) -> None: ...


class QStyleHints(QtCore.QObject):

    contextMenuTriggerChanged: typing.ClassVar[QtCore.pyqtSignal]
    def unsetColorScheme(self) -> None: ...
    def setColorScheme(self, scheme: QtCore.Qt.ColorScheme) -> None: ...
    def setContextMenuTrigger(self, contextMenuTrigger: QtCore.Qt.ContextMenuTrigger) -> None: ...
    def contextMenuTrigger(self) -> QtCore.Qt.ContextMenuTrigger: ...
    colorSchemeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def colorScheme(self) -> QtCore.Qt.ColorScheme: ...
    def keyboardAutoRepeatRateF(self) -> float: ...
    def touchDoubleTapDistance(self) -> int: ...
    def mouseDoubleClickDistance(self) -> int: ...
    showShortcutsInContextMenusChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setShowShortcutsInContextMenus(self, showShortcutsInContextMenus: bool) -> None: ...
    mouseQuickSelectionThresholdChanged: typing.ClassVar[QtCore.pyqtSignal]
    def mouseQuickSelectionThreshold(self) -> int: ...
    def showShortcutsInContextMenus(self) -> bool: ...
    wheelScrollLinesChanged: typing.ClassVar[QtCore.pyqtSignal]
    def wheelScrollLines(self) -> int: ...
    useHoverEffectsChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setUseHoverEffects(self, useHoverEffects: bool) -> None: ...
    def useHoverEffects(self) -> bool: ...
    def showIsMaximized(self) -> bool: ...
    tabFocusBehaviorChanged: typing.ClassVar[QtCore.pyqtSignal]
    mousePressAndHoldIntervalChanged: typing.ClassVar[QtCore.pyqtSignal]
    startDragTimeChanged: typing.ClassVar[QtCore.pyqtSignal]
    startDragDistanceChanged: typing.ClassVar[QtCore.pyqtSignal]
    mouseDoubleClickIntervalChanged: typing.ClassVar[QtCore.pyqtSignal]
    keyboardInputIntervalChanged: typing.ClassVar[QtCore.pyqtSignal]
    cursorFlashTimeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def singleClickActivation(self) -> bool: ...
    def tabFocusBehavior(self) -> QtCore.Qt.TabFocusBehavior: ...
    def mousePressAndHoldInterval(self) -> int: ...
    def setFocusOnTouchRelease(self) -> bool: ...
    def passwordMaskCharacter(self) -> str: ...
    def useRtlExtensions(self) -> bool: ...
    def fontSmoothingGamma(self) -> float: ...
    def passwordMaskDelay(self) -> int: ...
    def showIsFullScreen(self) -> bool: ...
    def cursorFlashTime(self) -> int: ...
    def keyboardAutoRepeatRate(self) -> int: ...
    def keyboardInputInterval(self) -> int: ...
    def startDragVelocity(self) -> int: ...
    def startDragTime(self) -> int: ...
    def startDragDistance(self) -> int: ...
    def mouseDoubleClickInterval(self) -> int: ...


class QSurfaceFormat(PyQt6.sip.simplewrapper):

    class OpenGLContextProfile(enum.Enum):
        NoProfile = ... # type: QSurfaceFormat.OpenGLContextProfile
        CoreProfile = ... # type: QSurfaceFormat.OpenGLContextProfile
        CompatibilityProfile = ... # type: QSurfaceFormat.OpenGLContextProfile

    class RenderableType(enum.Enum):
        DefaultRenderableType = ... # type: QSurfaceFormat.RenderableType
        OpenGL = ... # type: QSurfaceFormat.RenderableType
        OpenGLES = ... # type: QSurfaceFormat.RenderableType
        OpenVG = ... # type: QSurfaceFormat.RenderableType

    class SwapBehavior(enum.Enum):
        DefaultSwapBehavior = ... # type: QSurfaceFormat.SwapBehavior
        SingleBuffer = ... # type: QSurfaceFormat.SwapBehavior
        DoubleBuffer = ... # type: QSurfaceFormat.SwapBehavior
        TripleBuffer = ... # type: QSurfaceFormat.SwapBehavior

    class FormatOption(enum.Flag):
        StereoBuffers = ... # type: QSurfaceFormat.FormatOption
        DebugContext = ... # type: QSurfaceFormat.FormatOption
        DeprecatedFunctions = ... # type: QSurfaceFormat.FormatOption
        ResetNotification = ... # type: QSurfaceFormat.FormatOption
        ProtectedContent = ... # type: QSurfaceFormat.FormatOption

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, options: 'QSurfaceFormat.FormatOption') -> None: ...
    @typing.overload
    def __init__(self, other: 'QSurfaceFormat') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def setColorSpace(self, colorSpace: QColorSpace) -> None: ...
    def colorSpace(self) -> QColorSpace: ...
    @staticmethod
    def defaultFormat() -> 'QSurfaceFormat': ...
    @staticmethod
    def setDefaultFormat(format: 'QSurfaceFormat') -> None: ...
    def setSwapInterval(self, interval: int) -> None: ...
    def swapInterval(self) -> int: ...
    def options(self) -> 'QSurfaceFormat.FormatOption': ...
    def testOption(self, option: 'QSurfaceFormat.FormatOption') -> bool: ...
    def setOption(self, option: 'QSurfaceFormat.FormatOption', on: bool = ...) -> None: ...
    def setOptions(self, options: 'QSurfaceFormat.FormatOption') -> None: ...
    def setVersion(self, major: int, minor: int) -> None: ...
    def version(self) -> tuple[int, int]: ...
    def stereo(self) -> bool: ...
    def setStereo(self, enable: bool) -> None: ...
    def minorVersion(self) -> int: ...
    def setMinorVersion(self, minorVersion: int) -> None: ...
    def majorVersion(self) -> int: ...
    def setMajorVersion(self, majorVersion: int) -> None: ...
    def renderableType(self) -> 'QSurfaceFormat.RenderableType': ...
    def setRenderableType(self, type: 'QSurfaceFormat.RenderableType') -> None: ...
    def profile(self) -> 'QSurfaceFormat.OpenGLContextProfile': ...
    def setProfile(self, profile: 'QSurfaceFormat.OpenGLContextProfile') -> None: ...
    def hasAlpha(self) -> bool: ...
    def swapBehavior(self) -> 'QSurfaceFormat.SwapBehavior': ...
    def setSwapBehavior(self, behavior: 'QSurfaceFormat.SwapBehavior') -> None: ...
    def samples(self) -> int: ...
    def setSamples(self, numSamples: int) -> None: ...
    def alphaBufferSize(self) -> int: ...
    def setAlphaBufferSize(self, size: int) -> None: ...
    def blueBufferSize(self) -> int: ...
    def setBlueBufferSize(self, size: int) -> None: ...
    def greenBufferSize(self) -> int: ...
    def setGreenBufferSize(self, size: int) -> None: ...
    def redBufferSize(self) -> int: ...
    def setRedBufferSize(self, size: int) -> None: ...
    def stencilBufferSize(self) -> int: ...
    def setStencilBufferSize(self, size: int) -> None: ...
    def depthBufferSize(self) -> int: ...
    def setDepthBufferSize(self, size: int) -> None: ...


class QSyntaxHighlighter(QtCore.QObject):

    @typing.overload
    def __init__(self, parent: typing.Optional['QTextDocument']) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject]) -> None: ...

    def currentBlock(self) -> 'QTextBlock': ...
    def currentBlockUserData(self) -> typing.Optional['QTextBlockUserData']: ...
    def setCurrentBlockUserData(self, data: typing.Optional['QTextBlockUserData']) -> None: ...
    def setCurrentBlockState(self, newState: int) -> None: ...
    def currentBlockState(self) -> int: ...
    def previousBlockState(self) -> int: ...
    def format(self, pos: int) -> 'QTextCharFormat': ...
    @typing.overload
    def setFormat(self, start: int, count: int, format: 'QTextCharFormat') -> None: ...
    @typing.overload
    def setFormat(self, start: int, count: int, color: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def setFormat(self, start: int, count: int, font: QFont) -> None: ...
    def highlightBlock(self, text: typing.Optional[str]) -> None: ...
    def rehighlightBlock(self, block: 'QTextBlock') -> None: ...
    def rehighlight(self) -> None: ...
    def document(self) -> typing.Optional['QTextDocument']: ...
    def setDocument(self, doc: typing.Optional['QTextDocument']) -> None: ...


class QTextCursor(PyQt6.sip.simplewrapper):

    class SelectionType(enum.Enum):
        WordUnderCursor = ... # type: QTextCursor.SelectionType
        LineUnderCursor = ... # type: QTextCursor.SelectionType
        BlockUnderCursor = ... # type: QTextCursor.SelectionType
        Document = ... # type: QTextCursor.SelectionType

    class MoveOperation(enum.Enum):
        NoMove = ... # type: QTextCursor.MoveOperation
        Start = ... # type: QTextCursor.MoveOperation
        Up = ... # type: QTextCursor.MoveOperation
        StartOfLine = ... # type: QTextCursor.MoveOperation
        StartOfBlock = ... # type: QTextCursor.MoveOperation
        StartOfWord = ... # type: QTextCursor.MoveOperation
        PreviousBlock = ... # type: QTextCursor.MoveOperation
        PreviousCharacter = ... # type: QTextCursor.MoveOperation
        PreviousWord = ... # type: QTextCursor.MoveOperation
        Left = ... # type: QTextCursor.MoveOperation
        WordLeft = ... # type: QTextCursor.MoveOperation
        End = ... # type: QTextCursor.MoveOperation
        Down = ... # type: QTextCursor.MoveOperation
        EndOfLine = ... # type: QTextCursor.MoveOperation
        EndOfWord = ... # type: QTextCursor.MoveOperation
        EndOfBlock = ... # type: QTextCursor.MoveOperation
        NextBlock = ... # type: QTextCursor.MoveOperation
        NextCharacter = ... # type: QTextCursor.MoveOperation
        NextWord = ... # type: QTextCursor.MoveOperation
        Right = ... # type: QTextCursor.MoveOperation
        WordRight = ... # type: QTextCursor.MoveOperation
        NextCell = ... # type: QTextCursor.MoveOperation
        PreviousCell = ... # type: QTextCursor.MoveOperation
        NextRow = ... # type: QTextCursor.MoveOperation
        PreviousRow = ... # type: QTextCursor.MoveOperation

    class MoveMode(enum.Enum):
        MoveAnchor = ... # type: QTextCursor.MoveMode
        KeepAnchor = ... # type: QTextCursor.MoveMode

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, document: typing.Optional['QTextDocument']) -> None: ...
    @typing.overload
    def __init__(self, frame: typing.Optional['QTextFrame']) -> None: ...
    @typing.overload
    def __init__(self, block: 'QTextBlock') -> None: ...
    @typing.overload
    def __init__(self, cursor: 'QTextCursor') -> None: ...

    def insertMarkdown(self, markdown: typing.Optional[str], features: 'QTextDocument.MarkdownFeature' = ...) -> None: ...
    def swap(self, other: 'QTextCursor') -> None: ...
    def keepPositionOnInsert(self) -> bool: ...
    def setKeepPositionOnInsert(self, b: bool) -> None: ...
    def verticalMovementX(self) -> int: ...
    def setVerticalMovementX(self, x: int) -> None: ...
    def positionInBlock(self) -> int: ...
    def document(self) -> typing.Optional['QTextDocument']: ...
    def setVisualNavigation(self, b: bool) -> None: ...
    def visualNavigation(self) -> bool: ...
    def isCopyOf(self, other: 'QTextCursor') -> bool: ...
    def __gt__(self, rhs: 'QTextCursor') -> bool: ...
    def __ge__(self, rhs: 'QTextCursor') -> bool: ...
    def __eq__(self, other: object): ...
    def __le__(self, rhs: 'QTextCursor') -> bool: ...
    def __lt__(self, rhs: 'QTextCursor') -> bool: ...
    def __ne__(self, other: object): ...
    def columnNumber(self) -> int: ...
    def blockNumber(self) -> int: ...
    def endEditBlock(self) -> None: ...
    def joinPreviousEditBlock(self) -> None: ...
    def beginEditBlock(self) -> None: ...
    @typing.overload
    def insertImage(self, format: 'QTextImageFormat') -> None: ...
    @typing.overload
    def insertImage(self, format: 'QTextImageFormat', alignment: 'QTextFrameFormat.Position') -> None: ...
    @typing.overload
    def insertImage(self, name: typing.Optional[str]) -> None: ...
    @typing.overload
    def insertImage(self, image: QImage, name: typing.Optional[str] = ...) -> None: ...
    def insertHtml(self, html: typing.Optional[str]) -> None: ...
    def insertFragment(self, fragment: 'QTextDocumentFragment') -> None: ...
    def currentFrame(self) -> typing.Optional['QTextFrame']: ...
    def insertFrame(self, format: 'QTextFrameFormat') -> typing.Optional['QTextFrame']: ...
    def currentTable(self) -> typing.Optional['QTextTable']: ...
    @typing.overload
    def insertTable(self, rows: int, cols: int, format: 'QTextTableFormat') -> typing.Optional['QTextTable']: ...
    @typing.overload
    def insertTable(self, rows: int, cols: int) -> typing.Optional['QTextTable']: ...
    def currentList(self) -> typing.Optional['QTextList']: ...
    @typing.overload
    def createList(self, format: 'QTextListFormat') -> typing.Optional['QTextList']: ...
    @typing.overload
    def createList(self, style: 'QTextListFormat.Style') -> typing.Optional['QTextList']: ...
    @typing.overload
    def insertList(self, format: 'QTextListFormat') -> typing.Optional['QTextList']: ...
    @typing.overload
    def insertList(self, style: 'QTextListFormat.Style') -> typing.Optional['QTextList']: ...
    @typing.overload
    def insertBlock(self) -> None: ...
    @typing.overload
    def insertBlock(self, format: 'QTextBlockFormat') -> None: ...
    @typing.overload
    def insertBlock(self, format: 'QTextBlockFormat', charFormat: 'QTextCharFormat') -> None: ...
    def atEnd(self) -> bool: ...
    def atStart(self) -> bool: ...
    def atBlockEnd(self) -> bool: ...
    def atBlockStart(self) -> bool: ...
    def mergeBlockCharFormat(self, modifier: 'QTextCharFormat') -> None: ...
    def setBlockCharFormat(self, format: 'QTextCharFormat') -> None: ...
    def blockCharFormat(self) -> 'QTextCharFormat': ...
    def mergeBlockFormat(self, modifier: 'QTextBlockFormat') -> None: ...
    def setBlockFormat(self, format: 'QTextBlockFormat') -> None: ...
    def blockFormat(self) -> 'QTextBlockFormat': ...
    def mergeCharFormat(self, modifier: 'QTextCharFormat') -> None: ...
    def setCharFormat(self, format: 'QTextCharFormat') -> None: ...
    def charFormat(self) -> 'QTextCharFormat': ...
    def block(self) -> 'QTextBlock': ...
    def selectedTableCells(self) -> typing.Tuple[typing.Optional[int], typing.Optional[int], typing.Optional[int], typing.Optional[int]]: ...
    def selection(self) -> 'QTextDocumentFragment': ...
    def selectedText(self) -> str: ...
    def selectionEnd(self) -> int: ...
    def selectionStart(self) -> int: ...
    def clearSelection(self) -> None: ...
    def removeSelectedText(self) -> None: ...
    def hasComplexSelection(self) -> bool: ...
    def hasSelection(self) -> bool: ...
    def select(self, selection: 'QTextCursor.SelectionType') -> None: ...
    def deletePreviousChar(self) -> None: ...
    def deleteChar(self) -> None: ...
    def movePosition(self, op: 'QTextCursor.MoveOperation', mode: 'QTextCursor.MoveMode' = ..., n: int = ...) -> bool: ...
    @typing.overload
    def insertText(self, text: typing.Optional[str]) -> None: ...
    @typing.overload
    def insertText(self, text: typing.Optional[str], format: 'QTextCharFormat') -> None: ...
    def anchor(self) -> int: ...
    def position(self) -> int: ...
    def setPosition(self, pos: int, mode: 'QTextCursor.MoveMode' = ...) -> None: ...
    def isNull(self) -> bool: ...


class QTextDocument(QtCore.QObject):

    class MarkdownFeature(enum.Flag):
        MarkdownNoHTML = ... # type: QTextDocument.MarkdownFeature
        MarkdownDialectCommonMark = ... # type: QTextDocument.MarkdownFeature
        MarkdownDialectGitHub = ... # type: QTextDocument.MarkdownFeature

    class Stacks(enum.Enum):
        UndoStack = ... # type: QTextDocument.Stacks
        RedoStack = ... # type: QTextDocument.Stacks
        UndoAndRedoStacks = ... # type: QTextDocument.Stacks

    class ResourceType(enum.IntEnum):
        UnknownResource = ... # type: QTextDocument.ResourceType
        HtmlResource = ... # type: QTextDocument.ResourceType
        ImageResource = ... # type: QTextDocument.ResourceType
        StyleSheetResource = ... # type: QTextDocument.ResourceType
        MarkdownResource = ... # type: QTextDocument.ResourceType
        UserResource = ... # type: QTextDocument.ResourceType

    class FindFlag(enum.Flag):
        FindBackward = ... # type: QTextDocument.FindFlag
        FindCaseSensitively = ... # type: QTextDocument.FindFlag
        FindWholeWords = ... # type: QTextDocument.FindFlag

    class MetaInformation(enum.Enum):
        DocumentTitle = ... # type: QTextDocument.MetaInformation
        DocumentUrl = ... # type: QTextDocument.MetaInformation
        CssMedia = ... # type: QTextDocument.MetaInformation
        FrontMatter = ... # type: QTextDocument.MetaInformation

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, text: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def isLayoutEnabled(self) -> bool: ...
    def setLayoutEnabled(self, b: bool) -> None: ...
    @staticmethod
    def setDefaultResourceProvider(provider: collections.abc.Callable[[QtCore.QUrl], typing.Any]) -> None: ...
    @staticmethod
    def defaultResourceProvider() -> collections.abc.Callable[[QtCore.QUrl], typing.Any]: ...
    def setResourceProvider(self, provider: collections.abc.Callable[[QtCore.QUrl], typing.Any]) -> None: ...
    def resourceProvider(self) -> collections.abc.Callable[[QtCore.QUrl], typing.Any]: ...
    def baselineOffset(self) -> float: ...
    def setBaselineOffset(self, baseline: float) -> None: ...
    def subScriptBaseline(self) -> float: ...
    def setSubScriptBaseline(self, baseline: float) -> None: ...
    def superScriptBaseline(self) -> float: ...
    def setSuperScriptBaseline(self, baseline: float) -> None: ...
    def setMarkdown(self, markdown: typing.Optional[str], features: 'QTextDocument.MarkdownFeature' = ...) -> None: ...
    def toMarkdown(self, features: 'QTextDocument.MarkdownFeature' = ...) -> str: ...
    def toRawText(self) -> str: ...
    baseUrlChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setBaseUrl(self, url: QtCore.QUrl) -> None: ...
    def baseUrl(self) -> QtCore.QUrl: ...
    def setDefaultCursorMoveStyle(self, style: QtCore.Qt.CursorMoveStyle) -> None: ...
    def defaultCursorMoveStyle(self) -> QtCore.Qt.CursorMoveStyle: ...
    def clearUndoRedoStacks(self, stacks: 'QTextDocument.Stacks' = ...) -> None: ...
    def availableRedoSteps(self) -> int: ...
    def availableUndoSteps(self) -> int: ...
    def characterCount(self) -> int: ...
    def lineCount(self) -> int: ...
    def setDocumentMargin(self, margin: float) -> None: ...
    def documentMargin(self) -> float: ...
    def characterAt(self, pos: int) -> str: ...
    documentLayoutChanged: typing.ClassVar[QtCore.pyqtSignal]
    undoCommandAdded: typing.ClassVar[QtCore.pyqtSignal]
    def setIndentWidth(self, width: float) -> None: ...
    def indentWidth(self) -> float: ...
    def lastBlock(self) -> 'QTextBlock': ...
    def firstBlock(self) -> 'QTextBlock': ...
    def findBlockByLineNumber(self, blockNumber: int) -> 'QTextBlock': ...
    def findBlockByNumber(self, blockNumber: int) -> 'QTextBlock': ...
    def revision(self) -> int: ...
    def setDefaultTextOption(self, option: 'QTextOption') -> None: ...
    def defaultTextOption(self) -> 'QTextOption': ...
    def setMaximumBlockCount(self, maximum: int) -> None: ...
    def maximumBlockCount(self) -> int: ...
    def defaultStyleSheet(self) -> str: ...
    def setDefaultStyleSheet(self, sheet: typing.Optional[str]) -> None: ...
    def blockCount(self) -> int: ...
    def size(self) -> QtCore.QSizeF: ...
    def adjustSize(self) -> None: ...
    def idealWidth(self) -> float: ...
    def textWidth(self) -> float: ...
    def setTextWidth(self, width: float) -> None: ...
    def drawContents(self, p: typing.Optional[QPainter], rect: QtCore.QRectF = ...) -> None: ...
    def loadResource(self, type: int, name: QtCore.QUrl) -> typing.Any: ...
    def createObject(self, f: 'QTextFormat') -> typing.Optional['QTextObject']: ...
    def setModified(self, on: bool = ...) -> None: ...
    @typing.overload
    def redo(self) -> None: ...
    @typing.overload
    def redo(self, cursor: typing.Optional[QTextCursor]) -> None: ...
    @typing.overload
    def undo(self) -> None: ...
    @typing.overload
    def undo(self, cursor: typing.Optional[QTextCursor]) -> None: ...
    undoAvailable: typing.ClassVar[QtCore.pyqtSignal]
    redoAvailable: typing.ClassVar[QtCore.pyqtSignal]
    modificationChanged: typing.ClassVar[QtCore.pyqtSignal]
    cursorPositionChanged: typing.ClassVar[QtCore.pyqtSignal]
    contentsChanged: typing.ClassVar[QtCore.pyqtSignal]
    contentsChange: typing.ClassVar[QtCore.pyqtSignal]
    blockCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    def useDesignMetrics(self) -> bool: ...
    def setUseDesignMetrics(self, b: bool) -> None: ...
    def markContentsDirty(self, from_: int, length: int) -> None: ...
    def allFormats(self) -> list['QTextFormat']: ...
    def addResource(self, type: int, name: QtCore.QUrl, resource: typing.Any) -> None: ...
    def resource(self, type: int, name: QtCore.QUrl) -> typing.Any: ...
    def print(self, printer: typing.Optional[QPagedPaintDevice]) -> None: ...
    def isModified(self) -> bool: ...
    def pageCount(self) -> int: ...
    def defaultFont(self) -> QFont: ...
    def setDefaultFont(self, font: QFont) -> None: ...
    def pageSize(self) -> QtCore.QSizeF: ...
    def setPageSize(self, size: QtCore.QSizeF) -> None: ...
    def end(self) -> 'QTextBlock': ...
    def begin(self) -> 'QTextBlock': ...
    def findBlock(self, pos: int) -> 'QTextBlock': ...
    def objectForFormat(self, a0: 'QTextFormat') -> typing.Optional['QTextObject']: ...
    def object(self, objectIndex: int) -> typing.Optional['QTextObject']: ...
    def rootFrame(self) -> typing.Optional['QTextFrame']: ...
    @typing.overload
    def find(self, expr: QtCore.QRegularExpression, cursor: QTextCursor, options: 'QTextDocument.FindFlag' = ...) -> QTextCursor: ...
    @typing.overload
    def find(self, expr: QtCore.QRegularExpression, position: int = ..., options: 'QTextDocument.FindFlag' = ...) -> QTextCursor: ...
    @typing.overload
    def find(self, subString: typing.Optional[str], cursor: QTextCursor, options: 'QTextDocument.FindFlag' = ...) -> QTextCursor: ...
    @typing.overload
    def find(self, subString: typing.Optional[str], position: int = ..., options: 'QTextDocument.FindFlag' = ...) -> QTextCursor: ...
    def setPlainText(self, text: typing.Optional[str]) -> None: ...
    def toPlainText(self) -> str: ...
    def setHtml(self, html: typing.Optional[str]) -> None: ...
    def toHtml(self) -> str: ...
    def metaInformation(self, info: 'QTextDocument.MetaInformation') -> str: ...
    def setMetaInformation(self, info: 'QTextDocument.MetaInformation', a1: typing.Optional[str]) -> None: ...
    def documentLayout(self) -> typing.Optional[QAbstractTextDocumentLayout]: ...
    def setDocumentLayout(self, layout: typing.Optional[QAbstractTextDocumentLayout]) -> None: ...
    def isRedoAvailable(self) -> bool: ...
    def isUndoAvailable(self) -> bool: ...
    def isUndoRedoEnabled(self) -> bool: ...
    def setUndoRedoEnabled(self, enable: bool) -> None: ...
    def clear(self) -> None: ...
    def isEmpty(self) -> bool: ...
    def clone(self, parent: typing.Optional[QtCore.QObject] = ...) -> typing.Optional['QTextDocument']: ...


class QTextDocumentFragment(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, document: typing.Optional[QTextDocument]) -> None: ...
    @typing.overload
    def __init__(self, range: QTextCursor) -> None: ...
    @typing.overload
    def __init__(self, rhs: 'QTextDocumentFragment') -> None: ...

    @staticmethod
    def fromMarkdown(markdown: typing.Optional[str], features: QTextDocument.MarkdownFeature = ...) -> 'QTextDocumentFragment': ...
    def toMarkdown(self, features: QTextDocument.MarkdownFeature = ...) -> str: ...
    def toRawText(self) -> str: ...
    @staticmethod
    def fromHtml(html: typing.Optional[str], resourceProvider: typing.Optional[QTextDocument] = ...) -> 'QTextDocumentFragment': ...
    @staticmethod
    def fromPlainText(plainText: typing.Optional[str]) -> 'QTextDocumentFragment': ...
    def toHtml(self) -> str: ...
    def toPlainText(self) -> str: ...
    def isEmpty(self) -> bool: ...


class QTextDocumentWriter(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, device: typing.Optional[QtCore.QIODevice], format: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str], format: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ...) -> None: ...

    @staticmethod
    def supportedDocumentFormats() -> list[QtCore.QByteArray]: ...
    @typing.overload
    def write(self, document: typing.Optional[QTextDocument]) -> bool: ...
    @typing.overload
    def write(self, fragment: QTextDocumentFragment) -> bool: ...
    def fileName(self) -> str: ...
    def setFileName(self, fileName: typing.Optional[str]) -> None: ...
    def device(self) -> typing.Optional[QtCore.QIODevice]: ...
    def setDevice(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...
    def format(self) -> QtCore.QByteArray: ...
    def setFormat(self, format: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...


class QTextLength(PyQt6.sip.simplewrapper):

    class Type(enum.Enum):
        VariableLength = ... # type: QTextLength.Type
        FixedLength = ... # type: QTextLength.Type
        PercentageLength = ... # type: QTextLength.Type

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, atype: 'QTextLength.Type', avalue: float) -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextLength') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def rawValue(self) -> float: ...
    def value(self, maximumLength: float) -> float: ...
    def type(self) -> 'QTextLength.Type': ...


class QTextFormat(PyQt6.sip.simplewrapper):

    class Property(enum.IntEnum):
        ObjectIndex = ... # type: QTextFormat.Property
        CssFloat = ... # type: QTextFormat.Property
        LayoutDirection = ... # type: QTextFormat.Property
        OutlinePen = ... # type: QTextFormat.Property
        BackgroundBrush = ... # type: QTextFormat.Property
        ForegroundBrush = ... # type: QTextFormat.Property
        BlockAlignment = ... # type: QTextFormat.Property
        BlockTopMargin = ... # type: QTextFormat.Property
        BlockBottomMargin = ... # type: QTextFormat.Property
        BlockLeftMargin = ... # type: QTextFormat.Property
        BlockRightMargin = ... # type: QTextFormat.Property
        TextIndent = ... # type: QTextFormat.Property
        BlockIndent = ... # type: QTextFormat.Property
        BlockNonBreakableLines = ... # type: QTextFormat.Property
        BlockTrailingHorizontalRulerWidth = ... # type: QTextFormat.Property
        FontPointSize = ... # type: QTextFormat.Property
        FontSizeAdjustment = ... # type: QTextFormat.Property
        FontSizeIncrement = ... # type: QTextFormat.Property
        FontWeight = ... # type: QTextFormat.Property
        FontItalic = ... # type: QTextFormat.Property
        FontUnderline = ... # type: QTextFormat.Property
        FontOverline = ... # type: QTextFormat.Property
        FontStrikeOut = ... # type: QTextFormat.Property
        FontFixedPitch = ... # type: QTextFormat.Property
        FontPixelSize = ... # type: QTextFormat.Property
        TextUnderlineColor = ... # type: QTextFormat.Property
        TextVerticalAlignment = ... # type: QTextFormat.Property
        TextOutline = ... # type: QTextFormat.Property
        IsAnchor = ... # type: QTextFormat.Property
        AnchorHref = ... # type: QTextFormat.Property
        AnchorName = ... # type: QTextFormat.Property
        ObjectType = ... # type: QTextFormat.Property
        ListStyle = ... # type: QTextFormat.Property
        ListIndent = ... # type: QTextFormat.Property
        FrameBorder = ... # type: QTextFormat.Property
        FrameMargin = ... # type: QTextFormat.Property
        FramePadding = ... # type: QTextFormat.Property
        FrameWidth = ... # type: QTextFormat.Property
        FrameHeight = ... # type: QTextFormat.Property
        TableColumns = ... # type: QTextFormat.Property
        TableColumnWidthConstraints = ... # type: QTextFormat.Property
        TableCellSpacing = ... # type: QTextFormat.Property
        TableCellPadding = ... # type: QTextFormat.Property
        TableCellRowSpan = ... # type: QTextFormat.Property
        TableCellColumnSpan = ... # type: QTextFormat.Property
        ImageName = ... # type: QTextFormat.Property
        ImageWidth = ... # type: QTextFormat.Property
        ImageHeight = ... # type: QTextFormat.Property
        TextUnderlineStyle = ... # type: QTextFormat.Property
        TableHeaderRowCount = ... # type: QTextFormat.Property
        FullWidthSelection = ... # type: QTextFormat.Property
        PageBreakPolicy = ... # type: QTextFormat.Property
        TextToolTip = ... # type: QTextFormat.Property
        FrameTopMargin = ... # type: QTextFormat.Property
        FrameBottomMargin = ... # type: QTextFormat.Property
        FrameLeftMargin = ... # type: QTextFormat.Property
        FrameRightMargin = ... # type: QTextFormat.Property
        FrameBorderBrush = ... # type: QTextFormat.Property
        FrameBorderStyle = ... # type: QTextFormat.Property
        BackgroundImageUrl = ... # type: QTextFormat.Property
        TabPositions = ... # type: QTextFormat.Property
        FirstFontProperty = ... # type: QTextFormat.Property
        FontCapitalization = ... # type: QTextFormat.Property
        FontLetterSpacing = ... # type: QTextFormat.Property
        FontWordSpacing = ... # type: QTextFormat.Property
        LastFontProperty = ... # type: QTextFormat.Property
        TableCellTopPadding = ... # type: QTextFormat.Property
        TableCellBottomPadding = ... # type: QTextFormat.Property
        TableCellLeftPadding = ... # type: QTextFormat.Property
        TableCellRightPadding = ... # type: QTextFormat.Property
        FontStyleHint = ... # type: QTextFormat.Property
        FontStyleStrategy = ... # type: QTextFormat.Property
        FontKerning = ... # type: QTextFormat.Property
        LineHeight = ... # type: QTextFormat.Property
        LineHeightType = ... # type: QTextFormat.Property
        FontHintingPreference = ... # type: QTextFormat.Property
        ListNumberPrefix = ... # type: QTextFormat.Property
        ListNumberSuffix = ... # type: QTextFormat.Property
        FontStretch = ... # type: QTextFormat.Property
        FontLetterSpacingType = ... # type: QTextFormat.Property
        HeadingLevel = ... # type: QTextFormat.Property
        ImageQuality = ... # type: QTextFormat.Property
        FontFamilies = ... # type: QTextFormat.Property
        FontStyleName = ... # type: QTextFormat.Property
        BlockQuoteLevel = ... # type: QTextFormat.Property
        BlockCodeLanguage = ... # type: QTextFormat.Property
        BlockCodeFence = ... # type: QTextFormat.Property
        BlockMarker = ... # type: QTextFormat.Property
        TableBorderCollapse = ... # type: QTextFormat.Property
        TableCellTopBorder = ... # type: QTextFormat.Property
        TableCellBottomBorder = ... # type: QTextFormat.Property
        TableCellLeftBorder = ... # type: QTextFormat.Property
        TableCellRightBorder = ... # type: QTextFormat.Property
        TableCellTopBorderStyle = ... # type: QTextFormat.Property
        TableCellBottomBorderStyle = ... # type: QTextFormat.Property
        TableCellLeftBorderStyle = ... # type: QTextFormat.Property
        TableCellRightBorderStyle = ... # type: QTextFormat.Property
        TableCellTopBorderBrush = ... # type: QTextFormat.Property
        TableCellBottomBorderBrush = ... # type: QTextFormat.Property
        TableCellLeftBorderBrush = ... # type: QTextFormat.Property
        TableCellRightBorderBrush = ... # type: QTextFormat.Property
        ImageTitle = ... # type: QTextFormat.Property
        ImageAltText = ... # type: QTextFormat.Property
        TextSuperScriptBaseline = ... # type: QTextFormat.Property
        TextSubScriptBaseline = ... # type: QTextFormat.Property
        TextBaselineOffset = ... # type: QTextFormat.Property
        OldFontLetterSpacingType = ... # type: QTextFormat.Property
        OldFontStretch = ... # type: QTextFormat.Property
        OldTextUnderlineColor = ... # type: QTextFormat.Property
        OldFontFamily = ... # type: QTextFormat.Property
        ListStart = ... # type: QTextFormat.Property
        ImageMaxWidth = ... # type: QTextFormat.Property
        UserProperty = ... # type: QTextFormat.Property

    class PageBreakFlag(enum.Flag):
        PageBreak_Auto = ... # type: QTextFormat.PageBreakFlag
        PageBreak_AlwaysBefore = ... # type: QTextFormat.PageBreakFlag
        PageBreak_AlwaysAfter = ... # type: QTextFormat.PageBreakFlag

    class ObjectTypes(enum.IntEnum):
        NoObject = ... # type: QTextFormat.ObjectTypes
        ImageObject = ... # type: QTextFormat.ObjectTypes
        TableObject = ... # type: QTextFormat.ObjectTypes
        TableCellObject = ... # type: QTextFormat.ObjectTypes
        UserObject = ... # type: QTextFormat.ObjectTypes

    class FormatType(enum.IntEnum):
        InvalidFormat = ... # type: QTextFormat.FormatType
        BlockFormat = ... # type: QTextFormat.FormatType
        CharFormat = ... # type: QTextFormat.FormatType
        ListFormat = ... # type: QTextFormat.FormatType
        FrameFormat = ... # type: QTextFormat.FormatType
        UserFormat = ... # type: QTextFormat.FormatType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, type: int) -> None: ...
    @typing.overload
    def __init__(self, rhs: 'QTextFormat') -> None: ...
    @typing.overload
    def __init__(self, variant: typing.Any) -> None: ...

    def isEmpty(self) -> bool: ...
    def swap(self, other: 'QTextFormat') -> None: ...
    def toTableCellFormat(self) -> 'QTextTableCellFormat': ...
    def isTableCellFormat(self) -> bool: ...
    def propertyCount(self) -> int: ...
    def setObjectType(self, atype: int) -> None: ...
    def clearForeground(self) -> None: ...
    def foreground(self) -> QBrush: ...
    def setForeground(self, brush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    def clearBackground(self) -> None: ...
    def background(self) -> QBrush: ...
    def setBackground(self, brush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    def layoutDirection(self) -> QtCore.Qt.LayoutDirection: ...
    def setLayoutDirection(self, direction: QtCore.Qt.LayoutDirection) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def toImageFormat(self) -> 'QTextImageFormat': ...
    def toFrameFormat(self) -> 'QTextFrameFormat': ...
    def toTableFormat(self) -> 'QTextTableFormat': ...
    def toListFormat(self) -> 'QTextListFormat': ...
    def toCharFormat(self) -> 'QTextCharFormat': ...
    def toBlockFormat(self) -> 'QTextBlockFormat': ...
    def isTableFormat(self) -> bool: ...
    def isImageFormat(self) -> bool: ...
    def isFrameFormat(self) -> bool: ...
    def isListFormat(self) -> bool: ...
    def isBlockFormat(self) -> bool: ...
    def isCharFormat(self) -> bool: ...
    def objectType(self) -> int: ...
    def properties(self) -> dict[int, typing.Any]: ...
    def lengthVectorProperty(self, propertyId: int) -> list[QTextLength]: ...
    def lengthProperty(self, propertyId: int) -> QTextLength: ...
    def brushProperty(self, propertyId: int) -> QBrush: ...
    def penProperty(self, propertyId: int) -> QPen: ...
    def colorProperty(self, propertyId: int) -> QColor: ...
    def stringProperty(self, propertyId: int) -> str: ...
    def doubleProperty(self, propertyId: int) -> float: ...
    def intProperty(self, propertyId: int) -> int: ...
    def boolProperty(self, propertyId: int) -> bool: ...
    def hasProperty(self, propertyId: int) -> bool: ...
    def clearProperty(self, propertyId: int) -> None: ...
    @typing.overload
    def setProperty(self, propertyId: int, lengths: collections.abc.Iterable[QTextLength]) -> None: ...
    @typing.overload
    def setProperty(self, propertyId: int, value: typing.Any) -> None: ...
    def property(self, propertyId: int) -> typing.Any: ...
    def setObjectIndex(self, object: int) -> None: ...
    def objectIndex(self) -> int: ...
    def type(self) -> int: ...
    def isValid(self) -> bool: ...
    def merge(self, other: 'QTextFormat') -> None: ...


class QTextCharFormat(QTextFormat):

    class FontPropertiesInheritanceBehavior(enum.Enum):
        FontPropertiesSpecifiedOnly = ... # type: QTextCharFormat.FontPropertiesInheritanceBehavior
        FontPropertiesAll = ... # type: QTextCharFormat.FontPropertiesInheritanceBehavior

    class UnderlineStyle(enum.Enum):
        NoUnderline = ... # type: QTextCharFormat.UnderlineStyle
        SingleUnderline = ... # type: QTextCharFormat.UnderlineStyle
        DashUnderline = ... # type: QTextCharFormat.UnderlineStyle
        DotLine = ... # type: QTextCharFormat.UnderlineStyle
        DashDotLine = ... # type: QTextCharFormat.UnderlineStyle
        DashDotDotLine = ... # type: QTextCharFormat.UnderlineStyle
        WaveUnderline = ... # type: QTextCharFormat.UnderlineStyle
        SpellCheckUnderline = ... # type: QTextCharFormat.UnderlineStyle

    class VerticalAlignment(enum.Enum):
        AlignNormal = ... # type: QTextCharFormat.VerticalAlignment
        AlignSuperScript = ... # type: QTextCharFormat.VerticalAlignment
        AlignSubScript = ... # type: QTextCharFormat.VerticalAlignment
        AlignMiddle = ... # type: QTextCharFormat.VerticalAlignment
        AlignTop = ... # type: QTextCharFormat.VerticalAlignment
        AlignBottom = ... # type: QTextCharFormat.VerticalAlignment
        AlignBaseline = ... # type: QTextCharFormat.VerticalAlignment

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextCharFormat') -> None: ...

    def baselineOffset(self) -> float: ...
    def setBaselineOffset(self, baseline: float) -> None: ...
    def subScriptBaseline(self) -> float: ...
    def setSubScriptBaseline(self, baseline: float) -> None: ...
    def superScriptBaseline(self) -> float: ...
    def setSuperScriptBaseline(self, baseline: float) -> None: ...
    def fontStyleName(self) -> typing.Any: ...
    def setFontStyleName(self, styleName: typing.Optional[str]) -> None: ...
    def fontFamilies(self) -> typing.Any: ...
    def setFontFamilies(self, families: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    def fontLetterSpacingType(self) -> QFont.SpacingType: ...
    def setFontLetterSpacingType(self, letterSpacingType: QFont.SpacingType) -> None: ...
    def setFontStretch(self, factor: int) -> None: ...
    def fontStretch(self) -> int: ...
    def fontHintingPreference(self) -> QFont.HintingPreference: ...
    def setFontHintingPreference(self, hintingPreference: QFont.HintingPreference) -> None: ...
    def fontKerning(self) -> bool: ...
    def setFontKerning(self, enable: bool) -> None: ...
    def fontStyleStrategy(self) -> QFont.StyleStrategy: ...
    def fontStyleHint(self) -> QFont.StyleHint: ...
    def setFontStyleStrategy(self, strategy: QFont.StyleStrategy) -> None: ...
    def setFontStyleHint(self, hint: QFont.StyleHint, strategy: QFont.StyleStrategy = ...) -> None: ...
    def fontWordSpacing(self) -> float: ...
    def setFontWordSpacing(self, spacing: float) -> None: ...
    def fontLetterSpacing(self) -> float: ...
    def setFontLetterSpacing(self, spacing: float) -> None: ...
    def fontCapitalization(self) -> QFont.Capitalization: ...
    def setFontCapitalization(self, capitalization: QFont.Capitalization) -> None: ...
    def anchorNames(self) -> list[str]: ...
    def setAnchorNames(self, names: collections.abc.Iterable[typing.Optional[str]]) -> None: ...
    def toolTip(self) -> str: ...
    def setToolTip(self, tip: typing.Optional[str]) -> None: ...
    def underlineStyle(self) -> 'QTextCharFormat.UnderlineStyle': ...
    def setUnderlineStyle(self, style: 'QTextCharFormat.UnderlineStyle') -> None: ...
    def textOutline(self) -> QPen: ...
    def setTextOutline(self, pen: typing.Union[QPen, typing.Union[QColor, QtCore.Qt.GlobalColor, int]]) -> None: ...
    def setTableCellColumnSpan(self, atableCellColumnSpan: int) -> None: ...
    def setTableCellRowSpan(self, atableCellRowSpan: int) -> None: ...
    def tableCellColumnSpan(self) -> int: ...
    def tableCellRowSpan(self) -> int: ...
    def anchorHref(self) -> str: ...
    def setAnchorHref(self, value: typing.Optional[str]) -> None: ...
    def isAnchor(self) -> bool: ...
    def setAnchor(self, anchor: bool) -> None: ...
    def verticalAlignment(self) -> 'QTextCharFormat.VerticalAlignment': ...
    def setVerticalAlignment(self, alignment: 'QTextCharFormat.VerticalAlignment') -> None: ...
    def fontFixedPitch(self) -> bool: ...
    def setFontFixedPitch(self, fixedPitch: bool) -> None: ...
    def underlineColor(self) -> QColor: ...
    def setUnderlineColor(self, color: typing.Union[QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def fontStrikeOut(self) -> bool: ...
    def setFontStrikeOut(self, strikeOut: bool) -> None: ...
    def fontOverline(self) -> bool: ...
    def setFontOverline(self, overline: bool) -> None: ...
    def fontUnderline(self) -> bool: ...
    def setFontUnderline(self, underline: bool) -> None: ...
    def fontItalic(self) -> bool: ...
    def setFontItalic(self, italic: bool) -> None: ...
    def fontWeight(self) -> int: ...
    def setFontWeight(self, weight: int) -> None: ...
    def fontPointSize(self) -> float: ...
    def setFontPointSize(self, size: float) -> None: ...
    def fontFamily(self) -> str: ...
    def setFontFamily(self, family: typing.Optional[str]) -> None: ...
    def font(self) -> QFont: ...
    def setFont(self, font: QFont, behavior: 'QTextCharFormat.FontPropertiesInheritanceBehavior' = ...) -> None: ...
    def isValid(self) -> bool: ...


class QTextBlockFormat(QTextFormat):

    class MarkerType(enum.Enum):
        NoMarker = ... # type: QTextBlockFormat.MarkerType
        Unchecked = ... # type: QTextBlockFormat.MarkerType
        Checked = ... # type: QTextBlockFormat.MarkerType

    class LineHeightTypes(enum.Enum):
        SingleHeight = ... # type: QTextBlockFormat.LineHeightTypes
        ProportionalHeight = ... # type: QTextBlockFormat.LineHeightTypes
        FixedHeight = ... # type: QTextBlockFormat.LineHeightTypes
        MinimumHeight = ... # type: QTextBlockFormat.LineHeightTypes
        LineDistanceHeight = ... # type: QTextBlockFormat.LineHeightTypes

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextBlockFormat') -> None: ...

    def marker(self) -> 'QTextBlockFormat.MarkerType': ...
    def setMarker(self, marker: 'QTextBlockFormat.MarkerType') -> None: ...
    def headingLevel(self) -> int: ...
    def setHeadingLevel(self, alevel: int) -> None: ...
    def lineHeightType(self) -> int: ...
    @typing.overload
    def lineHeight(self) -> float: ...
    @typing.overload
    def lineHeight(self, scriptLineHeight: float, scaling: float = ...) -> float: ...
    def setLineHeight(self, height: float, heightType: int) -> None: ...
    def tabPositions(self) -> list['QTextOption.Tab']: ...
    def setTabPositions(self, tabs: collections.abc.Iterable['QTextOption.Tab']) -> None: ...
    def pageBreakPolicy(self) -> QTextFormat.PageBreakFlag: ...
    def setPageBreakPolicy(self, flags: QTextFormat.PageBreakFlag) -> None: ...
    def setIndent(self, aindent: int) -> None: ...
    def setAlignment(self, aalignment: QtCore.Qt.AlignmentFlag) -> None: ...
    def nonBreakableLines(self) -> bool: ...
    def setNonBreakableLines(self, b: bool) -> None: ...
    def indent(self) -> int: ...
    def textIndent(self) -> float: ...
    def setTextIndent(self, margin: float) -> None: ...
    def rightMargin(self) -> float: ...
    def setRightMargin(self, margin: float) -> None: ...
    def leftMargin(self) -> float: ...
    def setLeftMargin(self, margin: float) -> None: ...
    def bottomMargin(self) -> float: ...
    def setBottomMargin(self, margin: float) -> None: ...
    def topMargin(self) -> float: ...
    def setTopMargin(self, margin: float) -> None: ...
    def alignment(self) -> QtCore.Qt.AlignmentFlag: ...
    def isValid(self) -> bool: ...


class QTextListFormat(QTextFormat):

    class Style(enum.Enum):
        ListDisc = ... # type: QTextListFormat.Style
        ListCircle = ... # type: QTextListFormat.Style
        ListSquare = ... # type: QTextListFormat.Style
        ListDecimal = ... # type: QTextListFormat.Style
        ListLowerAlpha = ... # type: QTextListFormat.Style
        ListUpperAlpha = ... # type: QTextListFormat.Style
        ListLowerRoman = ... # type: QTextListFormat.Style
        ListUpperRoman = ... # type: QTextListFormat.Style

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextListFormat') -> None: ...

    def start(self) -> int: ...
    def setStart(self, indent: int) -> None: ...
    def setNumberSuffix(self, ns: typing.Optional[str]) -> None: ...
    def setNumberPrefix(self, np: typing.Optional[str]) -> None: ...
    def numberSuffix(self) -> str: ...
    def numberPrefix(self) -> str: ...
    def setIndent(self, aindent: int) -> None: ...
    def setStyle(self, astyle: 'QTextListFormat.Style') -> None: ...
    def indent(self) -> int: ...
    def style(self) -> 'QTextListFormat.Style': ...
    def isValid(self) -> bool: ...


class QTextImageFormat(QTextCharFormat):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextImageFormat') -> None: ...

    def maximumWidth(self) -> QTextLength: ...
    def setMaximumWidth(self, maxWidth: QTextLength) -> None: ...
    def setQuality(self, quality: int = ...) -> None: ...
    def setHeight(self, aheight: float) -> None: ...
    def setWidth(self, awidth: float) -> None: ...
    def setName(self, aname: typing.Optional[str]) -> None: ...
    def quality(self) -> int: ...
    def height(self) -> float: ...
    def width(self) -> float: ...
    def name(self) -> str: ...
    def isValid(self) -> bool: ...


class QTextFrameFormat(QTextFormat):

    class BorderStyle(enum.Enum):
        BorderStyle_None = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Dotted = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Dashed = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Solid = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Double = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_DotDash = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_DotDotDash = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Groove = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Ridge = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Inset = ... # type: QTextFrameFormat.BorderStyle
        BorderStyle_Outset = ... # type: QTextFrameFormat.BorderStyle

    class Position(enum.Enum):
        InFlow = ... # type: QTextFrameFormat.Position
        FloatLeft = ... # type: QTextFrameFormat.Position
        FloatRight = ... # type: QTextFrameFormat.Position

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextFrameFormat') -> None: ...

    def setRightMargin(self, amargin: float) -> None: ...
    def setLeftMargin(self, amargin: float) -> None: ...
    def setBottomMargin(self, amargin: float) -> None: ...
    def setTopMargin(self, amargin: float) -> None: ...
    def rightMargin(self) -> float: ...
    def leftMargin(self) -> float: ...
    def bottomMargin(self) -> float: ...
    def topMargin(self) -> float: ...
    def borderStyle(self) -> 'QTextFrameFormat.BorderStyle': ...
    def setBorderStyle(self, style: 'QTextFrameFormat.BorderStyle') -> None: ...
    def borderBrush(self) -> QBrush: ...
    def setBorderBrush(self, brush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    def pageBreakPolicy(self) -> QTextFormat.PageBreakFlag: ...
    def setPageBreakPolicy(self, flags: QTextFormat.PageBreakFlag) -> None: ...
    @typing.overload
    def setHeight(self, aheight: float) -> None: ...
    @typing.overload
    def setHeight(self, aheight: QTextLength) -> None: ...
    def setPadding(self, apadding: float) -> None: ...
    def setMargin(self, amargin: float) -> None: ...
    def setBorder(self, aborder: float) -> None: ...
    def height(self) -> QTextLength: ...
    def width(self) -> QTextLength: ...
    @typing.overload
    def setWidth(self, length: QTextLength) -> None: ...
    @typing.overload
    def setWidth(self, awidth: float) -> None: ...
    def padding(self) -> float: ...
    def margin(self) -> float: ...
    def border(self) -> float: ...
    def position(self) -> 'QTextFrameFormat.Position': ...
    def setPosition(self, f: 'QTextFrameFormat.Position') -> None: ...
    def isValid(self) -> bool: ...


class QTextTableFormat(QTextFrameFormat):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextTableFormat') -> None: ...

    def borderCollapse(self) -> bool: ...
    def setBorderCollapse(self, borderCollapse: bool) -> None: ...
    def headerRowCount(self) -> int: ...
    def setHeaderRowCount(self, count: int) -> None: ...
    def setAlignment(self, aalignment: QtCore.Qt.AlignmentFlag) -> None: ...
    def setCellPadding(self, apadding: float) -> None: ...
    def setColumns(self, acolumns: int) -> None: ...
    def alignment(self) -> QtCore.Qt.AlignmentFlag: ...
    def cellPadding(self) -> float: ...
    def setCellSpacing(self, spacing: float) -> None: ...
    def cellSpacing(self) -> float: ...
    def clearColumnWidthConstraints(self) -> None: ...
    def columnWidthConstraints(self) -> list[QTextLength]: ...
    def setColumnWidthConstraints(self, constraints: collections.abc.Iterable[QTextLength]) -> None: ...
    def columns(self) -> int: ...
    def isValid(self) -> bool: ...


class QTextTableCellFormat(QTextCharFormat):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextTableCellFormat') -> None: ...

    def setBorderBrush(self, brush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    def rightBorderBrush(self) -> QBrush: ...
    def setRightBorderBrush(self, brush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    def leftBorderBrush(self) -> QBrush: ...
    def setLeftBorderBrush(self, brush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    def bottomBorderBrush(self) -> QBrush: ...
    def setBottomBorderBrush(self, brush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    def topBorderBrush(self) -> QBrush: ...
    def setTopBorderBrush(self, brush: typing.Union[QBrush, typing.Union[QColor, QtCore.Qt.GlobalColor, int], QGradient]) -> None: ...
    def setBorderStyle(self, style: QTextFrameFormat.BorderStyle) -> None: ...
    def rightBorderStyle(self) -> QTextFrameFormat.BorderStyle: ...
    def setRightBorderStyle(self, style: QTextFrameFormat.BorderStyle) -> None: ...
    def leftBorderStyle(self) -> QTextFrameFormat.BorderStyle: ...
    def setLeftBorderStyle(self, style: QTextFrameFormat.BorderStyle) -> None: ...
    def bottomBorderStyle(self) -> QTextFrameFormat.BorderStyle: ...
    def setBottomBorderStyle(self, style: QTextFrameFormat.BorderStyle) -> None: ...
    def topBorderStyle(self) -> QTextFrameFormat.BorderStyle: ...
    def setTopBorderStyle(self, style: QTextFrameFormat.BorderStyle) -> None: ...
    def setBorder(self, width: float) -> None: ...
    def rightBorder(self) -> float: ...
    def setRightBorder(self, width: float) -> None: ...
    def leftBorder(self) -> float: ...
    def setLeftBorder(self, width: float) -> None: ...
    def bottomBorder(self) -> float: ...
    def setBottomBorder(self, width: float) -> None: ...
    def topBorder(self) -> float: ...
    def setTopBorder(self, width: float) -> None: ...
    def setPadding(self, padding: float) -> None: ...
    def rightPadding(self) -> float: ...
    def setRightPadding(self, padding: float) -> None: ...
    def leftPadding(self) -> float: ...
    def setLeftPadding(self, padding: float) -> None: ...
    def bottomPadding(self) -> float: ...
    def setBottomPadding(self, padding: float) -> None: ...
    def topPadding(self) -> float: ...
    def setTopPadding(self, padding: float) -> None: ...
    def isValid(self) -> bool: ...


class QTextInlineObject(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextInlineObject') -> None: ...

    def format(self) -> QTextFormat: ...
    def formatIndex(self) -> int: ...
    def textPosition(self) -> int: ...
    def setDescent(self, d: float) -> None: ...
    def setAscent(self, a: float) -> None: ...
    def setWidth(self, w: float) -> None: ...
    def textDirection(self) -> QtCore.Qt.LayoutDirection: ...
    def height(self) -> float: ...
    def descent(self) -> float: ...
    def ascent(self) -> float: ...
    def width(self) -> float: ...
    def rect(self) -> QtCore.QRectF: ...
    def isValid(self) -> bool: ...


class QTextLayout(PyQt6.sip.simplewrapper):

    class GlyphRunRetrievalFlag(enum.Enum):
        RetrieveGlyphIndexes = ... # type: QTextLayout.GlyphRunRetrievalFlag
        RetrieveGlyphPositions = ... # type: QTextLayout.GlyphRunRetrievalFlag
        RetrieveStringIndexes = ... # type: QTextLayout.GlyphRunRetrievalFlag
        RetrieveString = ... # type: QTextLayout.GlyphRunRetrievalFlag
        RetrieveAll = ... # type: QTextLayout.GlyphRunRetrievalFlag

    class CursorMode(enum.Enum):
        SkipCharacters = ... # type: QTextLayout.CursorMode
        SkipWords = ... # type: QTextLayout.CursorMode

    class FormatRange(PyQt6.sip.simplewrapper):

        format = ... # type: QTextCharFormat
        length = ... # type: int
        start = ... # type: int

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QTextLayout.FormatRange') -> None: ...

        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, text: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, text: typing.Optional[str], font: QFont, paintdevice: typing.Optional[QPaintDevice] = ...) -> None: ...
    @typing.overload
    def __init__(self, b: 'QTextBlock') -> None: ...

    def clearFormats(self) -> None: ...
    def formats(self) -> list['QTextLayout.FormatRange']: ...
    def setFormats(self, overrides: collections.abc.Iterable['QTextLayout.FormatRange']) -> None: ...
    @typing.overload
    def glyphRuns(self, from_: int = ..., length: int = ...) -> list[QGlyphRun]: ...
    @typing.overload
    def glyphRuns(self, from_: int, length: int, flags: 'QTextLayout.GlyphRunRetrievalFlag') -> list[QGlyphRun]: ...
    def rightCursorPosition(self, oldPos: int) -> int: ...
    def leftCursorPosition(self, oldPos: int) -> int: ...
    def cursorMoveStyle(self) -> QtCore.Qt.CursorMoveStyle: ...
    def setCursorMoveStyle(self, style: QtCore.Qt.CursorMoveStyle) -> None: ...
    def clearLayout(self) -> None: ...
    def maximumWidth(self) -> float: ...
    def minimumWidth(self) -> float: ...
    def boundingRect(self) -> QtCore.QRectF: ...
    def setPosition(self, p: QtCore.QPointF) -> None: ...
    def position(self) -> QtCore.QPointF: ...
    @typing.overload
    def drawCursor(self, p: typing.Optional[QPainter], pos: QtCore.QPointF, cursorPosition: int) -> None: ...
    @typing.overload
    def drawCursor(self, p: typing.Optional[QPainter], pos: QtCore.QPointF, cursorPosition: int, width: int) -> None: ...
    def draw(self, p: typing.Optional[QPainter], pos: QtCore.QPointF, selections: collections.abc.Iterable['QTextLayout.FormatRange'] = ..., clip: QtCore.QRectF = ...) -> None: ...
    def previousCursorPosition(self, oldPos: int, mode: 'QTextLayout.CursorMode' = ...) -> int: ...
    def nextCursorPosition(self, oldPos: int, mode: 'QTextLayout.CursorMode' = ...) -> int: ...
    def isValidCursorPosition(self, pos: int) -> bool: ...
    def lineForTextPosition(self, pos: int) -> 'QTextLine': ...
    def lineAt(self, i: int) -> 'QTextLine': ...
    def lineCount(self) -> int: ...
    def createLine(self) -> 'QTextLine': ...
    def endLayout(self) -> None: ...
    def beginLayout(self) -> None: ...
    def cacheEnabled(self) -> bool: ...
    def setCacheEnabled(self, enable: bool) -> None: ...
    def preeditAreaText(self) -> str: ...
    def preeditAreaPosition(self) -> int: ...
    def setPreeditArea(self, position: int, text: typing.Optional[str]) -> None: ...
    def textOption(self) -> 'QTextOption': ...
    def setTextOption(self, option: 'QTextOption') -> None: ...
    def text(self) -> str: ...
    def setText(self, string: typing.Optional[str]) -> None: ...
    def font(self) -> QFont: ...
    def setFont(self, f: QFont) -> None: ...


class QTextLine(PyQt6.sip.simplewrapper):

    class CursorPosition(enum.Enum):
        CursorBetweenCharacters = ... # type: QTextLine.CursorPosition
        CursorOnCharacter = ... # type: QTextLine.CursorPosition

    class Edge(enum.Enum):
        Leading = ... # type: QTextLine.Edge
        Trailing = ... # type: QTextLine.Edge

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextLine') -> None: ...

    @typing.overload
    def glyphRuns(self, from_: int = ..., length: int = ...) -> list[QGlyphRun]: ...
    @typing.overload
    def glyphRuns(self, from_: int, length: int, flags: QTextLayout.GlyphRunRetrievalFlag) -> list[QGlyphRun]: ...
    def horizontalAdvance(self) -> float: ...
    def leadingIncluded(self) -> bool: ...
    def setLeadingIncluded(self, included: bool) -> None: ...
    def leading(self) -> float: ...
    def position(self) -> QtCore.QPointF: ...
    def draw(self, painter: typing.Optional[QPainter], position: QtCore.QPointF) -> None: ...
    def lineNumber(self) -> int: ...
    def textLength(self) -> int: ...
    def textStart(self) -> int: ...
    def setPosition(self, pos: QtCore.QPointF) -> None: ...
    @typing.overload
    def setNumColumns(self, columns: int) -> None: ...
    @typing.overload
    def setNumColumns(self, columns: int, alignmentWidth: float) -> None: ...
    def setLineWidth(self, width: float) -> None: ...
    def xToCursor(self, x: float, edge: 'QTextLine.CursorPosition' = ...) -> int: ...
    def cursorToX(self, cursorPos: typing.Optional[int], edge: 'QTextLine.Edge' = ...) -> typing.Tuple[float, typing.Optional[int]]: ...
    def naturalTextRect(self) -> QtCore.QRectF: ...
    def naturalTextWidth(self) -> float: ...
    def height(self) -> float: ...
    def descent(self) -> float: ...
    def ascent(self) -> float: ...
    def width(self) -> float: ...
    def y(self) -> float: ...
    def x(self) -> float: ...
    def rect(self) -> QtCore.QRectF: ...
    def isValid(self) -> bool: ...


class QTextObject(QtCore.QObject):

    def __init__(self, doc: typing.Optional[QTextDocument]) -> None: ...

    def objectIndex(self) -> int: ...
    def document(self) -> typing.Optional[QTextDocument]: ...
    def formatIndex(self) -> int: ...
    def format(self) -> QTextFormat: ...
    def setFormat(self, format: QTextFormat) -> None: ...


class QTextBlockGroup(QTextObject):

    def __init__(self, doc: typing.Optional[QTextDocument]) -> None: ...

    def blockList(self) -> list['QTextBlock']: ...
    def blockFormatChanged(self, block: 'QTextBlock') -> None: ...
    def blockRemoved(self, block: 'QTextBlock') -> None: ...
    def blockInserted(self, block: 'QTextBlock') -> None: ...


class QTextList(QTextBlockGroup):

    def __init__(self, doc: typing.Optional[QTextDocument]) -> None: ...

    def setFormat(self, aformat: QTextListFormat) -> None: ...
    def format(self) -> QTextListFormat: ...
    def add(self, block: 'QTextBlock') -> None: ...
    def remove(self, a0: 'QTextBlock') -> None: ...
    def removeItem(self, i: int) -> None: ...
    def itemText(self, a0: 'QTextBlock') -> str: ...
    def itemNumber(self, a0: 'QTextBlock') -> int: ...
    def item(self, i: int) -> 'QTextBlock': ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...


class QTextFrame(QTextObject):

    class iterator(PyQt6.sip.simplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QTextFrame.iterator') -> None: ...

        def __isub__(self, a0: int) -> 'QTextFrame.iterator': ...
        def __iadd__(self, a0: int) -> 'QTextFrame.iterator': ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def atEnd(self) -> bool: ...
        def currentBlock(self) -> 'QTextBlock': ...
        def currentFrame(self) -> typing.Optional['QTextFrame']: ...
        def parentFrame(self) -> typing.Optional['QTextFrame']: ...

    def __init__(self, doc: typing.Optional[QTextDocument]) -> None: ...

    def setFrameFormat(self, aformat: QTextFrameFormat) -> None: ...
    def end(self) -> 'QTextFrame.iterator': ...
    def begin(self) -> 'QTextFrame.iterator': ...
    def parentFrame(self) -> typing.Optional['QTextFrame']: ...
    def childFrames(self) -> list['QTextFrame']: ...
    def lastPosition(self) -> int: ...
    def firstPosition(self) -> int: ...
    def lastCursorPosition(self) -> QTextCursor: ...
    def firstCursorPosition(self) -> QTextCursor: ...
    def frameFormat(self) -> QTextFrameFormat: ...


class QTextBlock(PyQt6.sip.wrapper):

    class iterator(PyQt6.sip.simplewrapper):

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QTextBlock.iterator') -> None: ...

        def __isub__(self, a0: int) -> 'QTextBlock.iterator': ...
        def __iadd__(self, a0: int) -> 'QTextBlock.iterator': ...
        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...
        def atEnd(self) -> bool: ...
        def fragment(self) -> 'QTextFragment': ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, o: 'QTextBlock') -> None: ...

    def __ge__(self, o: 'QTextBlock') -> bool: ...
    def textFormats(self) -> list[QTextLayout.FormatRange]: ...
    def textDirection(self) -> QtCore.Qt.LayoutDirection: ...
    def lineCount(self) -> int: ...
    def setLineCount(self, count: int) -> None: ...
    def firstLineNumber(self) -> int: ...
    def blockNumber(self) -> int: ...
    def setVisible(self, visible: bool) -> None: ...
    def isVisible(self) -> bool: ...
    def setRevision(self, rev: int) -> None: ...
    def revision(self) -> int: ...
    def clearLayout(self) -> None: ...
    def setUserState(self, state: int) -> None: ...
    def userState(self) -> int: ...
    def setUserData(self, data: typing.Optional['QTextBlockUserData']) -> None: ...
    def userData(self) -> typing.Optional['QTextBlockUserData']: ...
    def previous(self) -> 'QTextBlock': ...
    def next(self) -> 'QTextBlock': ...
    def end(self) -> 'QTextBlock.iterator': ...
    def begin(self) -> 'QTextBlock.iterator': ...
    def textList(self) -> typing.Optional[QTextList]: ...
    def document(self) -> typing.Optional[QTextDocument]: ...
    def text(self) -> str: ...
    def charFormatIndex(self) -> int: ...
    def charFormat(self) -> QTextCharFormat: ...
    def blockFormatIndex(self) -> int: ...
    def blockFormat(self) -> QTextBlockFormat: ...
    def layout(self) -> typing.Optional[QTextLayout]: ...
    def contains(self, position: int) -> bool: ...
    def length(self) -> int: ...
    def position(self) -> int: ...
    def __lt__(self, o: 'QTextBlock') -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isValid(self) -> bool: ...


class QTextFragment(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, o: 'QTextFragment') -> None: ...

    def __ge__(self, o: 'QTextFragment') -> bool: ...
    def glyphRuns(self, from_: int = ..., length: int = ...) -> list[QGlyphRun]: ...
    def text(self) -> str: ...
    def charFormatIndex(self) -> int: ...
    def charFormat(self) -> QTextCharFormat: ...
    def contains(self, position: int) -> bool: ...
    def length(self) -> int: ...
    def position(self) -> int: ...
    def __lt__(self, o: 'QTextFragment') -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isValid(self) -> bool: ...


class QTextBlockUserData(PyQt6.sip.wrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QTextBlockUserData') -> None: ...


class QTextOption(PyQt6.sip.simplewrapper):

    class TabType(enum.Enum):
        LeftTab = ... # type: QTextOption.TabType
        RightTab = ... # type: QTextOption.TabType
        CenterTab = ... # type: QTextOption.TabType
        DelimiterTab = ... # type: QTextOption.TabType

    class Flag(enum.Flag):
        IncludeTrailingSpaces = ... # type: QTextOption.Flag
        ShowTabsAndSpaces = ... # type: QTextOption.Flag
        ShowLineAndParagraphSeparators = ... # type: QTextOption.Flag
        AddSpaceForLineAndParagraphSeparators = ... # type: QTextOption.Flag
        SuppressColors = ... # type: QTextOption.Flag
        ShowDocumentTerminator = ... # type: QTextOption.Flag
        ShowDefaultIgnorables = ... # type: QTextOption.Flag
        DisableEmojiParsing = ... # type: QTextOption.Flag

    class WrapMode(enum.Enum):
        NoWrap = ... # type: QTextOption.WrapMode
        WordWrap = ... # type: QTextOption.WrapMode
        ManualWrap = ... # type: QTextOption.WrapMode
        WrapAnywhere = ... # type: QTextOption.WrapMode
        WrapAtWordBoundaryOrAnywhere = ... # type: QTextOption.WrapMode

    class Tab(PyQt6.sip.simplewrapper):

        delimiter = ... # type: str
        position = ... # type: float
        type = ... # type: 'QTextOption.TabType'

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, pos: float, tabType: 'QTextOption.TabType', delim: str = ...) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QTextOption.Tab') -> None: ...

        def __ne__(self, other: object): ...
        def __eq__(self, other: object): ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, alignment: QtCore.Qt.AlignmentFlag) -> None: ...
    @typing.overload
    def __init__(self, o: 'QTextOption') -> None: ...

    def tabStopDistance(self) -> float: ...
    def setTabStopDistance(self, tabStopDistance: float) -> None: ...
    def tabs(self) -> list['QTextOption.Tab']: ...
    def setTabs(self, tabStops: collections.abc.Iterable['QTextOption.Tab']) -> None: ...
    def setFlags(self, flags: 'QTextOption.Flag') -> None: ...
    def setAlignment(self, aalignment: QtCore.Qt.AlignmentFlag) -> None: ...
    def useDesignMetrics(self) -> bool: ...
    def setUseDesignMetrics(self, b: bool) -> None: ...
    def tabArray(self) -> list[float]: ...
    def setTabArray(self, tabStops: collections.abc.Iterable[float]) -> None: ...
    def flags(self) -> 'QTextOption.Flag': ...
    def wrapMode(self) -> 'QTextOption.WrapMode': ...
    def setWrapMode(self, wrap: 'QTextOption.WrapMode') -> None: ...
    def textDirection(self) -> QtCore.Qt.LayoutDirection: ...
    def setTextDirection(self, aDirection: QtCore.Qt.LayoutDirection) -> None: ...
    def alignment(self) -> QtCore.Qt.AlignmentFlag: ...


class QTextTableCell(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, o: 'QTextTableCell') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def tableCellFormatIndex(self) -> int: ...
    def lastCursorPosition(self) -> QTextCursor: ...
    def firstCursorPosition(self) -> QTextCursor: ...
    def isValid(self) -> bool: ...
    def columnSpan(self) -> int: ...
    def rowSpan(self) -> int: ...
    def column(self) -> int: ...
    def row(self) -> int: ...
    def setFormat(self, format: QTextCharFormat) -> None: ...
    def format(self) -> QTextCharFormat: ...


class QTextTable(QTextFrame):

    def __init__(self, doc: typing.Optional[QTextDocument]) -> None: ...

    def appendColumns(self, count: int) -> None: ...
    def appendRows(self, count: int) -> None: ...
    def setFormat(self, aformat: QTextTableFormat) -> None: ...
    def format(self) -> QTextTableFormat: ...
    def rowEnd(self, c: QTextCursor) -> QTextCursor: ...
    def rowStart(self, c: QTextCursor) -> QTextCursor: ...
    @typing.overload
    def cellAt(self, row: int, col: int) -> QTextTableCell: ...
    @typing.overload
    def cellAt(self, position: int) -> QTextTableCell: ...
    @typing.overload
    def cellAt(self, c: QTextCursor) -> QTextTableCell: ...
    def columns(self) -> int: ...
    def rows(self) -> int: ...
    def splitCell(self, row: int, col: int, numRows: int, numCols: int) -> None: ...
    @typing.overload
    def mergeCells(self, row: int, col: int, numRows: int, numCols: int) -> None: ...
    @typing.overload
    def mergeCells(self, cursor: QTextCursor) -> None: ...
    def removeColumns(self, pos: int, num: int) -> None: ...
    def removeRows(self, pos: int, num: int) -> None: ...
    def insertColumns(self, pos: int, num: int) -> None: ...
    def insertRows(self, pos: int, num: int) -> None: ...
    def resize(self, rows: int, cols: int) -> None: ...


class QTransform(PyQt6.sip.simplewrapper):

    class TransformationType(enum.Enum):
        TxNone = ... # type: QTransform.TransformationType
        TxTranslate = ... # type: QTransform.TransformationType
        TxScale = ... # type: QTransform.TransformationType
        TxRotate = ... # type: QTransform.TransformationType
        TxShear = ... # type: QTransform.TransformationType
        TxProject = ... # type: QTransform.TransformationType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, h11: float, h12: float, h13: float, h21: float, h22: float, h23: float, h31: float, h32: float, h33: float) -> None: ...
    @typing.overload
    def __init__(self, h11: float, h12: float, h13: float, h21: float, h22: float, h23: float) -> None: ...
    @typing.overload
    def __init__(self, other: 'QTransform') -> None: ...

    def __truediv__(self, n: float) -> 'QTransform': ...
    def __add__(self, n: float) -> 'QTransform': ...
    def __sub__(self, n: float) -> 'QTransform': ...
    def __hash__(self) -> int: ...
    def __isub__(self, num: float) -> 'QTransform': ...
    def __iadd__(self, num: float) -> 'QTransform': ...
    def __itruediv__(self, div: float) -> 'QTransform': ...
    @staticmethod
    def fromScale(dx: float, dy: float) -> 'QTransform': ...
    @staticmethod
    def fromTranslate(dx: float, dy: float) -> 'QTransform': ...
    def dy(self) -> float: ...
    def dx(self) -> float: ...
    def m33(self) -> float: ...
    def m32(self) -> float: ...
    def m31(self) -> float: ...
    def m23(self) -> float: ...
    def m22(self) -> float: ...
    def m21(self) -> float: ...
    def m13(self) -> float: ...
    def m12(self) -> float: ...
    def m11(self) -> float: ...
    def determinant(self) -> float: ...
    def isTranslating(self) -> bool: ...
    def isRotating(self) -> bool: ...
    def isScaling(self) -> bool: ...
    def isInvertible(self) -> bool: ...
    def isIdentity(self) -> bool: ...
    def isAffine(self) -> bool: ...
    @typing.overload
    def mapRect(self, a0: QtCore.QRect) -> QtCore.QRect: ...
    @typing.overload
    def mapRect(self, a0: QtCore.QRectF) -> QtCore.QRectF: ...
    def mapToPolygon(self, r: QtCore.QRect) -> QPolygon: ...
    @typing.overload
    def map(self, x: int, y: int) -> typing.Tuple[typing.Optional[int], typing.Optional[int]]: ...
    @typing.overload
    def map(self, x: float, y: float) -> typing.Tuple[typing.Optional[float], typing.Optional[float]]: ...
    @typing.overload
    def map(self, p: QtCore.QPoint) -> QtCore.QPoint: ...
    @typing.overload
    def map(self, p: QtCore.QPointF) -> QtCore.QPointF: ...
    @typing.overload
    def map(self, l: QtCore.QLine) -> QtCore.QLine: ...
    @typing.overload
    def map(self, l: QtCore.QLineF) -> QtCore.QLineF: ...
    @typing.overload
    def map(self, a: QPolygonF) -> QPolygonF: ...
    @typing.overload
    def map(self, a: QPolygon) -> QPolygon: ...
    @typing.overload
    def map(self, r: QRegion) -> QRegion: ...
    @typing.overload
    def map(self, p: QPainterPath) -> QPainterPath: ...
    def reset(self) -> None: ...
    def __matmul__(self, o: 'QTransform') -> 'QTransform': ...
    @typing.overload
    def __mul__(self, o: 'QTransform') -> 'QTransform': ...
    @typing.overload
    def __mul__(self, n: float) -> 'QTransform': ...
    def __imatmul__(self, a0: 'QTransform') -> 'QTransform': ...
    @typing.overload
    def __imul__(self, a0: 'QTransform') -> 'QTransform': ...
    @typing.overload
    def __imul__(self, num: float) -> 'QTransform': ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @staticmethod
    def quadToQuad(one: QPolygonF, two: QPolygonF, result: 'QTransform') -> bool: ...
    @staticmethod
    def quadToSquare(quad: QPolygonF, result: 'QTransform') -> bool: ...
    @staticmethod
    def squareToQuad(square: QPolygonF, result: 'QTransform') -> bool: ...
    @typing.overload
    def rotateRadians(self, angle: float, axis: QtCore.Qt.Axis = ...) -> 'QTransform': ...
    @typing.overload
    def rotateRadians(self, a: float, axis: QtCore.Qt.Axis, distanceToPlane: float) -> 'QTransform': ...
    @typing.overload
    def rotate(self, angle: float, axis: QtCore.Qt.Axis = ...) -> 'QTransform': ...
    @typing.overload
    def rotate(self, a: float, axis: QtCore.Qt.Axis, distanceToPlane: float) -> 'QTransform': ...
    def shear(self, sh: float, sv: float) -> 'QTransform': ...
    def scale(self, sx: float, sy: float) -> 'QTransform': ...
    def translate(self, dx: float, dy: float) -> 'QTransform': ...
    def transposed(self) -> 'QTransform': ...
    def adjoint(self) -> 'QTransform': ...
    def inverted(self) -> typing.Tuple['QTransform', typing.Optional[bool]]: ...
    def setMatrix(self, m11: float, m12: float, m13: float, m21: float, m22: float, m23: float, m31: float, m32: float, m33: float) -> None: ...
    def type(self) -> 'QTransform.TransformationType': ...


class QUndoGroup(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    undoTextChanged: typing.ClassVar[QtCore.pyqtSignal]
    redoTextChanged: typing.ClassVar[QtCore.pyqtSignal]
    indexChanged: typing.ClassVar[QtCore.pyqtSignal]
    cleanChanged: typing.ClassVar[QtCore.pyqtSignal]
    canUndoChanged: typing.ClassVar[QtCore.pyqtSignal]
    canRedoChanged: typing.ClassVar[QtCore.pyqtSignal]
    activeStackChanged: typing.ClassVar[QtCore.pyqtSignal]
    def undo(self) -> None: ...
    def setActiveStack(self, stack: typing.Optional['QUndoStack']) -> None: ...
    def redo(self) -> None: ...
    def isClean(self) -> bool: ...
    def redoText(self) -> str: ...
    def undoText(self) -> str: ...
    def canRedo(self) -> bool: ...
    def canUndo(self) -> bool: ...
    def createUndoAction(self, parent: typing.Optional[QtCore.QObject], prefix: typing.Optional[str] = ...) -> typing.Optional[QAction]: ...
    def createRedoAction(self, parent: typing.Optional[QtCore.QObject], prefix: typing.Optional[str] = ...) -> typing.Optional[QAction]: ...
    def activeStack(self) -> typing.Optional['QUndoStack']: ...
    def stacks(self) -> list['QUndoStack']: ...
    def removeStack(self, stack: typing.Optional['QUndoStack']) -> None: ...
    def addStack(self, stack: typing.Optional['QUndoStack']) -> None: ...


class QUndoCommand(PyQt6.sip.wrapper):

    @typing.overload
    def __init__(self, parent: typing.Optional['QUndoCommand'] = ...) -> None: ...
    @typing.overload
    def __init__(self, text: typing.Optional[str], parent: typing.Optional['QUndoCommand'] = ...) -> None: ...

    def setObsolete(self, obsolete: bool) -> None: ...
    def isObsolete(self) -> bool: ...
    def actionText(self) -> str: ...
    def child(self, index: int) -> typing.Optional['QUndoCommand']: ...
    def childCount(self) -> int: ...
    def undo(self) -> None: ...
    def text(self) -> str: ...
    def setText(self, text: typing.Optional[str]) -> None: ...
    def redo(self) -> None: ...
    def mergeWith(self, other: typing.Optional['QUndoCommand']) -> bool: ...
    def id(self) -> int: ...


class QUndoStack(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def command(self, index: int) -> typing.Optional[QUndoCommand]: ...
    def undoLimit(self) -> int: ...
    def setUndoLimit(self, limit: int) -> None: ...
    undoTextChanged: typing.ClassVar[QtCore.pyqtSignal]
    redoTextChanged: typing.ClassVar[QtCore.pyqtSignal]
    indexChanged: typing.ClassVar[QtCore.pyqtSignal]
    cleanChanged: typing.ClassVar[QtCore.pyqtSignal]
    canUndoChanged: typing.ClassVar[QtCore.pyqtSignal]
    canRedoChanged: typing.ClassVar[QtCore.pyqtSignal]
    def resetClean(self) -> None: ...
    def undo(self) -> None: ...
    def setIndex(self, idx: int) -> None: ...
    def setClean(self) -> None: ...
    def setActive(self, active: bool = ...) -> None: ...
    def redo(self) -> None: ...
    def endMacro(self) -> None: ...
    def beginMacro(self, text: typing.Optional[str]) -> None: ...
    def cleanIndex(self) -> int: ...
    def isClean(self) -> bool: ...
    def isActive(self) -> bool: ...
    def createRedoAction(self, parent: typing.Optional[QtCore.QObject], prefix: typing.Optional[str] = ...) -> typing.Optional[QAction]: ...
    def createUndoAction(self, parent: typing.Optional[QtCore.QObject], prefix: typing.Optional[str] = ...) -> typing.Optional[QAction]: ...
    def text(self, idx: int) -> str: ...
    def index(self) -> int: ...
    def __len__(self) -> int: ...
    def count(self) -> int: ...
    def redoText(self) -> str: ...
    def undoText(self) -> str: ...
    def canRedo(self) -> bool: ...
    def canUndo(self) -> bool: ...
    def push(self, cmd: typing.Optional[QUndoCommand]) -> None: ...
    def clear(self) -> None: ...


class QValidator(QtCore.QObject):

    class State(enum.Enum):
        Invalid = ... # type: QValidator.State
        Intermediate = ... # type: QValidator.State
        Acceptable = ... # type: QValidator.State

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    changed: typing.ClassVar[QtCore.pyqtSignal]
    def locale(self) -> QtCore.QLocale: ...
    def setLocale(self, locale: QtCore.QLocale) -> None: ...
    def fixup(self, a0: typing.Optional[str]) -> str: ...
    def validate(self, a0: typing.Optional[str], a1: int) -> typing.Tuple['QValidator.State', str, int]: ...


class QIntValidator(QValidator):

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, bottom: int, top: int, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def top(self) -> int: ...
    def bottom(self) -> int: ...
    def setRange(self, bottom: int, top: int) -> None: ...
    def setTop(self, a0: int) -> None: ...
    def setBottom(self, a0: int) -> None: ...
    def fixup(self, input: typing.Optional[str]) -> str: ...
    def validate(self, a0: typing.Optional[str], a1: int) -> typing.Tuple[QValidator.State, str, int]: ...


class QDoubleValidator(QValidator):

    class Notation(enum.Enum):
        StandardNotation = ... # type: QDoubleValidator.Notation
        ScientificNotation = ... # type: QDoubleValidator.Notation

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, bottom: float, top: float, decimals: int, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def fixup(self, input: typing.Optional[str]) -> None: ...
    def notation(self) -> 'QDoubleValidator.Notation': ...
    def setNotation(self, a0: 'QDoubleValidator.Notation') -> None: ...
    def decimals(self) -> int: ...
    def top(self) -> float: ...
    def bottom(self) -> float: ...
    def setDecimals(self, a0: int) -> None: ...
    def setTop(self, a0: float) -> None: ...
    def setBottom(self, a0: float) -> None: ...
    def setRange(self, bottom: float, top: float, decimals: int = ...) -> None: ...
    def validate(self, a0: typing.Optional[str], a1: int) -> typing.Tuple[QValidator.State, str, int]: ...


class QRegularExpressionValidator(QValidator):

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, re: QtCore.QRegularExpression, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setRegularExpression(self, re: QtCore.QRegularExpression) -> None: ...
    def regularExpression(self) -> QtCore.QRegularExpression: ...
    def validate(self, input: typing.Optional[str], pos: int) -> typing.Tuple[QValidator.State, str, int]: ...


class QVector2D(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, xpos: float, ypos: float) -> None: ...
    @typing.overload
    def __init__(self, point: QtCore.QPoint) -> None: ...
    @typing.overload
    def __init__(self, point: QtCore.QPointF) -> None: ...
    @typing.overload
    def __init__(self, vector: 'QVector3D') -> None: ...
    @typing.overload
    def __init__(self, vector: 'QVector4D') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QVector2D') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @typing.overload
    def __truediv__(self, divisor: 'QVector2D') -> 'QVector2D': ...
    @typing.overload
    def __truediv__(self, divisor: float) -> 'QVector2D': ...
    def __add__(self, v2: 'QVector2D') -> 'QVector2D': ...
    def __sub__(self, v2: 'QVector2D') -> 'QVector2D': ...
    @typing.overload
    def __mul__(self, v2: 'QVector2D') -> 'QVector2D': ...
    @typing.overload
    def __mul__(self, factor: float) -> 'QVector2D': ...
    def __rmul__(self, factor: float) -> 'QVector2D': ...
    def __neg__(self) -> 'QVector2D': ...
    def toPointF(self) -> QtCore.QPointF: ...
    def toPoint(self) -> QtCore.QPoint: ...
    def toVector4D(self) -> 'QVector4D': ...
    def toVector3D(self) -> 'QVector3D': ...
    @staticmethod
    def dotProduct(v1: 'QVector2D', v2: 'QVector2D') -> float: ...
    @typing.overload
    def __itruediv__(self, divisor: float) -> 'QVector2D': ...
    @typing.overload
    def __itruediv__(self, vector: 'QVector2D') -> 'QVector2D': ...
    @typing.overload
    def __imul__(self, factor: float) -> 'QVector2D': ...
    @typing.overload
    def __imul__(self, vector: 'QVector2D') -> 'QVector2D': ...
    def __isub__(self, vector: 'QVector2D') -> 'QVector2D': ...
    def __iadd__(self, vector: 'QVector2D') -> 'QVector2D': ...
    def distanceToLine(self, point: 'QVector2D', direction: 'QVector2D') -> float: ...
    def distanceToPoint(self, point: 'QVector2D') -> float: ...
    def normalize(self) -> None: ...
    def normalized(self) -> 'QVector2D': ...
    def lengthSquared(self) -> float: ...
    def length(self) -> float: ...
    def __getitem__(self, i: int) -> float: ...
    def setY(self, y: float) -> None: ...
    def setX(self, x: float) -> None: ...
    def y(self) -> float: ...
    def x(self) -> float: ...
    def isNull(self) -> bool: ...
    def __repr__(self) -> str: ...


class QVector3D(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, xpos: float, ypos: float, zpos: float) -> None: ...
    @typing.overload
    def __init__(self, point: QtCore.QPoint) -> None: ...
    @typing.overload
    def __init__(self, point: QtCore.QPointF) -> None: ...
    @typing.overload
    def __init__(self, vector: QVector2D, zpos: float) -> None: ...
    @typing.overload
    def __init__(self, vector: QVector2D) -> None: ...
    @typing.overload
    def __init__(self, vector: 'QVector4D') -> None: ...
    @typing.overload
    def __init__(self, a0: 'QVector3D') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @typing.overload
    def __truediv__(self, divisor: 'QVector3D') -> 'QVector3D': ...
    @typing.overload
    def __truediv__(self, divisor: float) -> 'QVector3D': ...
    def __add__(self, v2: 'QVector3D') -> 'QVector3D': ...
    def __sub__(self, v2: 'QVector3D') -> 'QVector3D': ...
    @typing.overload
    def __mul__(self, matrix: QMatrix4x4) -> 'QVector3D': ...
    @typing.overload
    def __mul__(self, v2: 'QVector3D') -> 'QVector3D': ...
    @typing.overload
    def __mul__(self, factor: float) -> 'QVector3D': ...
    def __rmul__(self, factor: float) -> 'QVector3D': ...
    def __neg__(self) -> 'QVector3D': ...
    def toPointF(self) -> QtCore.QPointF: ...
    def toPoint(self) -> QtCore.QPoint: ...
    def toVector4D(self) -> 'QVector4D': ...
    def toVector2D(self) -> QVector2D: ...
    def distanceToLine(self, point: 'QVector3D', direction: 'QVector3D') -> float: ...
    @typing.overload
    def distanceToPlane(self, plane: 'QVector3D', normal: 'QVector3D') -> float: ...
    @typing.overload
    def distanceToPlane(self, plane1: 'QVector3D', plane2: 'QVector3D', plane3: 'QVector3D') -> float: ...
    def distanceToPoint(self, point: 'QVector3D') -> float: ...
    def unproject(self, modelView: QMatrix4x4, projection: QMatrix4x4, viewport: QtCore.QRect) -> 'QVector3D': ...
    def project(self, modelView: QMatrix4x4, projection: QMatrix4x4, viewport: QtCore.QRect) -> 'QVector3D': ...
    @typing.overload
    @staticmethod
    def normal(v1: 'QVector3D', v2: 'QVector3D') -> 'QVector3D': ...
    @typing.overload
    @staticmethod
    def normal(v1: 'QVector3D', v2: 'QVector3D', v3: 'QVector3D') -> 'QVector3D': ...
    @staticmethod
    def crossProduct(v1: 'QVector3D', v2: 'QVector3D') -> 'QVector3D': ...
    @staticmethod
    def dotProduct(v1: 'QVector3D', v2: 'QVector3D') -> float: ...
    @typing.overload
    def __itruediv__(self, divisor: float) -> 'QVector3D': ...
    @typing.overload
    def __itruediv__(self, vector: 'QVector3D') -> 'QVector3D': ...
    @typing.overload
    def __imul__(self, factor: float) -> 'QVector3D': ...
    @typing.overload
    def __imul__(self, vector: 'QVector3D') -> 'QVector3D': ...
    def __isub__(self, vector: 'QVector3D') -> 'QVector3D': ...
    def __iadd__(self, vector: 'QVector3D') -> 'QVector3D': ...
    def normalize(self) -> None: ...
    def normalized(self) -> 'QVector3D': ...
    def lengthSquared(self) -> float: ...
    def length(self) -> float: ...
    def __getitem__(self, i: int) -> float: ...
    def setZ(self, z: float) -> None: ...
    def setY(self, y: float) -> None: ...
    def setX(self, x: float) -> None: ...
    def z(self) -> float: ...
    def y(self) -> float: ...
    def x(self) -> float: ...
    def isNull(self) -> bool: ...
    def __repr__(self) -> str: ...


class QVector4D(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, xpos: float, ypos: float, zpos: float, wpos: float) -> None: ...
    @typing.overload
    def __init__(self, point: QtCore.QPoint) -> None: ...
    @typing.overload
    def __init__(self, point: QtCore.QPointF) -> None: ...
    @typing.overload
    def __init__(self, vector: QVector2D) -> None: ...
    @typing.overload
    def __init__(self, vector: QVector2D, zpos: float, wpos: float) -> None: ...
    @typing.overload
    def __init__(self, vector: QVector3D) -> None: ...
    @typing.overload
    def __init__(self, vector: QVector3D, wpos: float) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QVector4D') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @typing.overload
    def __truediv__(self, divisor: 'QVector4D') -> 'QVector4D': ...
    @typing.overload
    def __truediv__(self, divisor: float) -> 'QVector4D': ...
    def __add__(self, v2: 'QVector4D') -> 'QVector4D': ...
    def __sub__(self, v2: 'QVector4D') -> 'QVector4D': ...
    @typing.overload
    def __mul__(self, matrix: QMatrix4x4) -> 'QVector4D': ...
    @typing.overload
    def __mul__(self, v2: 'QVector4D') -> 'QVector4D': ...
    @typing.overload
    def __mul__(self, factor: float) -> 'QVector4D': ...
    def __rmul__(self, factor: float) -> 'QVector4D': ...
    def __neg__(self) -> 'QVector4D': ...
    def toPointF(self) -> QtCore.QPointF: ...
    def toPoint(self) -> QtCore.QPoint: ...
    def toVector3DAffine(self) -> QVector3D: ...
    def toVector3D(self) -> QVector3D: ...
    def toVector2DAffine(self) -> QVector2D: ...
    def toVector2D(self) -> QVector2D: ...
    @staticmethod
    def dotProduct(v1: 'QVector4D', v2: 'QVector4D') -> float: ...
    @typing.overload
    def __itruediv__(self, divisor: float) -> 'QVector4D': ...
    @typing.overload
    def __itruediv__(self, vector: 'QVector4D') -> 'QVector4D': ...
    @typing.overload
    def __imul__(self, factor: float) -> 'QVector4D': ...
    @typing.overload
    def __imul__(self, vector: 'QVector4D') -> 'QVector4D': ...
    def __isub__(self, vector: 'QVector4D') -> 'QVector4D': ...
    def __iadd__(self, vector: 'QVector4D') -> 'QVector4D': ...
    def normalize(self) -> None: ...
    def normalized(self) -> 'QVector4D': ...
    def lengthSquared(self) -> float: ...
    def length(self) -> float: ...
    def __getitem__(self, i: int) -> float: ...
    def setW(self, w: float) -> None: ...
    def setZ(self, z: float) -> None: ...
    def setY(self, y: float) -> None: ...
    def setX(self, x: float) -> None: ...
    def w(self) -> float: ...
    def z(self) -> float: ...
    def y(self) -> float: ...
    def x(self) -> float: ...
    def isNull(self) -> bool: ...
    def __repr__(self) -> str: ...


@typing.overload
def qGray(r: int, g: int, b: int) -> int: ...
@typing.overload
def qGray(rgb: int) -> int: ...
def qRgba(r: int, g: int, b: int, a: int) -> int: ...
def qRgb(r: int, g: int, b: int) -> int: ...
@typing.overload
def qAlpha(rgb: QRgba64) -> int: ...
@typing.overload
def qAlpha(rgb: int) -> int: ...
@typing.overload
def qBlue(rgb: QRgba64) -> int: ...
@typing.overload
def qBlue(rgb: int) -> int: ...
@typing.overload
def qGreen(rgb: QRgba64) -> int: ...
@typing.overload
def qGreen(rgb: int) -> int: ...
@typing.overload
def qRed(rgb: QRgba64) -> int: ...
@typing.overload
def qRed(rgb: int) -> int: ...
@typing.overload
def qUnpremultiply(c: QRgba64) -> QRgba64: ...
@typing.overload
def qUnpremultiply(p: int) -> int: ...
@typing.overload
def qPremultiply(c: QRgba64) -> QRgba64: ...
@typing.overload
def qPremultiply(x: int) -> int: ...
@typing.overload
def qRgba64(r: int, g: int, b: int, a: int) -> QRgba64: ...
@typing.overload
def qRgba64(c: int) -> QRgba64: ...
def qPixelFormatAlpha(channelSize: int, typeInterpretation: QPixelFormat.TypeInterpretation = ...) -> QPixelFormat: ...
def qPixelFormatYuv(layout: QPixelFormat.YUVLayout, alphaSize: int = ..., alphaUsage: QPixelFormat.AlphaUsage = ..., alphaPosition: QPixelFormat.AlphaPosition = ..., premultiplied: QPixelFormat.AlphaPremultiplied = ..., typeInterpretation: QPixelFormat.TypeInterpretation = ..., byteOrder: QPixelFormat.ByteOrder = ...) -> QPixelFormat: ...
def qPixelFormatHsv(channelSize: int, alphaSize: int = ..., alphaUsage: QPixelFormat.AlphaUsage = ..., alphaPosition: QPixelFormat.AlphaPosition = ..., typeInterpretation: QPixelFormat.TypeInterpretation = ...) -> QPixelFormat: ...
def qPixelFormatHsl(channelSize: int, alphaSize: int = ..., alphaUsage: QPixelFormat.AlphaUsage = ..., alphaPosition: QPixelFormat.AlphaPosition = ..., typeInterpretation: QPixelFormat.TypeInterpretation = ...) -> QPixelFormat: ...
def qPixelFormatCmyk(channelSize: int, alphaSize: int = ..., alphaUsage: QPixelFormat.AlphaUsage = ..., alphaPosition: QPixelFormat.AlphaPosition = ..., typeInterpretation: QPixelFormat.TypeInterpretation = ...) -> QPixelFormat: ...
def qPixelFormatGrayscale(channelSize: int, typeInterpretation: QPixelFormat.TypeInterpretation = ...) -> QPixelFormat: ...
def qPixelFormatRgba(red: int, green: int, blue: int, alfa: int, usage: QPixelFormat.AlphaUsage, position: QPixelFormat.AlphaPosition, premultiplied: QPixelFormat.AlphaPremultiplied = ..., typeInterpretation: QPixelFormat.TypeInterpretation = ...) -> QPixelFormat: ...
@typing.overload
def qFuzzyCompare(m1: QMatrix4x4, m2: QMatrix4x4) -> bool: ...
@typing.overload
def qFuzzyCompare(q1: QQuaternion, q2: QQuaternion) -> bool: ...
@typing.overload
def qFuzzyCompare(t1: QTransform, t2: QTransform) -> bool: ...
@typing.overload
def qFuzzyCompare(v1: QVector4D, v2: QVector4D) -> bool: ...
@typing.overload
def qFuzzyCompare(v1: QVector3D, v2: QVector3D) -> bool: ...
@typing.overload
def qFuzzyCompare(v1: QVector2D, v2: QVector2D) -> bool: ...
def qt_set_sequence_auto_mnemonic(b: bool) -> None: ...
