// qimagewriter.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QImageWriter
{
%TypeHeaderCode
#include <qimagewriter.h>
%End

public:
    enum ImageWriterError
    {
        UnknownError,
        DeviceError,
        UnsupportedFormatError,
        InvalidImageError,
    };

    QImageWriter();
    QImageWriter(QIODevice *device, const QByteArray &format);
    QImageWriter(const QString &fileName, const QByteArray &format = QByteArray());
    ~QImageWriter();
    void setFormat(const QByteArray &format);
    QByteArray format() const;
    void setDevice(QIODevice *device);
    QIODevice *device() const;
    void setFileName(const QString &fileName);
    QString fileName() const;
    void setQuality(int quality);
    int quality() const;
    bool canWrite() const;
    bool write(const QImage &image) /ReleaseGIL/;
    QImageWriter::ImageWriterError error() const;
    QString errorString() const;
    static QList<QByteArray> supportedImageFormats();
    void setText(const QString &key, const QString &text);
    bool supportsOption(QImageIOHandler::ImageOption option) const;
    void setCompression(int compression);
    int compression() const;
    static QList<QByteArray> supportedMimeTypes();
    void setSubType(const QByteArray &type);
    QByteArray subType() const;
    QList<QByteArray> supportedSubTypes() const;
    void setOptimizedWrite(bool optimize);
    bool optimizedWrite() const;
    void setProgressiveScanWrite(bool progressive);
    bool progressiveScanWrite() const;
    QImageIOHandler::Transformations transformation() const;
    void setTransformation(QImageIOHandler::Transformations orientation);
    static QList<QByteArray> imageFormatsForMimeType(const QByteArray &mimeType);

private:
    QImageWriter(const QImageWriter &);
};
