// qopenglframebufferobject.sip generated by MetaSIP
//
// This file is part of the QtOpenGL Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QOpenGLFramebufferObject
{
%TypeHeaderCode
#include <qopenglframebufferobject.h>
%End

%TypeCode
// The defaults are different for desktop OpenGL and OpenGL/ES so pretend the
// latter is the former.
#if defined(QT_OPENGL_ES)
#undef  GL_RGBA8
#define GL_RGBA8        GL_RGBA
#endif
%End

public:
    enum Attachment
    {
        NoAttachment,
        CombinedDepthStencil,
        Depth,
    };

    QOpenGLFramebufferObject(const QSize &size, GLenum target = GL_TEXTURE_2D);
    QOpenGLFramebufferObject(int width, int height, GLenum target = GL_TEXTURE_2D);
    QOpenGLFramebufferObject(const QSize &size, QOpenGLFramebufferObject::Attachment attachment, GLenum target = GL_TEXTURE_2D, GLenum internal_format = GL_RGBA8);
    QOpenGLFramebufferObject(int width, int height, QOpenGLFramebufferObject::Attachment attachment, GLenum target = GL_TEXTURE_2D, GLenum internal_format = GL_RGBA8);
    QOpenGLFramebufferObject(const QSize &size, const QOpenGLFramebufferObjectFormat &format);
    QOpenGLFramebufferObject(int width, int height, const QOpenGLFramebufferObjectFormat &format);
    virtual ~QOpenGLFramebufferObject();
    QOpenGLFramebufferObjectFormat format() const;
    bool isValid() const;
    bool isBound() const;
    bool bind();
    bool release();
    int width() const;
    int height() const;
    GLuint texture() const;
    QList<unsigned int> textures() const;
    QSize size() const;
    QImage toImage(bool flipped = true) const;
    QImage toImage(bool flipped, int colorAttachmentIndex) const;
    QOpenGLFramebufferObject::Attachment attachment() const;
    void setAttachment(QOpenGLFramebufferObject::Attachment attachment);
    GLuint handle() const;
    static bool bindDefault();
    static bool hasOpenGLFramebufferObjects();
    static bool hasOpenGLFramebufferBlit();

    enum FramebufferRestorePolicy
    {
        DontRestoreFramebufferBinding,
        RestoreFramebufferBindingToDefault,
        RestoreFrameBufferBinding,
    };

    static void blitFramebuffer(QOpenGLFramebufferObject *target, const QRect &targetRect, QOpenGLFramebufferObject *source, const QRect &sourceRect, GLbitfield buffers = GL_COLOR_BUFFER_BIT, GLenum filter = GL_NEAREST);
    static void blitFramebuffer(QOpenGLFramebufferObject *target, QOpenGLFramebufferObject *source, GLbitfield buffers = GL_COLOR_BUFFER_BIT, GLenum filter = GL_NEAREST);
    static void blitFramebuffer(QOpenGLFramebufferObject *target, const QRect &targetRect, QOpenGLFramebufferObject *source, const QRect &sourceRect, GLbitfield buffers, GLenum filter, int readColorAttachmentIndex, int drawColorAttachmentIndex);
    static void blitFramebuffer(QOpenGLFramebufferObject *target, const QRect &targetRect, QOpenGLFramebufferObject *source, const QRect &sourceRect, GLbitfield buffers, GLenum filter, int readColorAttachmentIndex, int drawColorAttachmentIndex, QOpenGLFramebufferObject::FramebufferRestorePolicy restorePolicy);
    GLuint takeTexture();
    GLuint takeTexture(int colorAttachmentIndex);
    void addColorAttachment(const QSize &size, GLenum internal_format = 0);
    void addColorAttachment(int width, int height, GLenum internal_format = 0);
    QList<QSize> sizes() const;

private:
    QOpenGLFramebufferObject(const QOpenGLFramebufferObject &);
};

class QOpenGLFramebufferObjectFormat
{
%TypeHeaderCode
#include <qopenglframebufferobject.h>
%End

public:
    QOpenGLFramebufferObjectFormat();
    QOpenGLFramebufferObjectFormat(const QOpenGLFramebufferObjectFormat &other);
    ~QOpenGLFramebufferObjectFormat();
    void setSamples(int samples);
    int samples() const;
    void setMipmap(bool enabled);
    bool mipmap() const;
    void setAttachment(QOpenGLFramebufferObject::Attachment attachment);
    QOpenGLFramebufferObject::Attachment attachment() const;
    void setTextureTarget(GLenum target);
    GLenum textureTarget() const;
    void setInternalTextureFormat(GLenum internalTextureFormat);
    GLenum internalTextureFormat() const;
    bool operator==(const QOpenGLFramebufferObjectFormat &other) const;
    bool operator!=(const QOpenGLFramebufferObjectFormat &other) const;
};
