// qmediametadata.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QMediaMetaData
{
%TypeHeaderCode
#include <qmediametadata.h>
%End

public:
    enum Key
    {
        Title,
        Author,
        Comment,
        Description,
        Genre,
        Date,
        Language,
        Publisher,
        Copyright,
        Url,
        Duration,
        MediaType,
        FileFormat,
        AudioBitRate,
        AudioCodec,
        VideoBitRate,
        VideoCodec,
        VideoFrameRate,
        AlbumTitle,
        AlbumArtist,
        ContributingArtist,
        TrackNumber,
        Composer,
        LeadPerformer,
        ThumbnailImage,
        CoverArtImage,
        Orientation,
        Resolution,
%If (Qt_6_8_0 -)
        HasHdrContent,
%End
    };

    QVariant value(QMediaMetaData::Key k) const;
    void insert(QMediaMetaData::Key k, const QVariant &value);
    QList<QMediaMetaData::Key> keys() const;
    QString stringValue(QMediaMetaData::Key k) const;
    static QString metaDataKeyToString(QMediaMetaData::Key k);

protected:
%If (Qt_6_4_0 -)
    static QMetaType keyType(QMediaMetaData::Key key);
%End
};

%End
%If (Qt_6_2_0 -)
bool operator==(const QMediaMetaData &a, const QMediaMetaData &b);
%End
%If (Qt_6_2_0 -)
bool operator!=(const QMediaMetaData &a, const QMediaMetaData &b);
%End
