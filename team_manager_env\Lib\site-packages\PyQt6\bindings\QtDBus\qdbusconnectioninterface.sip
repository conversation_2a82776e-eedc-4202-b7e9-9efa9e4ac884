// qdbusconnectioninterface.sip generated by MetaSIP
//
// This file is part of the QtDBus Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDBusConnectionInterface : public QDBusAbstractInterface
{
%TypeHeaderCode
#include <qdbusconnectioninterface.h>
%End

    QDBusConnectionInterface(const QDBusConnection &connection, QObject *parent /TransferThis/);
    virtual ~QDBusConnectionInterface();

public:
    enum ServiceQueueOptions
    {
        DontQueueService,
        QueueService,
        ReplaceExistingService,
    };

    enum ServiceReplacementOptions
    {
        DontAllowReplacement,
        AllowReplacement,
    };

    enum RegisterServiceReply
    {
        ServiceNotRegistered,
        ServiceRegistered,
        ServiceQueued,
    };

    QDBusReply<QStringList> registeredServiceNames() const /ReleaseGIL/;
    QDBusReply<QStringList> activatableServiceNames() const /ReleaseGIL/;
    QDBusReply<bool> isServiceRegistered(const QString &serviceName) const /ReleaseGIL/;
    QDBusReply<QString> serviceOwner(const QString &name) const /ReleaseGIL/;
    QDBusReply<bool> unregisterService(const QString &serviceName) /ReleaseGIL/;
    QDBusReply<QDBusConnectionInterface::RegisterServiceReply> registerService(const QString &serviceName, QDBusConnectionInterface::ServiceQueueOptions qoption = QDBusConnectionInterface::DontQueueService, QDBusConnectionInterface::ServiceReplacementOptions roption = QDBusConnectionInterface::DontAllowReplacement) /ReleaseGIL/;
    QDBusReply<unsigned int> servicePid(const QString &serviceName) const /ReleaseGIL/;
    QDBusReply<unsigned int> serviceUid(const QString &serviceName) const /ReleaseGIL/;
    QDBusReply<void> startService(const QString &name) /ReleaseGIL/;

signals:
    void serviceRegistered(const QString &service);
    void serviceUnregistered(const QString &service);
    void serviceOwnerChanged(const QString &name, const QString &oldOwner, const QString &newOwner);
    void callWithCallbackFailed(const QDBusError &error, const QDBusMessage &call);

protected:
    virtual void connectNotify(const QMetaMethod &);
    virtual void disconnectNotify(const QMetaMethod &);
};
