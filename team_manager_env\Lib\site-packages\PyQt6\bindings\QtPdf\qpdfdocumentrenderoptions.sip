// qpdfdocumentrenderoptions.sip generated by MetaSIP
//
// This file is part of the QtPdf Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPdfDocumentRenderOptions
{
%TypeHeaderCode
#include <qpdfdocumentrenderoptions.h>
%End

public:
    enum class Rotation
    {
        None,
        Clockwise90,
        Clockwise180,
        Clockwise270,
    };

    enum class RenderFlag
    {
        None,
        Annotations,
        OptimizedForLcd,
        Grayscale,
        ForceHalftone,
        TextAliased,
        ImageAliased,
        PathAliased,
    };

    typedef QFlags<QPdfDocumentRenderOptions::RenderFlag> RenderFlags;
    QPdfDocumentRenderOptions();
    QPdfDocumentRenderOptions::Rotation rotation() const;
    void setRotation(QPdfDocumentRenderOptions::Rotation r);
    QPdfDocumentRenderOptions::RenderFlags renderFlags() const;
    void setRenderFlags(QPdfDocumentRenderOptions::RenderFlags r);
    QRect scaledClipRect() const;
    void setScaledClipRect(const QRect &r);
    QSize scaledSize() const;
    void setScaledSize(const QSize &s);
};

bool operator==(const QPdfDocumentRenderOptions &lhs, const QPdfDocumentRenderOptions &rhs);
bool operator!=(const QPdfDocumentRenderOptions &lhs, const QPdfDocumentRenderOptions &rhs);
