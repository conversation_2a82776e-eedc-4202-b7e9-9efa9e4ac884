# The PEP 484 type hints stub file for the QtSvg module.
#
# Generated by SIP 6.12.0
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt6.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICENSE included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import collections, re, typing, enum

try:
    from warnings import deprecated
except ImportError:
    pass

import PyQt6.sip

from PyQt6 import QtCore
from PyQt6 import QtGui

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[collections.abc.Callable[..., Any], QtCore.pyqtBoundSignal]


class QtSvg(PyQt6.sip.simplewrapper):

    class Option(enum.Enum):
        NoOption = ... # type: QtSvg.Option
        Tiny12FeaturesOnly = ... # type: QtSvg.Option
        AssumeTrustedSource = ... # type: QtSvg.Option
        DisableSMILAnimations = ... # type: QtSvg.Option
        DisableCSSAnimations = ... # type: QtSvg.Option
        DisableAnimations = ... # type: QtSvg.Option


class QSvgGenerator(QtGui.QPaintDevice):

    class SvgVersion(enum.Enum):
        SvgTiny12 = ... # type: QSvgGenerator.SvgVersion
        Svg11 = ... # type: QSvgGenerator.SvgVersion

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, version: 'QSvgGenerator.SvgVersion') -> None: ...

    def svgVersion(self) -> 'QSvgGenerator.SvgVersion': ...
    def metric(self, metric: QtGui.QPaintDevice.PaintDeviceMetric) -> int: ...
    def paintEngine(self) -> typing.Optional[QtGui.QPaintEngine]: ...
    @typing.overload
    def setViewBox(self, viewBox: QtCore.QRect) -> None: ...
    @typing.overload
    def setViewBox(self, viewBox: QtCore.QRectF) -> None: ...
    def viewBoxF(self) -> QtCore.QRectF: ...
    def viewBox(self) -> QtCore.QRect: ...
    def setDescription(self, description: typing.Optional[str]) -> None: ...
    def description(self) -> str: ...
    def setTitle(self, title: typing.Optional[str]) -> None: ...
    def title(self) -> str: ...
    def setResolution(self, resolution: int) -> None: ...
    def resolution(self) -> int: ...
    def setOutputDevice(self, outputDevice: typing.Optional[QtCore.QIODevice]) -> None: ...
    def outputDevice(self) -> typing.Optional[QtCore.QIODevice]: ...
    def setFileName(self, fileName: typing.Optional[str]) -> None: ...
    def fileName(self) -> str: ...
    def setSize(self, size: QtCore.QSize) -> None: ...
    def size(self) -> QtCore.QSize: ...


class QSvgRenderer(QtCore.QObject):

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, filename: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, contents: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, contents: typing.Optional[QtCore.QXmlStreamReader], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    @staticmethod
    def setDefaultOptions(flags: QtSvg.Option) -> None: ...
    def setAnimationEnabled(self, enable: bool) -> None: ...
    def isAnimationEnabled(self) -> bool: ...
    def setOptions(self, flags: QtSvg.Option) -> None: ...
    def options(self) -> QtSvg.Option: ...
    def transformForElement(self, id: typing.Optional[str]) -> QtGui.QTransform: ...
    def setAspectRatioMode(self, mode: QtCore.Qt.AspectRatioMode) -> None: ...
    def aspectRatioMode(self) -> QtCore.Qt.AspectRatioMode: ...
    repaintNeeded: typing.ClassVar[QtCore.pyqtSignal]
    @typing.overload
    def render(self, p: typing.Optional[QtGui.QPainter]) -> None: ...
    @typing.overload
    def render(self, p: typing.Optional[QtGui.QPainter], bounds: QtCore.QRectF) -> None: ...
    @typing.overload
    def render(self, painter: typing.Optional[QtGui.QPainter], elementId: typing.Optional[str], bounds: QtCore.QRectF = ...) -> None: ...
    @typing.overload
    def load(self, filename: typing.Optional[str]) -> bool: ...
    @typing.overload
    def load(self, contents: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> bool: ...
    @typing.overload
    def load(self, contents: typing.Optional[QtCore.QXmlStreamReader]) -> bool: ...
    def animationDuration(self) -> int: ...
    def setCurrentFrame(self, a0: int) -> None: ...
    def currentFrame(self) -> int: ...
    def setFramesPerSecond(self, num: int) -> None: ...
    def framesPerSecond(self) -> int: ...
    def boundsOnElement(self, id: typing.Optional[str]) -> QtCore.QRectF: ...
    def animated(self) -> bool: ...
    @typing.overload
    def setViewBox(self, viewbox: QtCore.QRect) -> None: ...
    @typing.overload
    def setViewBox(self, viewbox: QtCore.QRectF) -> None: ...
    def viewBoxF(self) -> QtCore.QRectF: ...
    def viewBox(self) -> QtCore.QRect: ...
    def elementExists(self, id: typing.Optional[str]) -> bool: ...
    def defaultSize(self) -> QtCore.QSize: ...
    def isValid(self) -> bool: ...
