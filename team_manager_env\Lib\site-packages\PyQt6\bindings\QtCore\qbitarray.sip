// qbitarray.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QBitArray
{
%TypeHeaderCode
#include <qbitarray.h>
%End

%TypeCode
// This is needed by __hash__().
#include <qhash.h>
%End

public:
    QBitArray();
    QBitArray(qsizetype size, bool value = false);
    QBitArray(const QBitArray &other);
    qsizetype size() const;
    qsizetype count() const /__len__/;
    qsizetype count(bool on) const;
    bool isEmpty() const;
    bool isNull() const;
    void resize(qsizetype size);
    void detach();
    bool isDetached() const;
    void clear();
    QBitArray &operator&=(const QBitArray &);
    QBitArray &operator|=(const QBitArray &);
    QBitArray &operator^=(const QBitArray &);
%If (- Qt_6_7_0)
    // This was changed to be a global operator in Qt v6.7 but this isn't supported by SIP v6.8.
    QBitArray operator~() const;
%End
%If (- Qt_6_8_0)
    bool operator==(const QBitArray &a) const;
%End
%If (- Qt_6_8_0)
    bool operator!=(const QBitArray &a) const;
%End
    void fill(bool val, qsizetype first, qsizetype last);
    bool fill(bool val, qsizetype size = -1);
    void truncate(qsizetype pos);
    bool testBit(qsizetype i) const;
    void setBit(qsizetype i);
    void setBit(qsizetype i, bool val);
    void clearBit(qsizetype i);
    bool toggleBit(qsizetype i);
    bool operator[](qsizetype i) const;
    bool at(qsizetype i) const;
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    void swap(QBitArray &other /Constrained/);
    SIP_PYOBJECT bits() const /TypeHint="bytes"/;
%MethodCode
        return PyBytes_FromStringAndSize(sipCpp->bits(), (sipCpp->size() + 7) / 8);
%End

    static QBitArray fromBits(const char *data /Encoding="None"/, Py_ssize_t len) [QBitArray (const char *data, qsizetype len)];
    quint32 toUInt32(QSysInfo::Endian endianness, bool *ok = 0) const;
};

QBitArray operator&(const QBitArray &, const QBitArray &);
QBitArray operator|(const QBitArray &, const QBitArray &);
QBitArray operator^(const QBitArray &, const QBitArray &);
QDataStream &operator<<(QDataStream &, const QBitArray &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QBitArray & /Constrained/) /ReleaseGIL/;
%If (Qt_6_8_0 -)
bool operator!=(const QBitArray &lhs, const QBitArray &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QBitArray &lhs, const QBitArray &rhs);
%End
