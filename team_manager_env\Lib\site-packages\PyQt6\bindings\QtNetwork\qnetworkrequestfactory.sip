// qnetworkrequestfactory.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_8_0 -)

class QNetworkRequestFactory
{
%TypeHeaderCode
#include <qnetworkrequestfactory.h>
%End

public:
    QNetworkRequestFactory();
    explicit QNetworkRequestFactory(const QUrl &baseUrl);
    QNetworkRequestFactory(const QNetworkRequestFactory &other);
    ~QNetworkRequestFactory();
    void swap(QNetworkRequestFactory &other /Constrained/);
    QUrl baseUrl() const;
    void setBaseUrl(const QUrl &url);
%If (PyQt_SSL)
    QSslConfiguration sslConfiguration() const;
%End
%If (PyQt_SSL)
    void setSslConfiguration(const QSslConfiguration &configuration);
%End
    QNetworkRequest createRequest() const;
    QNetworkRequest createRequest(const QUrlQuery &query) const;
    QNetworkRequest createRequest(const QString &path) const;
    QNetworkRequest createRequest(const QString &path, const QUrlQuery &query) const;
    void setCommonHeaders(const QHttpHeaders &headers);
    QHttpHeaders commonHeaders() const;
    void clearCommonHeaders();
    QByteArray bearerToken() const;
    void setBearerToken(const QByteArray &token);
    void clearBearerToken();
    QString userName() const;
    void setUserName(const QString &userName);
    void clearUserName();
    QString password() const;
    void setPassword(const QString &password);
    void clearPassword();
    void setTransferTimeout(std::chrono::milliseconds timeout);
    std::chrono::milliseconds transferTimeout() const;
    QUrlQuery queryParameters() const;
    void setQueryParameters(const QUrlQuery &query);
    void clearQueryParameters();
    void setPriority(QNetworkRequest::Priority priority);
    QNetworkRequest::Priority priority() const;
    QVariant attribute(QNetworkRequest::Attribute attribute) const;
    QVariant attribute(QNetworkRequest::Attribute attribute, const QVariant &defaultValue) const;
    void setAttribute(QNetworkRequest::Attribute attribute, const QVariant &value);
    void clearAttribute(QNetworkRequest::Attribute attribute);
    void clearAttributes();
};

%End
