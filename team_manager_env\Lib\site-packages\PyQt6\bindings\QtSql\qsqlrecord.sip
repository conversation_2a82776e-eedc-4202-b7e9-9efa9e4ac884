// qsqlrecord.sip generated by MetaSIP
//
// This file is part of the QtSql Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSqlRecord
{
%TypeHeaderCode
#include <qsqlrecord.h>
%End

public:
    QSqlRecord();
    QSqlRecord(const QSqlRecord &other);
    ~QSqlRecord();
    bool operator==(const QSqlRecord &other) const;
    bool operator!=(const QSqlRecord &other) const;
    QVariant value(int i) const;
%If (Qt_6_8_0 -)
    QVariant value(QAnyStringView name) const;
%End
%If (- Qt_6_8_0)
    QVariant value(const QString &name) const;
%End
    void setValue(int i, const QVariant &val);
%If (Qt_6_8_0 -)
    void setValue(QAnyStringView name, const QVariant &val);
%End
%If (- Qt_6_8_0)
    void setValue(const QString &name, const QVariant &val);
%End
    void setNull(int i);
%If (Qt_6_8_0 -)
    void setNull(QAnyStringView name);
%End
%If (- Qt_6_8_0)
    void setNull(const QString &name);
%End
    bool isNull(int i) const;
%If (Qt_6_8_0 -)
    bool isNull(QAnyStringView name) const;
%End
%If (- Qt_6_8_0)
    bool isNull(const QString &name) const;
%End
%If (Qt_6_8_0 -)
    int indexOf(QAnyStringView name) const;
%End
%If (- Qt_6_8_0)
    int indexOf(const QString &name) const;
%End
    QString fieldName(int i) const;
    QSqlField field(int i) const;
%If (Qt_6_8_0 -)
    QSqlField field(QAnyStringView name) const;
%End
%If (- Qt_6_8_0)
    QSqlField field(const QString &name) const;
%End
    bool isGenerated(int i) const;
%If (Qt_6_8_0 -)
    bool isGenerated(QAnyStringView name) const;
%End
%If (- Qt_6_8_0)
    bool isGenerated(const QString &name) const;
%End
%If (Qt_6_8_0 -)
    void setGenerated(QAnyStringView name, bool generated);
%End
%If (- Qt_6_8_0)
    void setGenerated(const QString &name, bool generated);
%End
    void setGenerated(int i, bool generated);
    void append(const QSqlField &field);
    void replace(int pos, const QSqlField &field);
    void insert(int pos, const QSqlField &field);
    void remove(int pos);
    bool isEmpty() const;
%If (Qt_6_8_0 -)
    bool contains(QAnyStringView name) const;
%End
%If (- Qt_6_8_0)
    bool contains(const QString &name) const;
%End
    void clear();
    void clearValues();
    int count() const /__len__/;
    QSqlRecord keyValues(const QSqlRecord &keyFields) const;
%If (Qt_6_6_0 -)
    void swap(QSqlRecord &other /Constrained/);
%End
};
