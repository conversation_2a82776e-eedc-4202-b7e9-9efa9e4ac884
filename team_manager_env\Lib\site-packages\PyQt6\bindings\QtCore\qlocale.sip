// qlocale.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLocale
{
%TypeHeaderCode
#include <qlocale.h>
%End

public:
    enum Language : ushort
    {
        C,
        Abkhazian,
        Afan,
        Afar,
        Afrikaans,
        Albanian,
        Amharic,
        Arabic,
        Armenian,
        Assamese,
        Aymara,
        Azerbaijani,
        Bashkir,
        Basque,
        Bengali,
        Bhutani,
        Bislama,
        Breton,
        Bulgarian,
        Burmese,
        Byelorussian,
        Cambodian,
        Catalan,
        Chinese,
        Corsican,
        Croatian,
        Czech,
        Danish,
        Dutch,
        English,
        Esperanto,
        Estonian,
        Faroese,
        Finnish,
        French,
        Frisian,
        Gaelic,
        Galician,
        Georgian,
        German,
        Greek,
        Greenlandic,
        Guarani,
        Gujarati,
        Hausa,
        Hebrew,
        Hindi,
        Hungarian,
        Icelandic,
        Indonesian,
        Interlingua,
        Interlingue,
        Inuktitut,
        Inupiak,
        Irish,
        Italian,
        Japanese,
        Javanese,
        Kannada,
        Kashmiri,
        Kazakh,
        Kinyarwanda,
        Kirghiz,
        Korean,
        Kurdish,
        Kurundi,
        Latin,
        Latvian,
        Lingala,
        Lithuanian,
        Macedonian,
        Malagasy,
        Malay,
        Malayalam,
        Maltese,
        Maori,
        Marathi,
        Mongolian,
        NauruLanguage,
        Nepali,
        Occitan,
        Oriya,
        Pashto,
        Persian,
        Polish,
        Portuguese,
        Punjabi,
        Quechua,
        RhaetoRomance,
        Romanian,
        Russian,
        Samoan,
        Sanskrit,
        Serbian,
        Shona,
        Sindhi,
        Slovak,
        Slovenian,
        Somali,
        Spanish,
        Sundanese,
        Swahili,
        Swedish,
        Tajik,
        Tamil,
        Tatar,
        Telugu,
        Thai,
        Tibetan,
        Tigrinya,
        Tsonga,
        Turkish,
        Turkmen,
        Uigur,
        Ukrainian,
        Urdu,
        Uzbek,
        Vietnamese,
        Volapuk,
        Welsh,
        Wolof,
        Xhosa,
        Yiddish,
        Yoruba,
        Zhuang,
        Zulu,
        Bosnian,
        Divehi,
        Manx,
        Cornish,
        LastLanguage,
        NorwegianBokmal,
        NorwegianNynorsk,
        Akan,
        Konkani,
        Ga,
        Igbo,
        Kamba,
        Syriac,
        Blin,
        Geez,
        Koro,
        Sidamo,
        Atsam,
        Tigre,
        Jju,
        Friulian,
        Venda,
        Ewe,
        Walamo,
        Hawaiian,
        Tyap,
        Chewa,
        Filipino,
        SwissGerman,
        SichuanYi,
        Kpelle,
        LowGerman,
        SouthNdebele,
        NorthernSotho,
        NorthernSami,
        Taroko,
        Gusii,
        Taita,
        Fulah,
        Kikuyu,
        Samburu,
        Sena,
        NorthNdebele,
        Rombo,
        Tachelhit,
        Kabyle,
        Nyankole,
        Bena,
        Vunjo,
        Bambara,
        Embu,
        Cherokee,
        Morisyen,
        Makonde,
        Langi,
        Ganda,
        Bemba,
        Kabuverdianu,
        Meru,
        Kalenjin,
        Nama,
        Machame,
        Colognian,
        Masai,
        Soga,
        Luyia,
        Asu,
        Teso,
        Saho,
        KoyraChiini,
        Rwa,
        Luo,
        Chiga,
        CentralMoroccoTamazight,
        KoyraboroSenni,
        Shambala,
        AnyLanguage,
        Rundi,
        Bodo,
        Aghem,
        Basaa,
        Zarma,
        Duala,
        JolaFonyi,
        Ewondo,
        Bafia,
        LubaKatanga,
        MakhuwaMeetto,
        Mundang,
        Kwasio,
        Nuer,
        Sakha,
        Sangu,
        Tasawaq,
        Vai,
        Walser,
        Yangben,
        Oromo,
        Dzongkha,
        Belarusian,
        Khmer,
        Fijian,
        WesternFrisian,
        Lao,
        Marshallese,
        Romansh,
        Sango,
        Ossetic,
        SouthernSotho,
        Tswana,
        Sinhala,
        Swati,
        Sardinian,
        Tongan,
        Tahitian,
        Nyanja,
        Avaric,
        Chamorro,
        Chechen,
        Church,
        Chuvash,
        Cree,
        Haitian,
        Herero,
        HiriMotu,
        Kanuri,
        Komi,
        Kongo,
        Kwanyama,
        Limburgish,
        Luxembourgish,
        Navaho,
        Ndonga,
        Ojibwa,
        Pali,
        Walloon,
        Avestan,
        Asturian,
        Ngomba,
        Kako,
        Meta,
        Ngiemboon,
        Uighur,
        Aragonese,
        Akkadian,
        AncientEgyptian,
        AncientGreek,
        Aramaic,
        Balinese,
        Bamun,
        BatakToba,
        Buginese,
        Chakma,
        Coptic,
        Dogri,
        Gothic,
        Ingush,
        Mandingo,
        Manipuri,
        OldIrish,
        OldNorse,
        OldPersian,
        Pahlavi,
        Phoenician,
        Santali,
        Saurashtra,
        TaiDam,
        Ugaritic,
        Akoose,
        Lakota,
        StandardMoroccanTamazight,
        Mapuche,
        CentralKurdish,
        LowerSorbian,
        UpperSorbian,
        Kenyang,
        Mohawk,
        Nko,
        Prussian,
        Kiche,
        SouthernSami,
        LuleSami,
        InariSami,
        SkoltSami,
        Warlpiri,
        Mende,
        Lezghian,
        Maithili,
        AmericanSignLanguage,
        Bhojpuri,
        LiteraryChinese,
        Mazanderani,
        Newari,
        NorthernLuri,
        Palauan,
        Papiamento,
        TokelauLanguage,
        TokPisin,
        TuvaluLanguage,
        Cantonese,
        Osage,
        Ido,
        Lojban,
        Sicilian,
        SouthernKurdish,
        WesternBalochi,
        Cebuano,
        Erzya,
        Chickasaw,
        Muscogee,
        Silesian,
        NigerianPidgin,
        Bangla,
        CentralAtlasTamazight,
        Inupiaq,
        Kalaallisut,
        Kuanyama,
        Kyrgyz,
        Navajo,
        Odia,
        Uyghur,
        Wolaytta,
%If (Qt_6_3_0 -)
        Kaingang,
%End
%If (Qt_6_3_0 -)
        Nheengatu,
%End
%If (Qt_6_5_0 -)
        Haryanvi,
%End
%If (Qt_6_5_0 -)
        NorthernFrisian,
%End
%If (Qt_6_5_0 -)
        Rajasthani,
%End
%If (Qt_6_5_0 -)
        Moksha,
%End
%If (Qt_6_5_0 -)
        TokiPona,
%End
%If (Qt_6_5_0 -)
        Pijin,
%End
%If (Qt_6_5_0 -)
        Obolo,
%End
%If (Qt_6_6_0 -)
        Baluchi,
%End
%If (Qt_6_6_0 -)
        Ligurian,
%End
%If (Qt_6_6_0 -)
        Rohingya,
%End
%If (Qt_6_6_0 -)
        Torwali,
%End
%If (Qt_6_7_0 -)
        Anii,
%End
%If (Qt_6_7_0 -)
        Kangri,
%End
%If (Qt_6_7_0 -)
        Venetian,
%End
%If (Qt_6_8_0 -)
        Kuvi,
%End
%If (Qt_6_9_0 -)
        KaraKalpak,
%End
%If (Qt_6_9_0 -)
        SwampyCree,
%End
    };

    enum Country : ushort
    {
        AnyCountry,
%If (Qt_6_2_0 -)
        AnyTerritory,
%End
        Afghanistan,
        Albania,
        Algeria,
        AmericanSamoa,
        Andorra,
        Angola,
        Anguilla,
        Antarctica,
        AntiguaAndBarbuda,
        Argentina,
        Armenia,
        Aruba,
        Australia,
        Austria,
        Azerbaijan,
        Bahamas,
        Bahrain,
        Bangladesh,
        Barbados,
        Belarus,
        Belgium,
        Belize,
        Benin,
        Bermuda,
        Bhutan,
        Bolivia,
        BosniaAndHerzegowina,
        Botswana,
        BouvetIsland,
        Brazil,
        BritishIndianOceanTerritory,
        Bulgaria,
        BurkinaFaso,
        Burundi,
        Cambodia,
        Cameroon,
        Canada,
        CapeVerde,
        CaymanIslands,
        CentralAfricanRepublic,
        Chad,
        Chile,
        China,
        ChristmasIsland,
        CocosIslands,
        Colombia,
        Comoros,
        DemocraticRepublicOfCongo,
        PeoplesRepublicOfCongo,
        CookIslands,
        CostaRica,
        IvoryCoast,
        Croatia,
        Cuba,
        Cyprus,
        CzechRepublic,
        Denmark,
        Djibouti,
        Dominica,
        DominicanRepublic,
        EastTimor,
        Ecuador,
        Egypt,
        ElSalvador,
        EquatorialGuinea,
        Eritrea,
        Estonia,
        Ethiopia,
        FalklandIslands,
        FaroeIslands,
        Finland,
        France,
        FrenchGuiana,
        FrenchPolynesia,
        FrenchSouthernTerritories,
        Gabon,
        Gambia,
        Georgia,
        Germany,
        Ghana,
        Gibraltar,
        Greece,
        Greenland,
        Grenada,
        Guadeloupe,
        Guam,
        Guatemala,
        Guinea,
        GuineaBissau,
        Guyana,
        Haiti,
        HeardAndMcDonaldIslands,
        Honduras,
        HongKong,
        Hungary,
        Iceland,
        India,
        Indonesia,
        Iran,
        Iraq,
        Ireland,
        Israel,
        Italy,
        Jamaica,
        Japan,
        Jordan,
        Kazakhstan,
        Kenya,
        Kiribati,
        DemocraticRepublicOfKorea,
        RepublicOfKorea,
        Kuwait,
        Kyrgyzstan,
        Latvia,
        Lebanon,
        Lesotho,
        Liberia,
        Liechtenstein,
        Lithuania,
        Luxembourg,
        Macau,
        Macedonia,
        Madagascar,
        Malawi,
        Malaysia,
        Maldives,
        Mali,
        Malta,
        MarshallIslands,
        Martinique,
        Mauritania,
        Mauritius,
        Mayotte,
        Mexico,
        Micronesia,
        Moldova,
        Monaco,
        Mongolia,
        Montserrat,
        Morocco,
        Mozambique,
        Myanmar,
        Namibia,
        NauruCountry,
        Nepal,
        Netherlands,
        NewCaledonia,
        NewZealand,
        Nicaragua,
        Niger,
        Nigeria,
        Niue,
        NorfolkIsland,
        NorthernMarianaIslands,
        Norway,
        Oman,
        Pakistan,
        Palau,
        Panama,
        PapuaNewGuinea,
        Paraguay,
        Peru,
        Philippines,
        Pitcairn,
        Poland,
        Portugal,
        PuertoRico,
        Qatar,
        Reunion,
        Romania,
        RussianFederation,
        Rwanda,
        SaintKittsAndNevis,
        Samoa,
        SanMarino,
        SaoTomeAndPrincipe,
        SaudiArabia,
        Senegal,
        Seychelles,
        SierraLeone,
        Singapore,
        Slovakia,
        Slovenia,
        SolomonIslands,
        Somalia,
        SouthAfrica,
        SouthGeorgiaAndTheSouthSandwichIslands,
        Spain,
        SriLanka,
        Sudan,
        Suriname,
        SvalbardAndJanMayenIslands,
        Swaziland,
        Sweden,
        Switzerland,
        SyrianArabRepublic,
        Taiwan,
        Tajikistan,
        Tanzania,
        Thailand,
        Togo,
        TrinidadAndTobago,
        Tunisia,
        Turkey,
        Turkmenistan,
        TurksAndCaicosIslands,
        Uganda,
        Ukraine,
        UnitedArabEmirates,
        UnitedKingdom,
        UnitedStates,
        UnitedStatesMinorOutlyingIslands,
        Uruguay,
        Uzbekistan,
        Vanuatu,
        VaticanCityState,
        Venezuela,
        BritishVirginIslands,
        WallisAndFutunaIslands,
        WesternSahara,
        Yemen,
        Zambia,
        Zimbabwe,
        Montenegro,
        Serbia,
        SaintBarthelemy,
        SaintMartin,
        LatinAmericaAndTheCaribbean,
        LastCountry,
        Brunei,
        CongoKinshasa,
        CongoBrazzaville,
        Fiji,
        Guernsey,
        NorthKorea,
        SouthKorea,
        Laos,
        Libya,
        CuraSao,
        PalestinianTerritories,
        Russia,
        SaintLucia,
        SaintVincentAndTheGrenadines,
        SaintHelena,
        SaintPierreAndMiquelon,
        Syria,
        Tonga,
        Vietnam,
        UnitedStatesVirginIslands,
        CanaryIslands,
        ClippertonIsland,
        AscensionIsland,
        AlandIslands,
        DiegoGarcia,
        CeutaAndMelilla,
        IsleOfMan,
        Jersey,
        TristanDaCunha,
        SouthSudan,
        Bonaire,
        SintMaarten,
        Kosovo,
        TokelauCountry,
        TuvaluCountry,
        EuropeanUnion,
        OutlyingOceania,
        LatinAmerica,
        World,
        Europe,
        BosniaAndHerzegovina,
        CaribbeanNetherlands,
        Curacao,
        Czechia,
        Eswatini,
        Macao,
        SaintVincentAndGrenadines,
        SouthGeorgiaAndSouthSandwichIslands,
        SvalbardAndJanMayen,
        TimorLeste,
        UnitedStatesOutlyingIslands,
        VaticanCity,
        WallisAndFutuna,
%If (Qt_6_2_0 -)
        NauruTerritory,
%End
%If (Qt_6_2_0 -)
        TokelauTerritory,
%End
%If (Qt_6_2_0 -)
        TuvaluTerritory,
%End
    };

    enum NumberOption /BaseType=Flag/
    {
        OmitGroupSeparator,
        RejectGroupSeparator,
        DefaultNumberOptions,
        OmitLeadingZeroInExponent,
        RejectLeadingZeroInExponent,
        IncludeTrailingZeroesAfterDot,
        RejectTrailingZeroesAfterDot,
    };

%If (Qt_6_7_0 -)

    enum class TagSeparator : char
    {
        Dash,
        Underscore,
    };

%End
    typedef QFlags<QLocale::NumberOption> NumberOptions;
    QLocale();
    explicit QLocale(const QString &name);
    QLocale(QLocale::Language language, QLocale::Country country = QLocale::AnyCountry);
    QLocale(QLocale::Language language, QLocale::Script script, QLocale::Country country);
    QLocale(const QLocale &other);
    ~QLocale();
    QLocale::Language language() const;
    QLocale::Country country() const;
%If (Qt_6_7_0 -)
    QString name(QLocale::TagSeparator separator = QLocale::TagSeparator::Underscore) const;
%End
%If (- Qt_6_7_0)
    QString name() const;
%End
    short toShort(const QString &s, bool *ok = 0) const;
    ushort toUShort(const QString &s, bool *ok = 0) const;
    int toInt(const QString &s, bool *ok = 0) const;
    uint toUInt(const QString &s, bool *ok = 0) const;
    qlonglong toLongLong(const QString &s, bool *ok = 0) const;
    qulonglong toULongLong(const QString &s, bool *ok = 0) const;
    float toFloat(const QString &s, bool *ok = 0) const;
    double toDouble(const QString &s, bool *ok = 0) const;
    QString toString(QDate date, QLocale::FormatType format, QCalendar cal) const;
    QString toString(QDate date, QStringView formatStr, QCalendar cal) const;
    QString toString(QDate date, QLocale::FormatType format = QLocale::LongFormat) const;
    QString toString(QDate date, QStringView formatStr) const;
    QString toString(QTime time, QLocale::FormatType format = QLocale::LongFormat) const;
    QString toString(QTime time, QStringView formatStr) const;
    QString toString(double i /Constrained/, char format = 'g', int precision = 6) const;
    static QString languageToString(QLocale::Language language);
    static QString countryToString(QLocale::Country country);
    static void setDefault(const QLocale &locale);
    static QLocale c();
    static QLocale system();

    enum FormatType
    {
        LongFormat,
        ShortFormat,
        NarrowFormat,
    };

    QString toString(const QDateTime &dateTime, QLocale::FormatType format, QCalendar cal) const;
    QString toString(const QDateTime &dateTime, const QString &format) const;
    QString toString(const QDateTime &dateTime, const QString &formatStr, QCalendar cal) const;
%MethodCode
        // QStringView has issues being implemented as a mapped type.
        sipRes = new QString(sipCpp->toString(*a0, QStringView(*a1), *a2));
%End

    QString toString(const QDateTime &dateTime, QLocale::FormatType format = QLocale::LongFormat) const;
    QString dateFormat(QLocale::FormatType format = QLocale::LongFormat) const;
    QString timeFormat(QLocale::FormatType format = QLocale::LongFormat) const;
    QString dateTimeFormat(QLocale::FormatType format = QLocale::LongFormat) const;
%If (Qt_6_7_0 -)
    QDate toDate(const QString &string, const QString &format, int baseYear = QLocale::DefaultTwoDigitBaseYear) const;
%End
%If (- Qt_6_7_0)
    QDate toDate(const QString &string, const QString &format) const;
%End
%If (Qt_6_7_0 -)
    QDate toDate(const QString &string, const QString &format, QCalendar cal, int baseYear = QLocale::DefaultTwoDigitBaseYear) const;
%End
%If (- Qt_6_7_0)
    QDate toDate(const QString &string, const QString &format, QCalendar cal) const;
%End
%If (Qt_6_7_0 -)
    QDate toDate(const QString &string, QLocale::FormatType format, QCalendar cal, int baseYear = QLocale::DefaultTwoDigitBaseYear) const;
%End
%If (- Qt_6_7_0)
    QDate toDate(const QString &string, QLocale::FormatType format, QCalendar cal) const;
%End
%If (Qt_6_7_0 -)
    QDate toDate(const QString &string, QLocale::FormatType = QLocale::LongFormat, int baseYear = QLocale::DefaultTwoDigitBaseYear) const;
%End
%If (- Qt_6_7_0)
    QDate toDate(const QString &string, QLocale::FormatType format = QLocale::LongFormat) const;
%End
    QTime toTime(const QString &string, QLocale::FormatType format = QLocale::LongFormat) const;
    QTime toTime(const QString &string, const QString &format) const;
%If (Qt_6_7_0 -)
    QDateTime toDateTime(const QString &string, const QString &format, int baseYear = QLocale::DefaultTwoDigitBaseYear) const;
%End
%If (- Qt_6_7_0)
    QDateTime toDateTime(const QString &string, const QString &format) const;
%End
%If (Qt_6_7_0 -)
    QDateTime toDateTime(const QString &string, const QString &format, QCalendar cal, int baseYear = QLocale::DefaultTwoDigitBaseYear) const;
%End
%If (- Qt_6_7_0)
    QDateTime toDateTime(const QString &string, const QString &format, QCalendar cal) const;
%End
%If (Qt_6_7_0 -)
    QDateTime toDateTime(const QString &string, QLocale::FormatType format, QCalendar cal, int baseYear = QLocale::DefaultTwoDigitBaseYear) const;
%End
%If (- Qt_6_7_0)
    QDateTime toDateTime(const QString &string, QLocale::FormatType format, QCalendar cal) const;
%End
%If (Qt_6_7_0 -)
    QDateTime toDateTime(const QString &string, QLocale::FormatType format = QLocale::LongFormat, int baseYear = QLocale::DefaultTwoDigitBaseYear) const;
%End
%If (- Qt_6_7_0)
    QDateTime toDateTime(const QString &string, QLocale::FormatType format = QLocale::LongFormat) const;
%End
    QString decimalPoint() const;
    QString groupSeparator() const;
    QString percent() const;
    QString zeroDigit() const;
    QString negativeSign() const;
    QString exponential() const;
    QString monthName(int, QLocale::FormatType format = QLocale::LongFormat) const;
    QString dayName(int, QLocale::FormatType format = QLocale::LongFormat) const;
    void setNumberOptions(QLocale::NumberOptions options);
    QLocale::NumberOptions numberOptions() const;

    enum MeasurementSystem
    {
        MetricSystem,
        ImperialSystem,
        ImperialUSSystem,
        ImperialUKSystem,
    };

    QLocale::MeasurementSystem measurementSystem() const;
    QString positiveSign() const;
    QString standaloneMonthName(int, QLocale::FormatType format = QLocale::LongFormat) const;
    QString standaloneDayName(int, QLocale::FormatType format = QLocale::LongFormat) const;
    QString amText() const;
    QString pmText() const;
    Qt::LayoutDirection textDirection() const;

    enum Script : ushort
    {
        AnyScript,
        ArabicScript,
        CyrillicScript,
        DeseretScript,
        GurmukhiScript,
        SimplifiedHanScript,
        TraditionalHanScript,
        LatinScript,
        MongolianScript,
        TifinaghScript,
        SimplifiedChineseScript,
        TraditionalChineseScript,
        ArmenianScript,
        BengaliScript,
        CherokeeScript,
        DevanagariScript,
        EthiopicScript,
        GeorgianScript,
        GreekScript,
        GujaratiScript,
        HebrewScript,
        JapaneseScript,
        KhmerScript,
        KannadaScript,
        KoreanScript,
        LaoScript,
        MalayalamScript,
        MyanmarScript,
        OriyaScript,
        TamilScript,
        TeluguScript,
        ThaanaScript,
        ThaiScript,
        TibetanScript,
        SinhalaScript,
        SyriacScript,
        YiScript,
        VaiScript,
        AvestanScript,
        BalineseScript,
        BamumScript,
        BatakScript,
        BopomofoScript,
        BrahmiScript,
        BugineseScript,
        BuhidScript,
        CanadianAboriginalScript,
        CarianScript,
        ChakmaScript,
        ChamScript,
        CopticScript,
        CypriotScript,
        EgyptianHieroglyphsScript,
        FraserScript,
        GlagoliticScript,
        GothicScript,
        HanScript,
        HangulScript,
        HanunooScript,
        ImperialAramaicScript,
        InscriptionalPahlaviScript,
        InscriptionalParthianScript,
        JavaneseScript,
        KaithiScript,
        KatakanaScript,
        KayahLiScript,
        KharoshthiScript,
        LannaScript,
        LepchaScript,
        LimbuScript,
        LinearBScript,
        LycianScript,
        LydianScript,
        MandaeanScript,
        MeiteiMayekScript,
        MeroiticScript,
        MeroiticCursiveScript,
        NkoScript,
        NewTaiLueScript,
        OghamScript,
        OlChikiScript,
        OldItalicScript,
        OldPersianScript,
        OldSouthArabianScript,
        OrkhonScript,
        OsmanyaScript,
        PhagsPaScript,
        PhoenicianScript,
        PollardPhoneticScript,
        RejangScript,
        RunicScript,
        SamaritanScript,
        SaurashtraScript,
        SharadaScript,
        ShavianScript,
        SoraSompengScript,
        CuneiformScript,
        SundaneseScript,
        SylotiNagriScript,
        TagalogScript,
        TagbanwaScript,
        TaiLeScript,
        TaiVietScript,
        TakriScript,
        UgariticScript,
        BrailleScript,
        HiraganaScript,
        CaucasianAlbanianScript,
        BassaVahScript,
        DuployanScript,
        ElbasanScript,
        GranthaScript,
        PahawhHmongScript,
        KhojkiScript,
        LinearAScript,
        MahajaniScript,
        ManichaeanScript,
        MendeKikakuiScript,
        ModiScript,
        MroScript,
        OldNorthArabianScript,
        NabataeanScript,
        PalmyreneScript,
        PauCinHauScript,
        OldPermicScript,
        PsalterPahlaviScript,
        SiddhamScript,
        KhudawadiScript,
        TirhutaScript,
        VarangKshitiScript,
        AhomScript,
        AnatolianHieroglyphsScript,
        HatranScript,
        MultaniScript,
        OldHungarianScript,
        SignWritingScript,
        AdlamScript,
        BhaiksukiScript,
        MarchenScript,
        NewaScript,
        OsageScript,
        TangutScript,
        HanWithBopomofoScript,
        JamoScript,
        BanglaScript,
        MendeScript,
        OdiaScript,
%If (Qt_6_6_0 -)
        HanifiScript,
%End
    };

    enum CurrencySymbolFormat
    {
        CurrencyIsoCode,
        CurrencySymbol,
        CurrencyDisplayName,
    };

    QLocale::Script script() const;
%If (Qt_6_7_0 -)
    QString bcp47Name(QLocale::TagSeparator separator = QLocale::TagSeparator::Dash) const;
%End
%If (- Qt_6_7_0)
    QString bcp47Name() const;
%End
    QString nativeLanguageName() const;
    QString nativeCountryName() const;
    Qt::DayOfWeek firstDayOfWeek() const;
    QList<Qt::DayOfWeek> weekdays() const;
    QString toUpper(const QString &str) const;
    QString toLower(const QString &str) const;
    QString currencySymbol(QLocale::CurrencySymbolFormat format = QLocale::CurrencySymbol) const;
    QString toCurrencyString(double, const QString &symbol = QString(), int precision = -1) const;
%If (Qt_6_7_0 -)
    QStringList uiLanguages(QLocale::TagSeparator separator = QLocale::TagSeparator::Dash) const;
%End
%If (- Qt_6_7_0)
    QStringList uiLanguages() const;
%End
    static QString scriptToString(QLocale::Script script);
%If (Qt_6_2_0 -)
    static QList<QLocale> matchingLocales(QLocale::Language language, QLocale::Script script, QLocale::Territory territory);
%End
%If (- Qt_6_2_0)
    static QList<QLocale> matchingLocales(QLocale::Language language, QLocale::Script script, QLocale::Country country);
%End

    enum QuotationStyle
    {
        StandardQuotation,
        AlternateQuotation,
    };

    QString quoteString(QStringView str, QLocale::QuotationStyle style = QLocale::StandardQuotation) const;
    QString createSeparatedList(const QStringList &list) const;
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    enum FloatingPointPrecisionOption /BaseType=IntEnum/
    {
        FloatingPointShortest,
    };

    void swap(QLocale &other /Constrained/);
    QString toString(SIP_PYOBJECT i /TypeHint="int"/) const;
%MethodCode
        // Convert a Python int avoiding overflow as much as possible.
        
        static PyObject *zero = 0;
        if (!zero)
            zero = PyLong_FromLong(0);
        
        int rc = PyObject_RichCompareBool(a0, zero, Py_LT);
        
        PyErr_Clear();
        
        if (rc < 0)
        {
            sipError = sipBadCallableArg(0, a0);
        }
        else if (rc)
        {
            long long value = PyLong_AsLongLong(a0);
        
            if (PyErr_Occurred() && !PyErr_ExceptionMatches(PyExc_OverflowError))
            {
                sipError = sipBadCallableArg(0, a0);
            }
            else
            {
                sipRes = new QString(sipCpp->toString(value));
            }
        }
        else
        {
            unsigned long long value = PyLong_AsUnsignedLongLongMask(a0);
        
            if (PyErr_Occurred() && !PyErr_ExceptionMatches(PyExc_OverflowError))
            {
                sipError = sipBadCallableArg(0, a0);
            }
            else
            {
                sipRes = new QString(sipCpp->toString(value));
            }
        }
%End

    QString toCurrencyString(SIP_PYOBJECT value /TypeHint="int"/, const QString &symbol = QString()) const;
%MethodCode
        // Convert a Python int avoiding overflow as much as possible.
        
        static PyObject *zero = 0;
        if (!zero)
            zero = PyLong_FromLong(0);
        
        int rc = PyObject_RichCompareBool(a0, zero, Py_LT);
        
        PyErr_Clear();
        
        if (rc < 0)
        {
            sipError = sipBadCallableArg(0, a0);
        }
        else if (rc)
        {
            long long value = PyLong_AsLongLong(a0);
        
            if (PyErr_Occurred() && !PyErr_ExceptionMatches(PyExc_OverflowError))
            {
                sipError = sipBadCallableArg(0, a0);
            }
            else
            {
                sipRes = new QString(sipCpp->toCurrencyString(value, *a1));
            }
        }
        else
        {
            unsigned long long value = PyLong_AsUnsignedLongLongMask(a0);
        
            if (PyErr_Occurred() && !PyErr_ExceptionMatches(PyExc_OverflowError))
            {
                sipError = sipBadCallableArg(0, a0);
            }
            else
            {
                sipRes = new QString(sipCpp->toCurrencyString(value, *a1));
            }
        }
%End

    enum DataSizeFormat /BaseType=Flag/
    {
        DataSizeIecFormat,
        DataSizeTraditionalFormat,
        DataSizeSIFormat,
    };

    typedef QFlags<QLocale::DataSizeFormat> DataSizeFormats;
    QString formattedDataSize(qint64 bytes, int precision = 2, QLocale::DataSizeFormats format = QLocale::DataSizeIecFormat) const;
    long toLong(const QString &s, bool *ok = 0) const;
    ulong toULong(const QString &s, bool *ok = 0) const;
    QLocale collation() const;
%If (Qt_6_3_0 -)
    static QString languageToCode(QLocale::Language language, QLocale::LanguageCodeTypes codeTypes = QLocale::AnyLanguageCode);
%End
%If (Qt_6_1_0 - Qt_6_3_0)
    static QString languageToCode(QLocale::Language language);
%End
%If (Qt_6_3_0 -)
    static QLocale::Language codeToLanguage(QStringView languageCode, QLocale::LanguageCodeTypes codeTypes = QLocale::AnyLanguageCode);
%End
%If (Qt_6_1_0 - Qt_6_3_0)
    static QLocale::Language codeToLanguage(QStringView languageCode);
%End
%If (Qt_6_1_0 -)
    static QString countryToCode(QLocale::Country country);
%End
%If (Qt_6_1_0 -)
    static QLocale::Country codeToCountry(QStringView countryCode);
%End
%If (Qt_6_1_0 -)
    static QString scriptToCode(QLocale::Script script);
%End
%If (Qt_6_1_0 -)
    static QLocale::Script codeToScript(QStringView scriptCode);
%End
%If (Qt_6_2_0 -)
    typedef QLocale::Country Territory;
%End
%If (Qt_6_2_0 -)
    QLocale::Territory territory() const;
%End
%If (Qt_6_2_0 -)
    QString nativeTerritoryName() const;
%End
%If (Qt_6_2_0 -)
    static QString territoryToCode(QLocale::Territory territory);
%End
%If (Qt_6_2_0 -)
    static QLocale::Territory codeToTerritory(QStringView territoryCode);
%End
%If (Qt_6_2_0 -)
    static QString territoryToString(QLocale::Territory territory);
%End
%If (Qt_6_3_0 -)

    enum LanguageCodeType /BaseType=IntFlag/
    {
        ISO639Part1,
        ISO639Part2B,
        ISO639Part2T,
        ISO639Part3,
        LegacyLanguageCode,
        ISO639Part2,
        ISO639Alpha2,
        ISO639Alpha3,
        ISO639,
        AnyLanguageCode,
    };

%End
%If (Qt_6_3_0 -)
    typedef QFlags<QLocale::LanguageCodeType> LanguageCodeTypes;
%End
%If (Qt_6_7_0 -)
    static const int DefaultTwoDigitBaseYear;
%End
};

QDataStream &operator<<(QDataStream &, const QLocale &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QLocale & /Constrained/) /ReleaseGIL/;
%If (Qt_6_8_0 -)
bool operator==(const QLocale &lhs, const QLocale::Language &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QLocale::Language &lhs, const QLocale &rhs);
%End
bool operator==(const QLocale &lhs, const QLocale &rhs);
%If (Qt_6_8_0 -)
bool operator!=(const QLocale &lhs, const QLocale::Language &rhs);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QLocale::Language &lhs, const QLocale &rhs);
%End
bool operator!=(const QLocale &lhs, const QLocale &rhs);
