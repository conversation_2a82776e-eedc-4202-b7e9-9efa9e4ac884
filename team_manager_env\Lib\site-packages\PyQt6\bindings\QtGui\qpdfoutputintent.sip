// qpdfoutputintent.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_8_0 -)

class QPdfOutputIntent
{
%TypeHeaderCode
#include <qpdfoutputintent.h>
%End

public:
    QPdfOutputIntent();
    QPdfOutputIntent(const QPdfOutputIntent &other);
    ~QPdfOutputIntent();
    void swap(QPdfOutputIntent &other);
    QString outputConditionIdentifier() const;
    void setOutputConditionIdentifier(const QString &identifier);
    QString outputCondition() const;
    void setOutputCondition(const QString &condition);
    QUrl registryName() const;
    void setRegistryName(const QUrl &name);
    QColorSpace outputProfile() const;
    void setOutputProfile(const QColorSpace &profile);
};

%End
