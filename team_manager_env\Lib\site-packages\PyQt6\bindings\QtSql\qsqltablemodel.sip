// qsqltablemodel.sip generated by MetaSIP
//
// This file is part of the QtSql Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSqlTableModel : public QSqlQueryModel
{
%TypeHeaderCode
#include <qsqltablemodel.h>
%End

public:
    enum EditStrategy
    {
        OnFieldChange,
        OnRowChange,
        OnManualSubmit,
    };

    QSqlTableModel(QObject *parent /TransferThis/ = 0, const QSqlDatabase &db = QSqlDatabase());
    virtual ~QSqlTableModel();
    virtual bool select();
    virtual void setTable(const QString &tableName);
    QString tableName() const;
    virtual Qt::ItemFlags flags(const QModelIndex &index) const;
    virtual QVariant data(const QModelIndex &idx, int role = Qt::DisplayRole) const;
    virtual bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole);
    virtual QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const;
    bool isDirty(const QModelIndex &index) const;
    bool isDirty() const;
    virtual void clear();
    virtual void setEditStrategy(QSqlTableModel::EditStrategy strategy);
    QSqlTableModel::EditStrategy editStrategy() const;
    QSqlIndex primaryKey() const;
    QSqlDatabase database() const;
    int fieldIndex(const QString &fieldName) const;
    virtual void sort(int column, Qt::SortOrder order);
    virtual void setSort(int column, Qt::SortOrder order);
    QString filter() const;
    virtual void setFilter(const QString &filter);
    virtual int rowCount(const QModelIndex &parent = QModelIndex()) const;
    virtual bool removeColumns(int column, int count, const QModelIndex &parent = QModelIndex());
    virtual bool removeRows(int row, int count, const QModelIndex &parent = QModelIndex());
    virtual bool insertRows(int row, int count, const QModelIndex &parent = QModelIndex());
    bool insertRecord(int row, const QSqlRecord &record);
    bool setRecord(int row, const QSqlRecord &record);
    virtual void revertRow(int row);

public slots:
    virtual bool submit();
    virtual void revert();
    bool submitAll();
    void revertAll();

signals:
    void primeInsert(int row, QSqlRecord &record);
    void beforeInsert(QSqlRecord &record);
    void beforeUpdate(int row, QSqlRecord &record);
    void beforeDelete(int row);

protected:
    virtual bool updateRowInTable(int row, const QSqlRecord &values);
    virtual bool insertRowIntoTable(const QSqlRecord &values);
    virtual bool deleteRowFromTable(int row);
    virtual QString orderByClause() const;
    virtual QString selectStatement() const;
    void setPrimaryKey(const QSqlIndex &key);
%If (- Qt_6_5_0)
    void setQuery(const QSqlQuery &query);
%End
    virtual QModelIndex indexInQuery(const QModelIndex &item) const;
    QSqlRecord primaryValues(int row) const;

public:
    virtual bool selectRow(int row);
    QSqlRecord record() const;
    QSqlRecord record(int row) const;
    virtual bool clearItemData(const QModelIndex &index);
};
