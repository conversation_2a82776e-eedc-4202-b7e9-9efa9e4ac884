// qhelpindexwidget.sip generated by MetaSIP
//
// This file is part of the QtHelp Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHelpIndexModel : public QStringListModel /NoDefaultCtors/
{
%TypeHeaderCode
#include <qhelpindexwidget.h>
%End

public:
    QHelpEngineCore *helpEngine() const;
    void createIndex(const QString &customFilterName);
%If (Qt_6_8_0 -)
    void createIndexForCurrentFilter();
%End
    QModelIndex filter(const QString &filter, const QString &wildcard = QString());
    bool isCreatingIndex() const;

signals:
    void indexCreationStarted();
    void indexCreated();

private:
    virtual ~QHelpIndexModel();
};

class QHelpIndexWidget : public QListView
{
%TypeHeaderCode
#include <qhelpindexwidget.h>
%End

public slots:
    void filterIndices(const QString &filter, const QString &wildcard = QString());
    void activateCurrentItem();

signals:
    void documentActivated(const QHelpLink &document, const QString &keyword);
    void documentsActivated(const QList<QHelpLink> &documents, const QString &keyword);

private:
    QHelpIndexWidget();
};
