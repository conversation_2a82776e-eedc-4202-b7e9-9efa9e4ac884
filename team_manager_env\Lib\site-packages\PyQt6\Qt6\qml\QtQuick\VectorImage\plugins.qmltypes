import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickvectorimage_p.h"
        name: "QQuickVectorImage"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.VectorImage/VectorImage 6.0",
            "QtQuick.VectorImage/VectorImage 6.3",
            "QtQuick.VectorImage/VectorImage 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1539, 1543]
        Enum {
            name: "FillMode"
            values: [
                "NoResize",
                "PreserveAspectFit",
                "PreserveAspectCrop",
                "Stretch"
            ]
        }
        Enum {
            name: "RendererType"
            values: ["GeometryRenderer", "CurveRenderer"]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "fillMode"
            type: "FillMode"
            read: "fillMode"
            write: "setFillMode"
            notify: "fillModeChanged"
            index: 1
        }
        Property {
            name: "preferredRendererType"
            type: "RendererType"
            read: "preferredRendererType"
            write: "setPreferredRendererType"
            notify: "preferredRendererTypeChanged"
            index: 2
        }
        Signal { name: "sourceChanged" }
        Signal { name: "fillModeChanged" }
        Signal { name: "preferredRendererTypeChanged" }
        Method { name: "updateSvgItemScale" }
    }
}
