// qgroupbox.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>ENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGroupBox : public QWidget
{
%TypeHeaderCode
#include <qgroupbox.h>
%End

public:
    explicit QGroupBox(QWidget *parent /TransferThis/ = 0);
    QGroupBox(const QString &title, QWidget *parent /TransferThis/ = 0);
    virtual ~QGroupBox();
    QString title() const;
    void setTitle(const QString &);
    Qt::Alignment alignment() const;
    void setAlignment(int);
    virtual QSize minimumSizeHint() const;
    bool isFlat() const;
    void setFlat(bool b);
    bool isCheckable() const;
    void setCheckable(bool b);
    bool isChecked() const;

public slots:
    void setChecked(bool b);

signals:
    void clicked(bool checked = false);
    void toggled(bool);

protected:
    virtual void initStyleOption(QStyleOptionGroupBox *option) const;
    virtual bool event(QEvent *);
    virtual void childEvent(QChildEvent *);
    virtual void resizeEvent(QResizeEvent *);
    virtual void paintEvent(QPaintEvent *);
    virtual void focusInEvent(QFocusEvent *);
    virtual void changeEvent(QEvent *);
    virtual void mousePressEvent(QMouseEvent *event);
    virtual void mouseMoveEvent(QMouseEvent *event);
    virtual void mouseReleaseEvent(QMouseEvent *event);
};
