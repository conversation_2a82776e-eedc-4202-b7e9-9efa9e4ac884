// qaudioroom.sip generated by MetaSIP
//
// This file is part of the QtSpatialAudio Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_5_0 -)

class QAudioRoom : public QObject
{
%TypeHeaderCode
#include <qaudioroom.h>
%End

public:
    enum Material
    {
        Transparent,
        AcousticCeilingTiles,
        BrickBare,
        BrickPainted,
        ConcreteBlockCoarse,
        ConcreteBlockPainted,
        CurtainHeavy,
        FiberGlassInsulation,
        GlassThin,
        GlassThick,
        Grass,
        LinoleumOnConcrete,
        Marble,
        Metal,
        ParquetOnConcrete,
        PlasterRough,
        PlasterSmooth,
        PlywoodPanel,
        PolishedConcreteOrTile,
        Sheetrock,
        WaterOrIceSurface,
        WoodCeiling,
        WoodPanel,
        UniformMaterial,
    };

    enum Wall
    {
        LeftWall,
        RightWall,
        Floor,
        Ceiling,
        FrontWall,
        BackWall,
    };

    explicit QAudioRoom(QAudioEngine *engine);
    virtual ~QAudioRoom();
    void setPosition(QVector3D pos);
    QVector3D position() const;
    void setDimensions(QVector3D dim);
    QVector3D dimensions() const;
    void setRotation(const QQuaternion &q);
    QQuaternion rotation() const;
    void setWallMaterial(QAudioRoom::Wall wall, QAudioRoom::Material material);
    QAudioRoom::Material wallMaterial(QAudioRoom::Wall wall) const;
    void setReflectionGain(float factor);
    float reflectionGain() const;
    void setReverbGain(float factor);
    float reverbGain() const;
    void setReverbTime(float factor);
    float reverbTime() const;
    void setReverbBrightness(float factor);
    float reverbBrightness() const;

signals:
    void positionChanged();
    void dimensionsChanged();
    void rotationChanged();
    void wallsChanged();
    void reflectionGainChanged();
    void reverbGainChanged();
    void reverbTimeChanged();
    void reverbBrightnessChanged();
};

%End
