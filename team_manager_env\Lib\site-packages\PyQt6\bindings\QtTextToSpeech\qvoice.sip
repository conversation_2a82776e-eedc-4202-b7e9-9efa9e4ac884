// qvoice.sip generated by MetaSIP
//
// This file is part of the QtTextToSpeech Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QVoice
{
%TypeHeaderCode
#include <qvoice.h>
%End

public:
    enum Gender
    {
        Male,
        Female,
        Unknown,
    };

    enum Age
    {
        Child,
        Teenager,
        Adult,
        Senior,
        Other,
    };

    QVoice();
    QVoice(const QVoice &other);
    ~QVoice();
    QString name() const;
    QVoice::Gender gender() const;
    QVoice::Age age() const;
    static QString genderName(QVoice::Gender gender);
    static QString ageName(QVoice::Age age);
    void swap(QVoice &other);
    QLocale locale() const;
%If (Qt_6_6_0 -)
    QLocale::Language language() const;
%End
};

bool operator==(const QVoice &lhs, const QVoice &rhs);
bool operator!=(const QVoice &lhs, const QVoice &rhs);
QDataStream &operator<<(QDataStream &str, const QVoice &voice);
QDataStream &operator>>(QDataStream &str, QVoice &voice /Constrained/);
