// qpdflinkmodel.sip generated by MetaSIP
//
// This file is part of the QtPdf Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_6_0 -)

class QPdfLinkModel : public QAbstractListModel
{
%TypeHeaderCode
#include <qpdflinkmodel.h>
%End

public:
    enum class Role
    {
        Link,
        Rectangle,
        Url,
        Page,
        Location,
        Zoom,
    };

    explicit QPdfLinkModel(QObject *parent /TransferThis/ = 0);
    virtual ~QPdfLinkModel();
    QPdfDocument *document() const;
    virtual QHash<int, QByteArray> roleNames() const;
    virtual int rowCount(const QModelIndex &parent) const;
    virtual QVariant data(const QModelIndex &index, int role) const;
    int page() const;
    QPdfLink linkAt(QPointF point) const;

public slots:
    void setDocument(QPdfDocument *document);
    void setPage(int page);

signals:
    void documentChanged();
    void pageChanged(int page);
};

%End
